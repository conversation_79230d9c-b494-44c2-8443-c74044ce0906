# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# =============================================================================
# PLAXIS Automation Tool Specific
# =============================================================================

# PLAXIS files and outputs
*.p2dx
*.plx
*.g2d
*.plxis
*.tabdat
*.out
*.gout
*.gid

# PLAXIS temporary files
*.tmp
*.bak
*.auto

# Configuration and sensitive data
plaxis_config.dat
security_log.json

# User-specific directories
C:/ELS_RPA/

# Log files and application data
*.log
logs/
.plaxis_automation/

# Generated Excel output files
*_output.xlsx
*_results.xlsx
*AutoRun*.xlsx

# Charts and visualizations
*.png
*.jpg
*.jpeg
*.pdf
charts/
plots/

# Temporary Excel files
~$*.xlsx
~$*.xls
*.tmp

# =============================================================================
# IDE and Editor
# =============================================================================

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets

# Local History for Visual Studio Code
.history/

# Built Visual Studio Code Extensions
*.vsix

# PyCharm
#  JetBrains specific template is maintained in a separate JetBrains.gitignore that can
#  be added to the global gitignore or merged into this file.  For a more nuclear
#  option (not recommended) you can uncomment the following to ignore the entire idea folder.
.idea/

# Sublime Text
*.sublime-project
*.sublime-workspace

# =============================================================================
# Operating System
# =============================================================================

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# Build and Packaging
# =============================================================================

# Nuitka build outputs (for standalone executable)
*.build/
*.dist/
*.onefile-build/

# Windows executable
*.exe
*.msi

# =============================================================================
# Keep Important Files
# =============================================================================

# Keep sample input files but ignore user-generated data
!Sample\ Input/*.xlsx
!Library_Steel/*.xlsx

# Keep configuration template but ignore user config
!Plaxis_Initation.xlsx

# Keep application icon
!AIS.ico

# Keep requirements and documentation
!requirements.txt
!*.md
!.github/