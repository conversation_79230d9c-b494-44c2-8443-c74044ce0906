import numpy as np
import pandas as pd

from plaxis.geometry.utils import cal_plate_prop


def divide_soil_seg(file_paths, excel_inputs, excel_master):
    df_soilprop = excel_inputs.SoilProperties.copy()
    soil_type_prev = 'nth'
    counter = 1
    for i in range(df_soilprop.index.size):

        if pd.isna(df_soilprop.loc[i, 'Bottom Level (mPD)']):
            name = df_soilprop.loc[i, 'Soil Type'] + ' (to -inf mPD)'
        else:
            name = df_soilprop.loc[i, 'Soil Type'] + ' (to ' + str(
                df_soilprop.loc[i, "Bottom Level (mPD)"]) + 'mPD)'

        df_soilprop.loc[i, 'Name'] = name

        soil_type = df_soilprop.loc[i, 'Soil Type']
        if soil_type_prev == soil_type:
            counter += 1
        else:
            counter = 1

        soil_type_prev = soil_type
        count = df_soilprop['Soil Type'].value_counts()[soil_type]

        if count == 1:
            df_soilprop.loc[i, 'Abbreviation'] = soil_type
        else:
            df_soilprop.loc[i, 'Abbreviation'] = f'{soil_type}{counter}'

    excel_master.SoilProperties = df_soilprop

    return file_paths, excel_inputs, excel_master


def format_plate_loc(file_paths, excel_inputs, excel_master):
    df_plate = excel_inputs.Plate
    df_anchor = excel_inputs.Anchor
    df_steel_section = excel_inputs.SteelSectionProp
    df_plate_prop = pd.DataFrame(
        columns=['Plate Section', 'With Grout (Y/N)', 'Material Type', 'Mass (kg/m)', 'Unit weight (kN/m3)',
                 'E (MPa)', 'I (cm4/m)', 'Area A (cm2)', 'Nos. of Strut Layer', 'βD',
                 'Spacing (mm)', 'EA (kN/m)', 'EI (kNm2/m)', 'v'])
    num_strut_layer = df_anchor['Stage'].max()
    for i, r in df_plate.iterrows():
        pile_section = r['Pile Section']
        spacing = r['Pile Spacing (mm)']
        with_grout = r['With Grout (Y/N)']
        if r['With Grout (Y/N)'] == 'Y':
            is_grout = True
        else:
            is_grout = False
        pile_section, material_type, mass, uw, e, i, a, num_strut_layer, beta_d, spacing, ea, ei, v = (
            cal_plate_prop(pile_section, df_steel_section, num_strut_layer, spacing, is_grout))
        if is_grout:
            plate_section = f'{pile_section}_{num_strut_layer}_{spacing}_Grouted'
        else:
            plate_section = f'{pile_section}_{num_strut_layer}_{spacing}'

        if plate_section not in df_plate_prop['Plate Section'].values:
            row = df_plate_prop.index.size
            df_plate_prop.loc[row] = [plate_section, with_grout, material_type, mass, uw, e, i, a,
                                      num_strut_layer, beta_d, spacing, ea, ei, v]

    excel_inputs.PlateProperties = df_plate_prop

    df_plate_loc = pd.DataFrame(
        columns=['Plate Name', 'X (m)', 'Y1 (m)', 'Y2 (m)', 'Plate Section', 'With Grout (Y/N)'])

    for i in range(df_plate.index.size):
        df_plate_loc.loc[i, 'Plate Name'] = f'Plate_{i + 1}'
        df_plate_loc.loc[i, 'X (m)'] = df_plate.loc[i, 'X (mm)'] / 1000
        df_plate_loc.loc[i, 'Y1 (m)'] = df_plate.loc[i, 'Cut-Off Level (mmPD)'] / 1000
        df_plate_loc.loc[i, 'Y2 (m)'] = df_plate.loc[i, 'Founding Level (mmPD)'] / 1000
        pile_section = df_plate.loc[i, 'Pile Section']
        spacing = df_plate.loc[i, 'Pile Spacing (mm)']
        pile_section = pile_section.replace('.', '').replace('/', '')
        df_plate_loc.loc[i, 'Steel Section'] = pile_section
        df_plate_loc.loc[i, 'With Grout (Y/N)'] = df_plate.loc[i, 'With Grout (Y/N)']
        if df_plate_loc.loc[i, 'With Grout (Y/N)'] == 'Y':
            is_grout = True
        else:
            is_grout = False
        if is_grout:
            df_plate_loc.loc[i, 'Plate Section'] = f'{pile_section}_{num_strut_layer}_{spacing}_Grouted'
        else:
            df_plate_loc.loc[i, 'Plate Section'] = f'{pile_section}_{num_strut_layer}_{spacing}'
        df_plate_loc.loc[i, 'Material Type'] = 'E'

        df_plate_loc.loc[i, 'Number of Strut Layer'] = num_strut_layer
        df_plate_loc.loc[i, 'Spacing (mm)'] = spacing

    df_merged = pd.merge(df_plate_loc,
                         df_plate_prop[['Plate Section', 'Unit weight (kN/m3)', 'EA (kN/m)', 'EI (kNm2/m)', 'v']],
                         on='Plate Section')
    excel_master.PlateLoc = df_merged

    with pd.ExcelWriter(file_paths.ExcelProperty) as writer:
        excel_inputs.SteelSectionProp.to_excel(writer, sheet_name='Steel_Sections_Properties', index=False)
        excel_inputs.PlateProperties.to_excel(writer, sheet_name='Plate_Properties', index=False)

    return file_paths, excel_inputs, excel_master


def format_el(file_paths, excel_inputs, excel_master):
    df_anchor = excel_inputs.Anchor.copy()
    df_plate = excel_inputs.Plate.copy()

    x_min = df_plate['X (mm)'].min() / 1000
    x_max = df_plate['X (mm)'].max() / 1000

    # sort the df by the column 'stage'
    df_anchor = df_anchor.sort_values(by='Stage', ascending=True).reset_index(drop=True)

    # get the maximum stage
    max_stage = df_anchor['Stage'].max()

    df_el = pd.DataFrame(columns=['Stage', 'X (m)', 'Y (m)'])

    # loop the range of max stage from 1 to max_stage
    for stage in range(1, max_stage + 1):
        z_min = 999999
        condition = df_anchor['Stage'] == stage
        for idx, row in df_anchor[condition].iterrows():
            # get the X1, Z1, X2, Z2 of the current stage
            z1 = row['Z1 (mm)'] / 1000
            z2 = row['Z2 (mm)'] / 1000
            if z_min >= min(z1, z2):
                z_min = min(z1, z2)

        z_min = z_min - 0.5

        i = df_el.index.size
        df_el.loc[i] = [stage, x_min, z_min]

        i = df_el.index.size
        df_el.loc[i] = [stage, x_max, z_min]

    y_start = excel_inputs.Plate.loc[0, 'Final Excavation Level (mmPD)'] / 1000
    y_end = excel_inputs.Plate.loc[1, 'Final Excavation Level (mmPD)'] / 1000

    i = df_el.index.size
    df_el.loc[i] = ['Final', x_min, y_start]

    i = df_el.index.size
    df_el.loc[i] = ['Final', x_max, y_end]

    excel_master.EL = df_el
    return file_paths, excel_inputs, excel_master


def section_to_borehole(file_paths, excel_inputs, excel_master):
    if not excel_inputs.GeoSection.empty:
        df_geo = excel_inputs.GeoSection.sort_values(by=['X (mm)', 'Z (mm)'], ascending=[True, False]).reset_index(
            drop=True)
        df_vbh = pd.DataFrame(columns=['Borehole', 'Soil', 'Top Level (mPD)', 'Bottom Level (mPD)'])
        df_bh = excel_inputs.Borehole

        vbh_prev = None
        soil_prev = None
        z_prev = 0

        for idx, record in df_geo.iterrows():
            vbh = record['VBH Mark']
            soil = record['Soil Type']
            z = record['Z (mm)'] / 1000

            if vbh != vbh_prev:
                vbh_prev = vbh
                z_prev = z
                soil_prev = soil
            else:
                row = df_vbh.index.size
                df_vbh.loc[row] = [vbh, soil_prev, z_prev, z]
                z_prev = z
                soil_prev = soil

        excel_inputs.Borehole = pd.concat([df_bh, df_vbh]).reset_index(drop=True)

    return file_paths, excel_inputs, excel_master


def format_ground_profile(file_paths, excel_inputs, excel_master):
    excel_master.SoilProperties['Bottom Level (mPD)'] = excel_master.SoilProperties['Bottom Level (mPD)'].fillna(
        -99999)
    excel_master.SoilProperties = excel_master.SoilProperties.sort_values(by=['Soil Type', 'Bottom Level (mPD)'],
                                                                            ascending=[True, False]).reset_index(
        drop=True)

    df_soil_prop = excel_master.SoilProperties.copy()
    excel_master.PLAXISSoil = pd.DataFrame(
        columns=['Borehole', 'Soil', 'Top Level (mPD)', 'Bottom Level (mPD)', 'PLAXIS Soil'])

    df_geo = excel_inputs.GeoSection
    if not excel_inputs.GeoSection.empty:
        df_vbh = pd.DataFrame(columns=['Borehole Name', 'X (mm)'])
        df_geo.drop_duplicates(subset='VBH Mark', inplace=True)
        for index, record in df_geo.iterrows():
            vbh = record['VBH Mark']
            x = record['X (mm)']
            row = df_vbh.index.size
            df_vbh.loc[row] = [vbh, x]

    else:
        df_vbh = excel_inputs.VBH

    list_vbh = df_vbh['Borehole Name'].to_list()
    df_bh = excel_inputs.Borehole
    df_bh = df_bh[df_bh['Borehole'].isin(list_vbh)]

    for index, record in df_bh.iterrows():
        bh_name = record['Borehole']
        bh_soil = record['Soil']
        bh_tl = record['Top Level (mPD)']
        bh_bl = record['Bottom Level (mPD)']

        row = excel_master.PLAXISSoil.index.size

        condition = df_soil_prop['Soil Type'] == bh_soil
        sorted_df = df_soil_prop[condition].sort_values(by='Bottom Level (mPD)', ascending=False)
        for i, r in sorted_df.iterrows():
            bh_seg_name = r['Abbreviation']
            bh_seg_bl = r['Bottom Level (mPD)']
            excel_master.PLAXISSoil.loc[row, 'Borehole'] = bh_name
            excel_master.PLAXISSoil.loc[row, 'Soil'] = bh_soil
            excel_master.PLAXISSoil.loc[row, 'Top Level (mPD)'] = bh_tl
            excel_master.PLAXISSoil.loc[row, 'PLAXIS Soil'] = bh_seg_name
            if bh_seg_bl >= bh_tl:
                excel_master.PLAXISSoil.loc[row, 'Bottom Level (mPD)'] = bh_tl
            elif (bh_seg_bl < bh_tl) and (bh_seg_bl > bh_bl):
                excel_master.PLAXISSoil.loc[row, 'Bottom Level (mPD)'] = bh_seg_bl
                bh_tl = bh_seg_bl
            elif bh_seg_bl <= bh_bl:
                excel_master.PLAXISSoil.loc[row, 'Bottom Level (mPD)'] = bh_bl
                bh_tl = bh_bl

            row += 1

    list_bh_name = df_vbh['Borehole Name'].to_list()
    list_bh_x = df_vbh['X (mm)'].to_list()

    df_ground_profile = pd.DataFrame(columns=list_bh_name)
    df_ground_profile.loc['X (m)'] = [x / 1000 for x in list_bh_x]

    for bh_name in list_bh_name:
        condition = excel_master.PLAXISSoil['Borehole'] == bh_name
        tl = excel_master.PLAXISSoil.loc[condition, 'Top Level (mPD)'].max()
        df_ground_profile.loc['Top', bh_name] = tl

    sorted_df = excel_master.PLAXISSoil.groupby('PLAXIS Soil')['Top Level (mPD)'].mean().reset_index()
    sorted_df = sorted_df.sort_values(by='Top Level (mPD)', ascending=False)
    new_indices = sorted_df['PLAXIS Soil'].drop_duplicates(keep='first').to_list()
    df = excel_master.PLAXISSoil.copy()

    df_add = pd.DataFrame(columns=list_bh_name, index=new_indices)
    df_ground_profile = pd.concat([df_ground_profile, df_add])
    prev_soil_type = "Top"
    for soil_type, row in df_ground_profile.iloc[2:].iterrows():
        for bh in df_ground_profile.columns:
            condition1 = df['Borehole'] == bh
            condition2 = df['PLAXIS Soil'] == soil_type
            condition = condition1 & condition2
            is_true = np.any(condition)
            if is_true:
                bl = df.loc[condition, 'Bottom Level (mPD)'].values[0]
                df_ground_profile.loc[soil_type, bh] = bl
            else:
                bl = df_ground_profile.loc[prev_soil_type, bh]
                df_ground_profile.loc[soil_type, bh] = bl
        prev_soil_type = soil_type

    excel_master.SoilProperties = df_soil_prop
    excel_master.GroundProfile = df_ground_profile
    return file_paths, excel_inputs, excel_master


def format_anchor_loc(file_paths, excel_inputs, excel_master):
    excel_inputs.Anchor = excel_inputs.Anchor.sort_values(by=['Stage', 'X1 (mm)', 'X2 (mm)'],
                                                          ascending=[True, True, True])
    excel_inputs.Anchor = excel_inputs.Anchor.reset_index(drop=True)
    df_steel_section = excel_inputs.SteelSectionProp
    df_anchor_loc = pd.DataFrame(
        columns=['Anchor Name', 'Anchor Type (F/N/G)', 'Stage', 'X1 (m)', 'Y1 (m)', 'X2 (m)', 'Y2 (m)',
                 'θ, Angle to Gravity (deg)', 'α, Angle to Waling (deg)',
                 'Steel Section', 'No.', 'E (MPa)', 'Area (cm2)', 'Spacing (m)', 'EA*sin(α)^2/S (kN/m/m)',
                 'Prestress (kN/m)'])

    for i, r in excel_inputs.Anchor.iterrows():
        row = df_anchor_loc.index.size
        anchor_type = r['Anchor Type (F/N/G)']
        stage = r['Stage']
        df_anchor_loc.loc[row, 'Anchor Type (F/N/G)'] = anchor_type
        df_anchor_loc.loc[row, 'Stage'] = stage

        name = f'{r["Strut Mark"]}_{stage}_N'

        df_anchor_loc.loc[row, 'X1 (m)'] = r['X1 (mm)'] / 1000
        df_anchor_loc.loc[row, 'Y1 (m)'] = r['Z1 (mm)'] / 1000
        df_anchor_loc.loc[row, 'X2 (m)'] = r['X2 (mm)'] / 1000
        df_anchor_loc.loc[row, 'Y2 (m)'] = r['Z2 (mm)'] / 1000

        if anchor_type == 'F':
            if r['X1 (mm)'] < r['X2 (mm)']:
                name = f'{r["Strut Mark"]}_{stage}_F0'
            else:
                name = f'{r["Strut Mark"]}_{stage}_F1'

        df_anchor_loc.loc[row, 'Anchor Name'] = name

        # df_anchor_loc.loc[row, 'θ, Angle to Gravity (deg)'] = r['Angle to Gravity (Deg)']
        df_anchor_loc.loc[row, 'α, Angle to Waling (deg)'] = r['Angle to Waling (Deg)']
        df_anchor_loc.loc[row, 'Steel Section'] = r['Steel Section']
        df_anchor_loc.loc[row, 'No.'] = r['Number of Section']
        df_anchor_loc.loc[row, 'Spacing (m)'] = r['Spacing (m)']
        df_anchor_loc.loc[row, 'Prestress (kN/m)'] = r['Prestress (kN/m)']

    df_anchor_loc['X1 (m)'] = df_anchor_loc['X1 (m)'].astype(float)
    df_anchor_loc['X2 (m)'] = df_anchor_loc['X2 (m)'].astype(float)
    df_anchor_loc['Y1 (m)'] = df_anchor_loc['Y1 (m)'].astype(float)
    df_anchor_loc['Y2 (m)'] = df_anchor_loc['Y2 (m)'].astype(float)
    df_anchor_loc['θ, Angle to Gravity (deg)'] = df_anchor_loc['θ, Angle to Gravity (deg)'].astype(float)
    df_anchor_loc['α, Angle to Waling (deg)'] = df_anchor_loc['α, Angle to Waling (deg)'].astype(float)

    dx = abs(df_anchor_loc['X2 (m)'] - df_anchor_loc['X1 (m)'])
    dy = abs(df_anchor_loc['Y2 (m)'] - df_anchor_loc['Y1 (m)'])
    # df_anchor_loc['θ, Angle to Gravity (deg)'] = np.degrees(np.arctan2(dx, dy))
    df_anchor_loc['θ, Angle to Gravity (deg)'] = np.where(
        df_anchor_loc['θ, Angle to Gravity (deg)'].isnull(),
        np.degrees(np.arctan2(dx, dy)),
        df_anchor_loc['θ, Angle to Gravity (deg)']
    )
    df_anchor_loc['E (MPa)'] = 205000
    df_merged = pd.merge(df_anchor_loc, df_steel_section[['Steel Section', 'A (cm2)']], on='Steel Section')
    df_anchor_loc['Area (cm2)'] = df_merged['A (cm2)'] * df_anchor_loc['No.']
    # Convert the angles to radians using numpy
    theta_rad = np.radians(df_anchor_loc['θ, Angle to Gravity (deg)'])
    alpha_rad = np.radians(df_anchor_loc['α, Angle to Waling (deg)'])

    # Perform the calculation using NumPy arrays
    # df_anchor_loc['EA*sin(θ)*sin(α)^2/S (kN/m/m)'] = (
    #         df_anchor_loc['E (MPa)'] * 10 ** 6 *
    #         df_anchor_loc['Area (cm2)'] / 100 / 100 *
    #         np.sin(theta_rad) *
    #         np.sin(alpha_rad) ** 2 /
    #         df_anchor_loc['Spacing (m)'] / 1000
    # )

    df_anchor_loc['EA*sin(α)^2/S (kN/m/m)'] = (
            df_anchor_loc['E (MPa)'] * 10 ** 6 *
            df_anchor_loc['Area (cm2)'] / 100 / 100 *
            np.sin(alpha_rad) ** 2 /
            df_anchor_loc['Spacing (m)'] / 1000
    )

    excel_master.AnchorLoc = df_anchor_loc

    return file_paths, excel_inputs, excel_master


def format_iwl(file_paths, excel_inputs, excel_master):
    df_section = excel_inputs.Section.copy()
    df_plate_loc = excel_master.PlateLoc.copy()

    condition = df_section['Name'] == 'Section_Start'
    x_start = df_section.loc[condition, 'X (mm)'].values[0] / 1000

    condition = df_plate_loc['Plate Name'] == 'Plate_1'
    y_start = df_plate_loc.loc[condition, 'Y1 (m)'].values[0] - 0.5

    condition = df_section['Name'] == 'Section_End'
    x_end = df_section.loc[condition, 'X (mm)'].values[0] / 1000

    condition = df_plate_loc['Plate Name'] == 'Plate_2'
    y_end = df_plate_loc.loc[condition, 'Y1 (m)'].values[0] - 0.5

    data = {'X (m)': [x_start, x_end], 'Y (m)': [y_start, y_end]}
    df_iwl = pd.DataFrame(data)

    excel_master.IWL = df_iwl

    return file_paths, excel_inputs, excel_master


def format_line_load(file_paths, excel_inputs, excel_master):
    df_section = excel_inputs.Section.copy()
    df_plate_loc = excel_master.PlateLoc.copy()

    condition = df_section['Name'] == 'Section_Start'
    x1_left = df_section.loc[condition, 'X (mm)'].values[0] / 1000

    condition = df_plate_loc['Plate Name'] == 'Plate_1'
    x2_left = df_plate_loc.loc[condition, 'X (m)'].values[0]
    y1_left = df_plate_loc.loc[condition, 'Y1 (m)'].values[0]
    y2_left = df_plate_loc.loc[condition, 'Y1 (m)'].values[0]

    condition = df_section['Name'] == 'Section_End'
    x2_right = df_section.loc[condition, 'X (mm)'].values[0] / 1000

    condition = df_plate_loc['Plate Name'] == 'Plate_2'
    x1_right = df_plate_loc.loc[condition, 'X (m)'].values[0]
    y1_right = df_plate_loc.loc[condition, 'Y1 (m)'].values[0]
    y2_right = df_plate_loc.loc[condition, 'Y1 (m)'].values[0]

    data = {'X1 (m)': [x1_left, x1_right], 'Y1 (m)': [y1_left, y1_right],
            'X2 (m)': [x2_left, x2_right], 'Y2 (m)': [y2_left, y2_right],
            'Fx_Start (kN/m)': [0, 0], 'Fx_End (kN/m)': [0, 0],
            'Fy_Start (kN/m)': [0, 0], 'Fy_End (kN/m)': [0, 0],
            }
    df_lineload = pd.DataFrame(data)

    excel_master.LineLoad = df_lineload

    return file_paths, excel_inputs, excel_master


def format_point_load(file_paths, excel_inputs, excel_master):
    df_section = excel_inputs.Section.copy()
    df_plate_loc = excel_master.PlateLoc.copy()

    condition = df_section['Name'] == 'Section_Start'
    x1_left = df_section.loc[condition, 'X (mm)'].values[0] / 1000

    condition = df_plate_loc['Plate Name'] == 'Plate_1'
    y1_left = df_plate_loc.loc[condition, 'Y1 (m)'].values[0]

    data = {'X (m)': [x1_left], 'Y (m)': [y1_left],
            'Fx (kN/m)': [0], 'Fy (kN/m)': [0], 'M (kNm/m)': [0]
            }
    df_pointload = pd.DataFrame(data)

    excel_master.PointLoad = df_pointload

    return file_paths, excel_inputs, excel_master


def format_input_parameter(file_paths, excel_inputs, excel_master):
    df_section = excel_inputs.Section.copy()
    df_plate_loc = excel_master.PlateLoc.copy()

    condition = df_section['Name'] == 'Section_Start'
    x1_left = df_section.loc[condition, 'X (mm)'].values[0] / 1000

    condition = df_plate_loc['Plate Name'] == 'Plate_1'
    x2_left = df_plate_loc.loc[condition, 'X (m)'].values[0]
    y1_left = df_plate_loc.loc[condition, 'Y1 (m)'].values[0]
    y2_left = df_plate_loc.loc[condition, 'Y1 (m)'].values[0]

    condition = df_section['Name'] == 'Section_End'
    x2_right = df_section.loc[condition, 'X (mm)'].values[0] / 1000

    condition = df_plate_loc['Plate Name'] == 'Plate_2'
    x1_right = df_plate_loc.loc[condition, 'X (m)'].values[0]
    y1_right = df_plate_loc.loc[condition, 'Y1 (m)'].values[0]
    y2_right = df_plate_loc.loc[condition, 'Y1 (m)'].values[0]

    df_anchor = excel_master.AnchorLoc.copy()
    df_input_parameter = pd.DataFrame()
    columns1 = ['Anchor Layer', 'X1 (m)', 'Y1 (m)', 'X2 (m)', 'Y2 (m)', 'α, Angle to Waling (deg)',
                'θ, Angle to Gravity (deg)', 'Spacing (m)']
    columns2 = ['Anchor Name', 'X1 (m)', 'Y1 (m)', 'X2 (m)', 'Y2 (m)', 'α, Angle to Waling (deg)',
                'θ, Angle to Gravity (deg)', 'Spacing (m)']
    df_input_parameter[columns1] = df_anchor[columns2]
    df_input_parameter['Max Anchor Section'] = 'UC356x406x634'
    df_input_parameter['Max Anchor Number'] = 1
    df_input_parameter['Max Prestress (kN/m)'] = 0
    df_input_parameter['Line1_X1 (m)'] = x1_left
    df_input_parameter['Line1_Y1 (m)'] = y1_left - 0.5
    df_input_parameter['Line1_X2 (m)'] = x2_left - 5
    df_input_parameter['Line1_Y2 (m)'] = y2_left - 0.5
    df_input_parameter['Line1_Uy_Limit (m)'] = 0.02

    df_input_parameter['Line2_X1 (m)'] = x1_right + 5
    df_input_parameter['Line2_Y1 (m)'] = y1_right - 0.5
    df_input_parameter['Line2_X2 (m)'] = x2_right
    df_input_parameter['Line2_Y2 (m)'] = y2_right - 0.5
    df_input_parameter['Line2_Uy_Limit (m)'] = 0.02

    excel_master.InputParameter = df_input_parameter
    return file_paths, excel_inputs, excel_master


def format_section(file_paths, excel_inputs, excel_master):
    df_section = excel_inputs.Section.copy()

    condition = df_section['Name'] == 'Section_Start'
    x_start = df_section.loc[condition, 'X (mm)'].values[0] / 1000

    condition = df_section['Name'] == 'Section_End'
    x_end = df_section.loc[condition, 'X (mm)'].values[0] / 1000

    data = {'Name': ['Section_Start', 'Section_End'], 'X (m)': [x_start, x_end]}
    df_section = pd.DataFrame(data)

    excel_master.Section = df_section

    return file_paths, excel_inputs, excel_master


def export(file_paths, excel_inputs, excel_master):
    with pd.ExcelWriter(file_paths.ExcelPlaxis) as writer:
        excel_master.InputParameter.to_excel(writer, sheet_name='Input_Parameter', index=False)
        excel_master.GroundProfile.to_excel(writer, sheet_name='Ground_Profile')
        excel_master.SoilProperties.to_excel(writer, sheet_name='Soil_Properties', index=False)
        excel_master.PlateLoc.to_excel(writer, sheet_name='Plate_Location', index=False)
        excel_master.AnchorLoc.to_excel(writer, sheet_name='Anchor_Location', index=False)
        excel_master.EL.to_excel(writer, sheet_name='Excavation_Level', index=False)
        excel_master.Section.to_excel(writer, sheet_name='Section', index=False)
        excel_master.IWL.to_excel(writer, sheet_name='Initial_Water_Level', index=False)
        excel_master.LineLoad.to_excel(writer, sheet_name='Line_Load', index=False)
        excel_master.PointLoad.to_excel(writer, sheet_name='Point_Load', index=False)
    print('Exported PLAXIS Master Data to Excel!')
    return excel_master


def generate_master_excel(file_paths, excel_inputs, excel_master):
    file_paths, excel_inputs, excel_master = divide_soil_seg(file_paths, excel_inputs, excel_master)
    file_paths, excel_inputs, excel_master = section_to_borehole(file_paths, excel_inputs, excel_master)
    file_paths, excel_inputs, excel_master = format_plate_loc(file_paths, excel_inputs, excel_master)
    file_paths, excel_inputs, excel_master = format_el(file_paths, excel_inputs, excel_master)
    file_paths, excel_inputs, excel_master = format_ground_profile(file_paths, excel_inputs, excel_master)
    file_paths, excel_inputs, excel_master = format_anchor_loc(file_paths, excel_inputs, excel_master)
    file_paths, excel_inputs, excel_master = format_iwl(file_paths, excel_inputs, excel_master)
    file_paths, excel_inputs, excel_master = format_input_parameter(file_paths, excel_inputs, excel_master)
    file_paths, excel_inputs, excel_master = format_line_load(file_paths, excel_inputs, excel_master)
    file_paths, excel_inputs, excel_master = format_point_load(file_paths, excel_inputs, excel_master)
    file_paths, excel_inputs, excel_master = format_section(file_paths, excel_inputs, excel_master)
    excel_master = export(file_paths, excel_inputs, excel_master)
