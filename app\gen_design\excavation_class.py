"""
Excavation Classes

This module provides backward compatibility for the reorganized excavation system.
All classes have been moved to the excavation.models package but are re-exported here for compatibility.
"""

# Import all classes from the new location for backward compatibility
from .excavation.models import (
    ExcavationCoordinatePoint,
    ExcavationPolylineSet,
    ExcavationStageSection,
    ExcavationLevelUIComponents
)

# Re-export for backward compatibility
__all__ = [
    'ExcavationCoordinatePoint',
    'ExcavationPolylineSet',
    'ExcavationStageSection',
    'ExcavationLevelUIComponents'
]
        """
        Initialize coordinate point UI components.

        Args:
            parent_frame: The parent frame to contain this point
            point_number: Sequential number for this point in the polyline
            row: Grid row position for this point
        """
        self.parent_frame = parent_frame
        self.point_number = point_number
        self.row = row
        self.entries = {}
        self.widgets = []  # Track all widgets for cleanup
        self.create_ui()

    def create_ui(self):
        """Create the coordinate input fields for this point."""
        # Point label
        point_label = create_info_label(self.parent_frame, f"Point {self.point_number}:")
        point_label.configure(width=8)
        point_label.grid(row=self.row, column=0, sticky="w", padx=5, pady=2)
        self.widgets.append(point_label)

        # Create X and Y coordinate entries
        for i, coord_label in enumerate(EL_COORDINATE_LABELS):
            # Coordinate label
            label = create_info_label(self.parent_frame, coord_label)
            label.grid(row=self.row, column=1 + i*2, sticky="w", padx=2, pady=2)
            self.widgets.append(label)

            # Coordinate entry
            entry = tk.Entry(
                self.parent_frame,
                width=ENTRY_WIDTH,
                font=FONT_REGULAR,
                justify='center'
            )
            entry.grid(row=self.row, column=2 + i*2, padx=5, pady=2)
            self.widgets.append(entry)

            # Store entry reference
            coord_key = coord_label.replace(":", "")  # "X" or "Y"
            self.entries[coord_key] = entry

    def get_values(self) -> Dict[str, float]:
        """
        Get coordinate values with validation.

        Returns:
            Dictionary with X and Y coordinate values

        Raises:
            ValueError: If coordinate values are invalid
        """
        values = {}
        for coord, entry in self.entries.items():
            try:
                value_str = entry.get().strip()
                value = float(value_str) if value_str else 0.0
                values[coord] = value
            except ValueError:
                raise ValueError(f"Invalid value for {coord} in Point {self.point_number}: '{entry.get()}'")
        return values

    def set_values(self, values: Dict[str, float]):
        """
        Set coordinate values in the entry fields.

        Args:
            values: Dictionary containing X and Y coordinate values
        """
        for coord, entry in self.entries.items():
            if coord in values:
                entry.delete(0, tk.END)
                entry.insert(0, str(values[coord]))

    def clear_values(self):
        """Clear all coordinate values."""
        for entry in self.entries.values():
            entry.delete(0, tk.END)

    def destroy(self):
        """Remove this coordinate point from the UI."""
        for widget in self.widgets:
            if widget.winfo_exists():
                widget.destroy()
        self.widgets.clear()


class ExcavationPolylineSet:
    """Manages a set of coordinate points that form a complete excavation polyline."""

    def __init__(self, parent_frame: tk.Frame, set_number: int, row: int):
        """
        Initialize polyline set UI components.

        Args:
            parent_frame: The parent frame to contain this polyline set
            set_number: Sequential number for this polyline set
            row: Grid row position for this polyline set
        """
        self.parent_frame = parent_frame
        self.set_number = set_number
        self.row = row
        self.coordinate_points = []
        self.frame = None
        self.button_widgets = []
        self.create_ui()

    def create_ui(self):
        """Create the UI for this polyline set."""
        # Create frame for this polyline set
        self.frame = create_labeled_frame(self.parent_frame, f"Polyline Set {self.set_number}",
                                         font=FONT_BOLD, bg=HIGHLIGHT_COLOR, fg="#333333",
                                         bd=1, relief="solid", padx=10, pady=5)
        self.frame.grid(row=self.row, column=0, columnspan=6, sticky="ew", padx=5, pady=3)

        # Configure grid weights
        self.frame.grid_columnconfigure(0, weight=0)
        self.frame.grid_columnconfigure(1, weight=1)
        self.frame.grid_columnconfigure(2, weight=0)
        self.frame.grid_columnconfigure(3, weight=1)
        self.frame.grid_columnconfigure(4, weight=0)
        self.frame.grid_columnconfigure(5, weight=1)

        # Add initial coordinate point
        self.add_coordinate_point()

        # Create buttons frame
        buttons_frame = create_standard_frame(self.frame, bg=HIGHLIGHT_COLOR)
        buttons_frame.grid(row=100, column=0, columnspan=6, sticky="ew", pady=5)

        # Add point button
        add_btn = create_primary_button(buttons_frame, "• Add Point", self.add_coordinate_point,
                                       width=10, height=1)
        self.button_widgets.append(add_btn)

        # Remove point button
        remove_btn = create_danger_button(buttons_frame, "- Remove Point", self.remove_coordinate_point,
                                         width=12, height=1)
        self.button_widgets.append(remove_btn)

        self.update_button_states()

    def add_coordinate_point(self):
        """Add a new coordinate point to this polyline."""
        point_number = len(self.coordinate_points) + 1
        row = len(self.coordinate_points)

        coordinate_point = ExcavationCoordinatePoint(self.frame, point_number, row)
        self.coordinate_points.append(coordinate_point)

        self.update_button_states()
        logging.info(f"Added coordinate point {point_number} to polyline set {self.set_number}")

    def remove_coordinate_point(self):
        """Remove the last coordinate point from this polyline."""
        if len(self.coordinate_points) > 1:  # Keep at least one point
            coordinate_point = self.coordinate_points.pop()
            coordinate_point.destroy()
            self.update_button_states()
            logging.info(f"Removed coordinate point from polyline set {self.set_number}")

    def update_button_states(self):
        """Update the state of add/remove buttons."""
        if len(self.button_widgets) >= 2:
            remove_btn = self.button_widgets[1]
            if len(self.coordinate_points) <= 1:
                remove_btn.configure(state="disabled", bg="#CCCCCC")
            else:
                remove_btn.configure(state="normal", bg="#FF6B6B")

    def get_all_values(self) -> List[Dict[str, float]]:
        """
        Get all coordinate values for this polyline set.

        Returns:
            List of coordinate dictionaries for this polyline

        Raises:
            ValueError: If any coordinate values are invalid
        """
        values = []
        for point in self.coordinate_points:
            try:
                point_values = point.get_values()
                values.append(point_values)
            except ValueError as e:
                raise ValueError(f"Polyline Set {self.set_number}: {str(e)}")
        return values

    def set_values(self, values_list: List[Dict[str, float]]):
        """
        Set coordinate values for this polyline set.

        Args:
            values_list: List of coordinate dictionaries to set
        """
        # Clear existing points
        for point in self.coordinate_points:
            point.destroy()
        self.coordinate_points.clear()

        # Create points for the provided values
        if not values_list:
            # Add at least one empty point if no values provided
            self.add_coordinate_point()
        else:
            for i, values in enumerate(values_list):
                point_number = i + 1
                row = i
                coordinate_point = ExcavationCoordinatePoint(self.frame, point_number, row)
                coordinate_point.set_values(values)
                self.coordinate_points.append(coordinate_point)

        self.update_button_states()

    def clear_all_points(self):
        """Clear all coordinate values in this polyline set."""
        for point in self.coordinate_points:
            point.clear_values()

    def remove_all_points(self):
        """
        Completely remove all coordinate points and their UI elements.
        
        This method destroys all coordinate point UI components and clears
        the coordinate_points list. Used when loading saved data to ensure
        a clean state before repopulating with saved coordinates.
        """
        try:
            # Destroy all coordinate point UI elements
            for point in self.coordinate_points:
                if hasattr(point, 'frame') and point.frame:
                    point.frame.destroy()
                    logging.info(f"Destroyed coordinate point {point.point_number} UI for polyline set {self.set_number}")
            
            # Clear the coordinate points list
            self.coordinate_points.clear()
            logging.info(f"Removed all coordinate points from polyline set {self.set_number}")
            
            # Update button states
            self.update_button_states()
            
        except Exception as e:
            logging.error(f"Error removing all points from polyline set {self.set_number}: {str(e)}")
            raise

    def destroy(self):
        """Remove this polyline set from the UI."""
        for point in self.coordinate_points:
            point.destroy()
        for widget in self.button_widgets:
            if widget.winfo_exists():
                widget.destroy()
        if self.frame and self.frame.winfo_exists():
            self.frame.destroy()


class ExcavationStageSection:
    """Manages multiple polyline sets for a single excavation stage."""

    def __init__(self, parent_frame: tk.Frame, stage_name: str, row: int):
        """
        Initialize excavation stage section.

        Args:
            parent_frame: The parent frame to contain this stage section
            stage_name: Name of the excavation stage
            row: Grid row position for this stage section
        """
        self.parent_frame = parent_frame
        self.stage_name = stage_name
        self.row = row
        self.polyline_sets = []
        self.frame = None
        self.content_frame = None
        self.buttons_frame = None
        self.add_set_button = None
        self.remove_set_button = None
        self.create_ui()

    def create_ui(self):
        """Create the UI for this excavation stage section."""
        # Create main frame for this stage
        self.frame = create_labeled_frame(self.parent_frame, f"Excavation Stage {self.stage_name}",
                                         font=FONT_HEADER, bg=BG_COLOR, fg="#333333",
                                         bd=2, relief="solid", padx=15, pady=10)
        self.frame.grid(row=self.row, column=0, sticky="ew", padx=10, pady=8)

        # Configure grid weights
        self.frame.grid_columnconfigure(0, weight=1)

        # Create content frame for polyline sets
        self.content_frame = create_standard_frame(self.frame)
        self.content_frame.grid(row=0, column=0, sticky="ew", padx=5, pady=5)
        self.content_frame.grid_columnconfigure(0, weight=1)

        # Add initial polyline set
        self.add_polyline_set()

        # Create stage-level buttons frame
        self.buttons_frame = create_standard_frame(self.frame)
        self.buttons_frame.grid(row=1, column=0, sticky="ew", padx=5, pady=5)

        # Add polyline set button
        self.add_set_button = create_primary_button(self.buttons_frame, "Add Polyline Set",
                                                   self.add_polyline_set, width=15, height=1,
                                                   font=FONT_BOLD, padx=5)

        # Remove polyline set button
        self.remove_set_button = create_danger_button(self.buttons_frame, "Remove Polyline Set",
                                                     self.remove_polyline_set, width=17, height=1,
                                                     font=FONT_BOLD, padx=5)

        self.update_button_states()

    def add_polyline_set(self):
        """Add a new polyline set to this stage."""
        if len(self.polyline_sets) >= MAX_SETS_PER_STAGE:
            messagebox.showwarning("Limit Reached", f"Maximum {MAX_SETS_PER_STAGE} polyline sets per stage")
            return

        set_number = len(self.polyline_sets) + 1
        row = len(self.polyline_sets)

        polyline_set = ExcavationPolylineSet(self.content_frame, set_number, row)
        self.polyline_sets.append(polyline_set)

        self.update_button_states()
        logging.info(f"Added polyline set {set_number} to stage {self.stage_name}")

    def remove_polyline_set(self):
        """Remove the last polyline set from this stage."""
        if len(self.polyline_sets) > 1:  # Keep at least one set
            polyline_set = self.polyline_sets.pop()
            polyline_set.destroy()
            self.update_button_states()
            logging.info(f"Removed polyline set from stage {self.stage_name}")

    def update_button_states(self):
        """Update the state of add/remove buttons."""
        if self.remove_set_button:
            if len(self.polyline_sets) <= 1:
                self.remove_set_button.configure(state="disabled", bg="#CCCCCC")
            else:
                self.remove_set_button.configure(state="normal", bg="#FF6B6B")

        if self.add_set_button:
            if len(self.polyline_sets) >= MAX_SETS_PER_STAGE:
                self.add_set_button.configure(state="disabled", bg="#CCCCCC")
            else:
                self.add_set_button.configure(state="normal", bg=ACCENT_COLOR)

    def get_all_values(self) -> List[List[Dict[str, float]]]:
        """
        Get all polyline data for this stage.

        Returns:
            List of polyline sets, each containing a list of coordinate points

        Raises:
            ValueError: If any coordinate values are invalid
        """
        stage_data = []
        for polyline_set in self.polyline_sets:
            try:
                set_data = polyline_set.get_all_values()
                stage_data.append(set_data)
            except ValueError as e:
                raise ValueError(f"Stage {self.stage_name}: {str(e)}")
        return stage_data

    def set_values(self, stage_data: List[List[Dict[str, float]]]):
        """
        Set polyline data for this stage.

        Args:
            stage_data: List of polyline sets to load into this stage
        """
        # Clear existing polyline sets
        for polyline_set in self.polyline_sets:
            polyline_set.destroy()
        self.polyline_sets.clear()

        # Create polyline sets for the provided data
        if not stage_data:
            # Add at least one empty set if no data provided
            self.add_polyline_set()
        else:
            for i, set_data in enumerate(stage_data):
                set_number = i + 1
                row = i
                polyline_set = ExcavationPolylineSet(self.content_frame, set_number, row)
                polyline_set.set_values(set_data)
                self.polyline_sets.append(polyline_set)

        self.update_button_states()

    def clear_all_sets(self):
        """Clear all polyline sets in this stage."""
        for polyline_set in self.polyline_sets:
            polyline_set.clear_all_points()

    def remove_all_sets(self):
        """
        Completely remove all polyline sets and their UI elements.
        
        This method destroys all polyline set UI components and clears
        the polyline_sets list. Used when loading saved data to ensure
        a clean state before repopulating with saved configurations.
        """
        try:
            # Destroy all polyline set UI elements
            for polyline_set in self.polyline_sets:
                if hasattr(polyline_set, 'frame') and polyline_set.frame:
                    polyline_set.frame.destroy()
                    logging.info(f"Destroyed polyline set {polyline_set.set_number} UI for stage {self.stage_name}")
            
            # Clear the polyline sets list
            self.polyline_sets.clear()
            logging.info(f"Removed all polyline sets from stage {self.stage_name}")
            
            # Update button states
            self.update_button_states()
            
        except Exception as e:
            logging.error(f"Error removing all sets from stage {self.stage_name}: {str(e)}")
            raise

    def destroy(self):
        """Remove this stage section from the UI."""
        for polyline_set in self.polyline_sets:
            polyline_set.destroy()
        if self.frame and self.frame.winfo_exists():
            self.frame.destroy()


class ExcavationLevelUIComponents:
    """Manages UI components for excavation level configuration."""

    def __init__(self):
        """Initialize UI components manager."""
        self.stage_sections = {}  # Dict[str, ExcavationStageSection]
        self.content_frame = None
        self.stage_label = None
        self.status_label = None
        self.dependency_label = None

    def clear_all_stages(self):
        """Clear all stage sections."""
        for stage_section in self.stage_sections.values():
            stage_section.destroy()
        self.stage_sections.clear()

    def add_stage_section(self, stage_name: str) -> ExcavationStageSection:
        """
        Add a new stage section.

        Args:
            stage_name: Name of the excavation stage

        Returns:
            The created ExcavationStageSection instance
        """
        if self.content_frame is None:
            raise ValueError("Content frame not initialized")

        row = len(self.stage_sections)
        stage_section = ExcavationStageSection(self.content_frame, stage_name, row)
        self.stage_sections[stage_name] = stage_section
        return stage_section

    def get_all_stage_data(self) -> Dict[str, List[List[Dict[str, float]]]]:
        """
        Get all excavation level data organized by stage.

        Returns:
            Dictionary mapping stage names to lists of polyline sets

        Raises:
            ValueError: If any coordinate values are invalid
        """
        data = {}
        for stage_name, stage_section in self.stage_sections.items():
            try:
                stage_data = stage_section.get_all_values()
                data[stage_name] = stage_data
            except ValueError as e:
                raise ValueError(f"Error in stage {stage_name}: {str(e)}")
        return data
