"""
Excavation Classes

This module provides backward compatibility for the reorganized excavation system.
All classes have been moved to the excavation.models package but are re-exported here for compatibility.
"""

# Import all classes from the new location for backward compatibility
from .excavation.models import (
    ExcavationCoordinatePoint,
    ExcavationPolylineSet,
    ExcavationStageSection,
    ExcavationLevelUIComponents
)

# Re-export for backward compatibility
__all__ = [
    'ExcavationCoordinatePoint',
    'ExcavationPolylineSet',
    'ExcavationStageSection',
    'ExcavationLevelUIComponents'
]
