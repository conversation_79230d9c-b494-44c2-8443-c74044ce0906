# Task Completion Guidelines

## When a Coding Task is Completed

### 1. Code Quality Checks for Modular Architecture
Since there are no automated linting/formatting tools configured:
- **Package Structure Validation** - Ensure new code follows the modular package organization
- **Component Design Review** - Check that UI components follow the established patterns (CoordinateSet, AnchorSection style)
- **Docstring Verification** - Ensure all new functions have proper docstrings with Args/Returns/Raises sections
- **Type Hints** - Add comprehensive type annotations to new functions and methods
- **Error Handling** - Verify appropriate exception handling with user-friendly messages

### 2. Testing Requirements for v1.8.x Architecture
As there is no formal test suite:
- **Component Testing** - Test individual UI components in isolation
- **Integration Testing** - Verify interactions between app/ and plaxis/ packages
- **UI Flow Testing** - Test complete workflows through multiple UI frames
- **PLAXIS Integration** - Verify PLAXIS automation still works if modified
- **Data Validation** - Test with sample Excel files in `Sample Input/` directory
- **Anchor Configuration** - Test complex anchor geometry and parameter configurations

### 3. Architecture Compliance
- **Package Organization** - Ensure code is placed in correct package (app/, plaxis/, calculation/)
- **Import Structure** - Verify imports follow package hierarchy correctly
- **Component Reusability** - Check that new UI components can be reused and extended
- **Separation of Concerns** - UI logic stays in app/, business logic in plaxis/

### 4. UI/UX Validation
- **Apple-Inspired Design** - Ensure consistent styling using defined constants
- **Component Behavior** - Test dynamic UI generation and component interactions
- **Modal Dialogs** - Verify proper modal window behavior and focus management
- **Scrollable Content** - Test scrolling behavior in complex forms
- **Error Messages** - Ensure user-friendly error dialogs and validation messages

### 5. Documentation Updates
- **Module Docstrings** - Update comprehensive module descriptions
- **Component Documentation** - Document new UI components with usage examples
- **Architecture Notes** - Update memory files if significant architectural changes
- **Version Updates** - Update `version_config.py` if needed

### 6. Logging and Monitoring
- **Comprehensive Logging** - Ensure appropriate logging for new features
- **Error Logging** - Verify error paths log correctly with sufficient context
- **User Action Logging** - Log significant user interactions for audit purposes
- **Performance Logging** - Add timing logs for potentially slow operations

### 7. Security and Authentication
- **Login Integration** - Ensure new features respect authentication requirements
- **Input Validation** - Verify all user inputs are properly validated
- **Session Management** - Check that session timeout is respected
- **Password Handling** - Ensure no sensitive data is logged or exposed

### 8. Windows Platform Compatibility
- **Path Handling** - Use pathlib or proper path separators for Windows
- **File Operations** - Test file reading/writing on Windows systems
- **PowerShell Integration** - Verify any shell commands work in PowerShell
- **Excel Integration** - Test Excel file operations with Windows Excel

### 9. PLAXIS Integration Validation
If PLAXIS-related code was modified:
- **plxscripting API** - Verify all PLAXIS API calls are correct
- **Model Building** - Test model building with various input configurations
- **Generative Design** - Test parametric studies and iterations
- **Result Processing** - Verify result extraction and visualization
- **Error Recovery** - Test PLAXIS error handling and recovery

### 10. Performance and Resource Management
- **UI Responsiveness** - Ensure UI remains responsive during operations
- **Memory Management** - Check for memory leaks in dynamic UI components
- **Threading** - Verify background operations don't block UI
- **Large Dataset Handling** - Test with complex Excel files and multiple iterations

## Specific Command Sequences for Task Completion

### After Code Changes in Modular Architecture
```powershell
# 1. Run the application to test
python main.py

# 2. Test package imports
python -c "from app._MainFrame import PlaxisAutomationApp; print('App package OK')"
python -c "from plaxis.builder import build; print('PLAXIS package OK')" 2>$null

# 3. Test component functionality
python -c "from app._GenDesign_Def_Anchor_Geom import CoordinateSet; print('Components OK')"

# 4. Check version consistency
python -c "from version_config import VERSION; print(f'Version: {VERSION}')"

# 5. Validate Excel processing
python -c "import pandas as pd; df = pd.read_excel('Sample Input/A.PLAXISInput_Geometry.xlsx'); print('Excel OK')"
```

### Component-Specific Testing
```powershell
# Test UI components individually
python -c "
import tkinter as tk
from app._GenDesign_Def_Anchor_Geom import GeometryUIComponents
root = tk.Tk()
ui = GeometryUIComponents()
print('UI components created successfully')
root.destroy()
"

# Test anchor configuration workflow
python -c "
from app._GenDesign_Def_Anchor_Param import AnchorConfig
config = AnchorConfig()
print('Anchor configuration created successfully')
"
```

### Before Committing Changes
```powershell
# 1. Verify all package imports work
python -c "import app, plaxis, calculation; print('All packages import OK')"

# 2. Check for syntax errors in all files
Get-ChildItem -Recurse -Include "*.py" | ForEach-Object { 
    python -m py_compile $_.FullName
}

# 3. Look for TODO comments that need addressing
Select-String -Pattern "TODO|FIXME" -Include "*.py" -Recurse

# 4. Verify version configuration
python -c "from version_config import get_version_info; print(get_version_info())"

# 5. Check logging configuration
Select-String -Pattern "logging\." -Include "*.py" -Recurse | Group-Object Filename
```

## Critical Success Criteria for v1.8.x

### Application Functionality
- **Application starts without errors** and shows proper version info
- **User can login successfully** with email authentication
- **All UI frames load correctly** (Login, Main, Build, GenDesign)
- **Component interactions work** (anchor geometry, parameter configuration)

### Core Features
- **Build PLAXIS Model works** with sample data
- **Generative Design functionality** operates correctly
- **Anchor configuration** (geometry and parameters) functions properly
- **Excel file reading** works with all sample input files

### Architecture Validation
- **Package structure** is maintained and imports work correctly
- **UI components** are reusable and properly encapsulated
- **Error handling** provides user-friendly messages at all levels
- **Logging captures** appropriate information across all packages

### Security and Performance
- **Authentication system** works with email integration
- **Session management** properly times out inactive sessions
- **UI remains responsive** during background operations
- **Memory usage** is reasonable for complex UI components

### Integration Points
- **PLAXIS integration** functions correctly (if PLAXIS is available)
- **Excel processing** handles all input file formats
- **Configuration management** works across all components
- **Version information** is consistent throughout application

## Rollback Criteria
If any of the following critical failures occur:
- **Application fails to start** or crashes on basic operations
- **Authentication system** becomes non-functional
- **Core PLAXIS building** functionality breaks
- **Data corruption** in Excel processing occurs
- **UI becomes** completely unresponsive or unusable

## Documentation Requirements
- **Update memories** if architectural changes were significant
- **Document new components** with clear usage examples
- **Update version history** in version_config.py if version changed
- **Add inline comments** for complex UI component interactions