import os

import numpy as np
import pandas as pd
from matplotlib import pyplot as plt
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


def chart_uy(chart_title, df_autorunlog, title_preload, title_x, title_y, path_folder, df_ss, show_plot=False):
    """
    Creates and saves a chart visualizing displacement data with different preloads.

    Parameters:
        chart_title (str): Title of the chart and filename
        df_autorunlog (DataFrame): Data containing analysis results
        title_preload (str): Column name for preload values to group by
        title_x (str): Name for x-axis after calculation
        title_y (str): Column name for y-axis data
        path_folder (str): Path to save the chart
        df_ss (DataFrame): Steel section data with 'Section' and other properties
        show_plot (bool, optional): Whether to display the plot. Defaults to False.

    Returns:
        matplotlib.figure.Figure: The created figure object
    """
    # Make a copy to avoid modifying the original dataframe
    df_plot = df_autorunlog.copy()
    # Merge data and calculate x-axis values
    df_plot = pd.merge(df_plot, df_ss, on='Section', how='left')

    # Validate required columns exist in dataframes
    required_cols = {'Section', 'Unit', 'M (kg/m)', title_preload, title_y}
    missing_cols = required_cols - set(df_plot.columns)
    if missing_cols:
        raise ValueError(f"Missing required columns in dataframe: {missing_cols}")

    if df_plot['M (kg/m)'].isnull().any():
        logger.warning("Some rows have missing 'M (kg/m)' values after merging")
    df_plot[title_x] = df_plot['M (kg/m)'] * df_plot['Unit']

    # Create the plot with a clean style
    plt.style.use('seaborn-v0_8-whitegrid')
    fig, ax = plt.subplots(figsize=(14, 8))

    # Plot data for each preload group
    unique_preloads = df_plot[title_preload].unique()
    colors = plt.cm.viridis(np.linspace(0, 1, len(unique_preloads)))

    for i, preload in enumerate(sorted(unique_preloads)):
        group = df_plot[df_plot[title_preload] == preload]
        marker = 'o' if preload == 0 else 'x'
        ax.plot(group[title_x], group[title_y],
                label=f'Preload {preload} kN/m',
                marker=marker,
                color=colors[i],
                markersize=8,
                linewidth=2)

    # Enhance chart appearance
    ax.set_xlabel(title_x, fontsize=12, fontweight='bold')
    ax.set_ylabel(title_y, fontsize=12, fontweight='bold')
    ax.set_title(chart_title, fontsize=14, fontweight='bold')
    ax.grid(True, linestyle='--', alpha=0.7)
    ax.legend(frameon=True, fontsize=10)

    # Adjust layout and save
    plt.tight_layout()
    try:
        os.makedirs(path_folder, exist_ok=True)
        plt.savefig(f'{path_folder}/{chart_title}.png', dpi=300, bbox_inches='tight')
    except Exception as e:
        logger.error(f"Error saving chart: {e}")

    # Show plot if requested
    if show_plot:
        plt.show()
    else:
        plt.close(fig)

    return fig
