# Suggested Commands for PLAXIS Automation Project

## Development Commands

### Running the Application
```powershell
# Run the main application
python main.py

# Run with debug logging
python -c "import logging; logging.basicConfig(level=logging.DEBUG); exec(open('main.py').read())"

# Run with specific module testing
python -c "from app._MainFrame import PlaxisAutomationApp; app = PlaxisAutomationApp(); app.mainloop()"
```

### Python Environment Management
```powershell
# Create virtual environment (recommended)
python -m venv venv
.\venv\Scripts\Activate.ps1

# Install dependencies
pip install -r requirements.txt

# Check Python version (must be 3.6+, recommended 3.8+)
python --version

# Verify core dependencies
python -c "import pandas, numpy, matplotlib, tkinter; print('Core dependencies OK')"

# Check PLAXIS integration (requires PLAXIS installed)
python -c "try: import plxscripting; print('PLAXIS integration available'); except: print('PLAXIS not available')"
```

### Project Structure Analysis
```powershell
# List all Python files in organized structure
Get-ChildItem -Recurse -Include "*.py" | Group-Object Directory | Format-Table Name, Count

# Check modular organization
Get-ChildItem -Path "app" -Include "*.py" | Select-Object Name
Get-ChildItem -Path "plaxis" -Recurse -Include "*.py" | Select-Object FullName

# Version information
python -c "from version_config import VERSION, APP_VERSION; print(f'Version: {VERSION}, App: {APP_VERSION}')"
```

### Code Quality and Analysis
```powershell
# Check code style (if flake8 available)
flake8 app/ plaxis/ --max-line-length=100

# Run type checking (if mypy available)
mypy app/ plaxis/ --ignore-missing-imports

# Count lines of code by package
Get-ChildItem -Path "app" -Recurse -Include "*.py" | Get-Content | Measure-Object -Line
Get-ChildItem -Path "plaxis" -Recurse -Include "*.py" | Get-Content | Measure-Object -Line

# Find complex UI components
Select-String -Pattern "class.*Frame|class.*Section|class.*Component" -Include "*.py" -Recurse
```

### Testing and Validation
```powershell
# Test main application startup
python -c "import main; print('Main module imports OK')"

# Test modular imports
python -c "from app._MainFrame import PlaxisAutomationApp; print('Main frame OK')"
python -c "from app._GenDesign_Def_Anchor_Geom import CoordinateSet; print('Anchor geom OK')"
python -c "from plaxis.builder import build; print('PLAXIS builder OK')" 2>$null

# Test Excel file processing
Test-Path "Sample Input\*.xlsx"
python -c "import pandas as pd; df = pd.read_excel('Sample Input/A.PLAXISInput_Geometry.xlsx', sheet_name=None); print(f'Excel sheets: {list(df.keys())}')"

# Validate configuration files
python -c "from version_config import get_version_info; print(get_version_info())"
```

### Development and Debugging
```powershell
# Search for specific patterns in new architecture
Select-String -Pattern "class.*UI|class.*Component" app/*.py
Select-String -Pattern "def.*coordinate|def.*anchor" app/*.py
Select-String -Pattern "logging\." -Include "*.py" -Recurse | Group-Object Filename

# Find UI constants and styling
Select-String -Pattern "FONT_|COLOR_|BG_COLOR" -Include "*.py" -Recurse

# Check anchor type implementations
Select-String -Pattern "anchor.*type|ANCHOR_TYPES" app/*.py

# Find error handling patterns
Select-String -Pattern "try:|except|raise|messagebox\.show" -Include "*.py" -Recurse
```

### File Operations and Analysis
```powershell
# Find all configuration and data files
Get-ChildItem -Include "*.xlsx", "*.ico", "*.txt", "*.py" | Select-Object Name, Length

# Check package structure
tree /F app
tree /F plaxis

# Find UI component hierarchy
Select-String -Pattern "tk\.|ttk\.|Frame|Button|Entry" app/*.py | Select-Object Filename, Line

# Search for PLAXIS integration points
Select-String -Pattern "plxscripting|PLAXIS" -Include "*.py" -Recurse
```

### Git and Version Control
```powershell
# Check current branch (should be developing_v1.8.2)
git branch

# View recent changes
git log --oneline -10 --graph

# Check for uncommitted changes in modular structure
git status
git diff --name-only

# See changes in app/ directory
git log --oneline app/

# View changes in plaxis/ package
git log --oneline plaxis/
```

### Build and Deployment
```powershell
# Test application compilation (requires Nuitka)
python -m nuitka --onefile main.py

# Check for deployment files
Test-Path "AIS.ico"
Test-Path "requirements.txt"
Test-Path "nuitka embed.txt"

# Validate all imports for compilation
python -c "
import sys
modules = ['app', 'plaxis', 'calculation']
for mod in modules:
    try:
        __import__(mod)
        print(f'{mod}: OK')
    except Exception as e:
        print(f'{mod}: Error - {e}')
"
```

### Advanced Analysis and Maintenance
```powershell
# Find TODO/FIXME comments in new structure
Select-String -Pattern "TODO|FIXME|HACK|XXX" -Include "*.py" -Recurse

# Analyze component complexity
Get-ChildItem -Path "app" -Include "*.py" | ForEach-Object {
    $lines = (Get-Content $_.FullName | Measure-Object -Line).Lines
    Write-Host "$($_.Name): $lines lines"
}

# Check for circular imports
python -c "
import ast
import os

def check_imports(directory):
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                print(f'Checking {os.path.join(root, file)}')

check_imports('app')
check_imports('plaxis')
"

# Memory and performance analysis
python -c "
import sys
import gc
import psutil
print(f'Python version: {sys.version}')
print(f'Memory usage: {psutil.Process().memory_info().rss / 1024 / 1024:.1f} MB')
print(f'Objects in memory: {len(gc.get_objects())}')
"
```

### UI and Component Testing
```powershell
# Test individual UI components
python -c "
import tkinter as tk
from app._GenDesign_Def_Anchor_Geom import CoordinateSet
root = tk.Tk()
frame = tk.Frame(root)
coord_set = CoordinateSet(frame, 1, 1)
print('CoordinateSet component created successfully')
root.destroy()
"

# Test anchor geometry configuration
python -c "
import tkinter as tk
from app._GenDesign_Def_Anchor_Geom import GeometryUIComponents
components = GeometryUIComponents()
print('GeometryUIComponents initialized successfully')
"
```

## Important Notes for v1.8.x Development

### New Architecture Considerations
- **Modular Structure**: Commands now work with `app/` and `plaxis/` packages
- **Component Testing**: Individual UI components can be tested in isolation
- **Package Dependencies**: Ensure imports work across the new package structure

### Development Workflow
- **Feature Development**: Work within appropriate packages (app/ for UI, plaxis/ for logic)
- **Testing**: Test both individual components and integrated functionality
- **Version Management**: Use `version_config.py` for all version references

### Debugging Strategy
- **Component-Level**: Test individual classes and components
- **Integration Testing**: Verify package interactions work correctly
- **UI Testing**: Test complex UI interactions with multiple components