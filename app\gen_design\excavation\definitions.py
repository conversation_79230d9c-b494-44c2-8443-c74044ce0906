"""
Excavation Level Configuration Module

This module provides UI components for configuring excavation level coordinates
organized by excavation stages. Each stage can contain multiple polyline sets,
where each set represents a complete excavation boundary polyline with X,Y coordinates.

The module follows the established patterns from anchor geometry configuration
and integrates with the excel_master data structure for default value loading.
"""

import logging
import tkinter as tk
from tkinter import messagebox, ttk
from typing import Dict, List, Optional, Any

# Import shared utilities and constants
from ..common import (
    FONT_BOLD, ACCENT_COLOR, DEFAULT_WINDOW_SIZES, create_modal_window, create_scrollable_content_area,
    create_standard_frame, create_info_label, create_header_label,
    create_status_label, create_primary_button, create_success_button, create_danger_button,
    safe_float_conversion, validate_dataframe, find_coordinate_columns, create_default_stage_data, 
    convert_coordinates_to_polylines, bind_mouse_wheel_globally
)
from .models import ExcavationLevelUIComponents


def create_excavation_level_window(parent_window: tk.Widget) -> tk.Toplevel:
    """
    Create the main excavation level configuration window.
    
    Args:
        parent_window: The parent window
        
    Returns:
        The created excavation level window
    """
    return create_modal_window(parent_window, "Edit Excavation Level Coordinates", 
                              DEFAULT_WINDOW_SIZES["excavation"], resizable=True, center=True)


def create_stage_info_section(window: tk.Toplevel, ui_components: ExcavationLevelUIComponents,
                              selected_stage: Optional[str] = None) -> tk.Frame:
    """
    Create the stage information section.
    
    Args:
        window: The excavation level window
        ui_components: UI components manager
        selected_stage: The stage value selected in the parent window
        
    Returns:
        The created stage info frame
    """
    # Create stage info frame
    stage_info_frame = create_standard_frame(window)
    stage_info_frame.pack(fill="x", padx=20, pady=10)
    
    # Stage label
    stage_text = f"Configuring excavation levels for Stage: {selected_stage}" if selected_stage else "No stage selected"
    ui_components.stage_label = create_header_label(stage_info_frame, stage_text)
    ui_components.selected_stage = selected_stage
    
    return stage_info_frame


def create_stage_selection_ui(parent_frame: tk.Frame, ui_components: ExcavationLevelUIComponents,
                             selected_stage: Optional[str] = None) -> tk.Frame:
    """
    Create stage selection UI for excavation levels.
    
    Args:
        parent_frame: Parent frame to contain the stage selection UI
        ui_components: UI components manager
        selected_stage: Currently selected stage
        
    Returns:
        The created stage selection frame
    """
    stage_frame = create_standard_frame(parent_frame)
    stage_frame.pack(fill="x", padx=20, pady=10)
    
    # Stage selection label
    stage_label = create_info_label(stage_frame, f"Current Stage: {selected_stage or 'Not specified'}")
    
    return stage_frame


def create_action_buttons(window: tk.Toplevel, ui_components: ExcavationLevelUIComponents,
                         app_instance, selected_stage: Optional[str] = None) -> tk.Frame:
    """
    Create action buttons for excavation level configuration.
    
    Args:
        window: The excavation level window
        ui_components: UI components manager
        app_instance: The main application instance
        selected_stage: The stage value selected in the parent window
        
    Returns:
        The created action buttons frame
    """
    action_frame = create_standard_frame(window)
    action_frame.pack(fill="x", padx=20, pady=20)
    
    def load_defaults():
        """Load default excavation level values from Excel."""
        try:
            if not ui_components.excel_master:
                messagebox.showwarning("No Data", "No Excel data available for loading defaults.")
                return
            
            # Load default excavation level data
            load_default_excavation_values(ui_components, selected_stage)
            
            # Update status
            if ui_components.status_label:
                ui_components.status_label.config(text="Default values loaded successfully")
            
            logging.info("Loaded default excavation level values")
            
        except Exception as e:
            error_msg = f"Error loading defaults: {str(e)}"
            logging.error(error_msg)
            messagebox.showerror("Error", error_msg)
    
    def save_configuration():
        """Save excavation level configuration for the current design stage."""
        try:
            # Validate all stage data
            stage_data = ui_components.get_all_stage_data()
            
            # Here you would typically save to excel_master or configuration
            # For now, just validate the data structure
            if not selected_stage:
                messagebox.showerror("Error", "No design stage specified for saving.")
                return
            
            if selected_stage not in stage_data:
                messagebox.showerror("Error", f"No data found for stage {selected_stage}")
                return
            
            # Validate the stage data
            validate_excavation_stage_data(stage_data[selected_stage])
            
            # Store in app instance
            if not hasattr(app_instance, 'excavation_configuration'):
                app_instance.excavation_configuration = {}
            app_instance.excavation_configuration[selected_stage] = stage_data[selected_stage]
            
            # Update comprehensive configuration using the integrated system
            try:
                from ..config_details import update_excavation_level_config
                excavation_data = {'stage_data': stage_data}
                update_excavation_level_config(app_instance, excavation_data, selected_stage)
            except ImportError:
                logging.warning("Could not import update_excavation_level_config function")
            except Exception as e:
                logging.error(f"Error updating comprehensive configuration: {str(e)}")
            
            # Show success message and close window
            messagebox.showinfo("Success", f"Excavation level configuration saved for stage {selected_stage}!")
            window.destroy()
            
            logging.info(f"Saved excavation level configuration for stage {selected_stage}")
            
        except ValueError as e:
            messagebox.showerror("Validation Error", str(e))
        except Exception as e:
            error_msg = f"Error saving excavation level configuration: {str(e)}"
            logging.error(error_msg)
            messagebox.showerror("Error", error_msg)
    
    def cancel_configuration():
        """Cancel and close the window."""
        window.destroy()
    
    # Create action buttons
    create_primary_button(action_frame, "Load Defaults", load_defaults)
    create_success_button(action_frame, "Save Configuration", save_configuration)
    create_danger_button(action_frame, "Cancel", cancel_configuration)
    
    return action_frame


def load_default_excavation_values(ui_components: ExcavationLevelUIComponents, selected_stage: Optional[str]):
    """
    Load default excavation level values from Excel data.
    
    Args:
        ui_components: UI components manager
        selected_stage: The stage to load defaults for
    """
    try:
        if not ui_components.excel_master or not selected_stage:
            return
        
        # Try to get excavation level data from Excel
        if hasattr(ui_components.excel_master, 'excavation_level_df'):
            df = ui_components.excel_master.excavation_level_df
            
            # Validate dataframe
            validate_dataframe(df, "Excavation Level")
            
            # Find coordinate columns
            coord_columns = find_coordinate_columns(df)
            if not coord_columns:
                logging.warning("No coordinate columns found in excavation level data")
                return
            
            # Filter data for the selected stage
            stage_data = df[df['Stage'].astype(str) == str(selected_stage)]
            
            if stage_data.empty:
                logging.warning(f"No excavation level data found for stage {selected_stage}")
                return
            
            # Convert coordinates to polylines
            polylines = convert_coordinates_to_polylines(stage_data, coord_columns)
            
            # Clear existing stage and add the loaded data
            ui_components.clear_all_stages()
            stage_section = ui_components.add_stage_section(selected_stage)
            stage_section.set_values(polylines)
            
            logging.info(f"Loaded default excavation level values for stage {selected_stage}")
            
        else:
            logging.warning("No excavation level data available in Excel master")
            
    except Exception as e:
        logging.error(f"Error loading default excavation values: {str(e)}")
        raise


def validate_excavation_stage_data(stage_data: List[List[Dict[str, float]]]):
    """
    Validate excavation stage data structure.
    
    Args:
        stage_data: List of polyline sets to validate
        
    Raises:
        ValueError: If validation fails
    """
    if not stage_data:
        raise ValueError("No excavation data provided for validation")
    
    for i, polyline_set in enumerate(stage_data):
        if not polyline_set:
            raise ValueError(f"Polyline set {i+1} is empty")
        
        for j, point in enumerate(polyline_set):
            if 'X' not in point or 'Y' not in point:
                raise ValueError(f"Missing X or Y coordinate in polyline set {i+1}, point {j+1}")
            
            # Validate coordinate values
            try:
                float(point['X'])
                float(point['Y'])
            except (ValueError, TypeError):
                raise ValueError(f"Invalid coordinate values in polyline set {i+1}, point {j+1}")


def edit_excavation_level(app_instance, selected_stage: Optional[str] = None):
    """
    Main function to edit excavation level configuration.
    
    Args:
        app_instance: The main application instance
        selected_stage: The stage value selected in the parent window
    """
    try:
        logging.info(f"Starting excavation level configuration for stage {selected_stage}")
        
        # Create the excavation level window
        window = create_excavation_level_window(app_instance.root)
        
        # Create scrollable content area
        main_frame, canvas, content_frame = create_scrollable_content_area(window)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Initialize UI components manager
        ui_components = ExcavationLevelUIComponents()
        ui_components.content_frame = content_frame
        ui_components.excel_master = getattr(app_instance, 'excel_master', None)
        ui_components.selected_stage = selected_stage
        
        # Create stage info section
        create_stage_info_section(window, ui_components, selected_stage)
        
        # Create stage selection UI
        stage_frame = create_stage_selection_ui(content_frame, ui_components, selected_stage)
        
        # Add initial stage section if stage is specified
        if selected_stage:
            ui_components.add_stage_section(selected_stage)
        
        # Create action buttons
        action_frame = create_action_buttons(window, ui_components, app_instance, selected_stage)
        
        # Create status label
        ui_components.status_label = create_status_label(content_frame, "Ready to configure excavation levels")
        
        # Bind mouse wheel for scrolling
        bind_mouse_wheel_globally(window, canvas)
        
        # Focus the window
        window.focus_set()
        
    except Exception as e:
        error_msg = f"Error creating excavation level configuration window: {str(e)}"
        logging.error(error_msg)
        logging.exception("Detailed traceback:")
        messagebox.showerror("Error", "Unable to open excavation level configuration.")
