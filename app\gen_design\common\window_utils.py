"""
Window Utilities for Generative Design Module

This module contains utilities for creating and configuring windows
with consistent behavior across the generative design modules.
"""

import tkinter as tk
from tkinter import ttk
from typing import Optional, Dict, Any
from .ui_constants import *


# =============================================================================
# WINDOW CREATION UTILITIES
# =============================================================================

def apply_window_icon(window: tk.Toplevel, icon_path: str = APP_ICON_PATH):
    """
    Apply application icon to a window with error handling.
    
    Args:
        window: Window to apply icon to
        icon_path: Path to icon file
    """
    try:
        window.iconbitmap(icon_path)
    except Exception:
        # Silently ignore if icon isn't available
        pass


def setup_modal_window(window: tk.Toplevel, parent_window: tk.Widget):
    """
    Configure a window as modal (blocks interaction with parent).
    
    Args:
        window: Window to make modal
        parent_window: Parent window to block
    """
    window.transient(parent_window)
    window.grab_set()


def center_window_on_screen(window: tk.Toplevel):
    """
    Center a window on the screen.
    
    Args:
        window: Window to center
    """
    window.update_idletasks()
    width = window.winfo_width()
    height = window.winfo_height()
    x = (window.winfo_screenwidth() // 2) - (width // 2)
    y = (window.winfo_screenheight() // 2) - (height // 2)
    window.geometry(f'{width}x{height}+{x}+{y}')


def create_modal_window(parent_window: tk.Widget, title: str, 
                       geometry: str = None, resizable: bool = True,
                       center: bool = False, apply_icon: bool = True) -> tk.Toplevel:
    """
    Create a modal window with standard configuration.
    
    Args:
        parent_window: Parent window
        title: Window title
        geometry: Window geometry string (e.g., "800x600")
        resizable: Whether window should be resizable
        center: Whether to center window on screen
        apply_icon: Whether to apply application icon
        
    Returns:
        Configured modal window
    """
    window = tk.Toplevel(parent_window)
    window.title(title)
    
    if geometry:
        window.geometry(geometry)
    
    window.configure(bg=BG_COLOR)
    setup_modal_window(window, parent_window)
    
    if resizable:
        window.resizable(True, True)
    else:
        window.resizable(False, False)
    
    if apply_icon:
        apply_window_icon(window)
    
    if center:
        center_window_on_screen(window)
    
    return window


def create_geometry_window(parent_window: tk.Widget) -> tk.Toplevel:
    """
    Create the geometry editing window with standard configuration.
    
    Args:
        parent_window: The parent window
        
    Returns:
        Configured geometry window
    """
    return create_modal_window(
        parent_window,
        "Anchor Geometry Configuration", 
        DEFAULT_WINDOW_SIZES["geometry"],
        resizable=True,
        center=False
    )


def create_parameter_window(parent_window: tk.Widget, 
                           selected_stage: Optional[str] = None) -> tk.Toplevel:
    """
    Create the parameter editing window with standard configuration.
    
    Args:
        parent_window: The parent window
        selected_stage: The stage value selected in the parent window
        
    Returns:
        Configured parameter window
    """
    return create_modal_window(
        parent_window,
        "Anchor Parameters Configuration",
        DEFAULT_WINDOW_SIZES["parameters"],
        resizable=True,
        center=False
    )


def create_excavation_level_window(parent_window: tk.Widget) -> tk.Toplevel:
    """
    Create the excavation level configuration window with standard configuration.
    
    Args:
        parent_window: The parent window
        
    Returns:
        Configured excavation level window
    """
    window = create_modal_window(
        parent_window,
        "Edit Excavation Level Coordinates",
        DEFAULT_WINDOW_SIZES["excavation"],
        resizable=True,
        center=True  # This window centers itself
    )
    return window


def create_main_configuration_window(parent_window: tk.Widget) -> tk.Toplevel:
    """
    Create the main configuration window.
    
    Args:
        parent_window: The parent window
        
    Returns:
        Configured main configuration window
    """
    window = create_modal_window(
        parent_window,
        "ELS Generative Design Configuration",
        DEFAULT_WINDOW_SIZES["main_config"],
        resizable=True,
        center=False
    )
    
    # Set minimum size for main config window
    window.minsize(800, 500)
    
    return window


# =============================================================================
# STAGE SELECTION UTILITIES
# =============================================================================

def create_stage_selection_frame(parent: tk.Widget, stage_options: list, 
                                 initial_selection: int = 0) -> Dict[str, Any]:
    """
    Create a stage selection frame with dropdown.
    
    Args:
        parent: Parent widget
        stage_options: List of stage options
        initial_selection: Index of initial selection
        
    Returns:
        Dictionary containing frame, stage_var, and dropdown widgets
    """
    from .ui_components import create_standard_frame, create_info_label
    
    frame = create_standard_frame(parent)
    frame.pack(fill="x", padx=20, pady=15)

    label = create_info_label(frame, "Select ELS Stage:", font=FONT_BOLD)
    label.pack(side=tk.LEFT, padx=5)
    
    stage_var = tk.StringVar(parent)
    
    # Apply styling to combobox
    style = ttk.Style()
    style.configure('TCombobox', padding=(5, 7), font=FONT_REGULAR)
    
    dropdown = ttk.Combobox(frame, textvariable=stage_var, 
                           values=stage_options, width=10, 
                           font=FONT_REGULAR, style='TCombobox',
                           state="readonly")
    dropdown.pack(side=tk.LEFT, padx=5)
    
    # Set default selection
    if stage_options and 0 <= initial_selection < len(stage_options):
        dropdown.current(initial_selection)
    
    return {
        "frame": frame, 
        "stage_var": stage_var, 
        "dropdown": dropdown
    }


def create_stage_info_display(parent: tk.Widget, selected_stage: Optional[str] = None,
                             show_dependency_status: bool = False) -> Dict[str, tk.Label]:
    """
    Create stage information display with labels.
    
    Args:
        parent: Parent widget
        selected_stage: Currently selected stage
        show_dependency_status: Whether to show dependency status
        
    Returns:
        Dictionary of label widgets
    """
    from .ui_components import create_standard_frame, create_info_label, create_header_label
    
    info_frame = create_standard_frame(parent)
    info_frame.pack(fill="x", padx=20, pady=15)
    
    # Header label
    header = create_header_label(info_frame, "Configuration")
    header.pack(pady=(0, 10))
    
    # Stage info frame
    stage_info_frame = create_standard_frame(info_frame)
    stage_info_frame.pack(fill="x")
    
    stage_label_text = create_info_label(stage_info_frame, "Current Design Stage:", 
                                        font=FONT_BOLD)
    stage_label_text.pack(side=tk.LEFT, padx=5)
    
    stage_value = selected_stage if selected_stage else "Not specified"
    stage_label = create_info_label(stage_info_frame, stage_value, fg_color=ACCENT_COLOR)
    stage_label.pack(side=tk.LEFT, padx=5)
    
    labels = {
        "header": header,
        "stage_label": stage_label
    }
    
    if show_dependency_status:
        dependency_label = create_info_label(stage_info_frame, 
                                           "✓ Dependencies satisfied", 
                                           fg_color=SUCCESS_COLOR)
        dependency_label.pack(side=tk.LEFT, padx=20)
        labels["dependency_label"] = dependency_label
    
    return labels


# =============================================================================
# WINDOW SIZE MANAGEMENT
# =============================================================================

def adjust_window_size_for_anchor_type(window: tk.Toplevel, anchor_type: str):
    """
    Adjust window size based on anchor type for parameter windows.
    
    Args:
        window: Window to resize
        anchor_type: Anchor type (N, F, G)
    """
    if anchor_type in PARAM_WINDOW_GEOMETRY:
        window.geometry(PARAM_WINDOW_GEOMETRY[anchor_type])


def set_minimum_window_size(window: tk.Toplevel, min_width: int, min_height: int):
    """
    Set minimum window size.
    
    Args:
        window: Window to configure
        min_width: Minimum width
        min_height: Minimum height
    """
    window.minsize(min_width, min_height)


# =============================================================================
# WINDOW STATE MANAGEMENT
# =============================================================================

def store_window_reference(app_instance: Any, window_name: str, window: tk.Toplevel):
    """
    Store window reference in app instance for later access.
    
    Args:
        app_instance: Application instance
        window_name: Name to store window under
        window: Window to store
    """
    if not hasattr(app_instance, '_windows'):
        app_instance._windows = {}
    app_instance._windows[window_name] = window


def get_stored_window(app_instance: Any, window_name: str) -> Optional[tk.Toplevel]:
    """
    Get stored window reference.
    
    Args:
        app_instance: Application instance
        window_name: Name of window to retrieve
        
    Returns:
        Window instance or None if not found
    """
    if hasattr(app_instance, '_windows'):
        return app_instance._windows.get(window_name)
    return None


def cleanup_window_reference(app_instance: Any, window_name: str):
    """
    Clean up stored window reference.
    
    Args:
        app_instance: Application instance
        window_name: Name of window to remove
    """
    if hasattr(app_instance, '_windows') and window_name in app_instance._windows:
        del app_instance._windows[window_name]


# =============================================================================
# DIALOG UTILITIES
# =============================================================================

def create_confirmation_dialog(parent: tk.Widget, title: str, message: str,
                              ok_callback: callable = None, 
                              cancel_callback: callable = None) -> tk.Toplevel:
    """
    Create a confirmation dialog with OK/Cancel buttons.
    
    Args:
        parent: Parent widget
        title: Dialog title
        message: Dialog message
        ok_callback: Callback for OK button
        cancel_callback: Callback for Cancel button
        
    Returns:
        Dialog window
    """
    from .ui_components import create_info_label, create_success_button, create_secondary_button
    
    dialog = create_modal_window(parent, title, "400x150", 
                                resizable=False, center=True)
    
    # Message label
    msg_label = create_info_label(dialog, message)
    msg_label.pack(pady=20)
    
    # Buttons frame
    buttons_frame = tk.Frame(dialog, bg=BG_COLOR)
    buttons_frame.pack(side=tk.BOTTOM, pady=10)
    
    def ok_clicked():
        if ok_callback:
            ok_callback()
        dialog.destroy()
    
    def cancel_clicked():
        if cancel_callback:
            cancel_callback()
        dialog.destroy()
    
    create_success_button(buttons_frame, "OK", ok_clicked, width=10, height=1)
    create_secondary_button(buttons_frame, "Cancel", cancel_clicked, width=10, height=1)
    
    return dialog