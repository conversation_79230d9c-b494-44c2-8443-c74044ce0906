"""
PLAXIS Geometry Package

This package contains modules for PLAXIS geometry operations including
drawing structures, getting geometry elements, material properties, and utilities.
"""

# Drawing functions
from .draw import (
    draw_plate,
    draw_anchor_n,
    draw_anchor_f,
    draw_embeddedbeam,
    draw_el,
    draw_line_load,
    draw_point_load,
    draw_boundary,
    draw_structure_polygon
)

# Getting geometry elements
from .get import (
    get_line,
    get_point,
    get_NodeToNodeAnchor,
    get_fixedEndAnchor,
    get_EmbeddedBeam
)

# Material properties
from .material import (
    add_plate_prop,
    add_anchor_prop,
    add_eb_prop
)

# Utilities
from .utils import cal_plate_prop

__all__ = [
    # Drawing functions
    'draw_plate',
    'draw_anchor_n',
    'draw_anchor_f', 
    'draw_embeddedbeam',
    'draw_el',
    'draw_line_load',
    'draw_point_load',
    'draw_boundary',
    'draw_structure_polygon',
    
    # Getting functions
    'get_line',
    'get_point',
    'get_NodeToNodeAnchor',
    'get_fixedEndAnchor',
    'get_EmbeddedBeam',
    # Material functions
    'add_plate_prop',
    'add_anchor_prop',
    'add_eb_prop',
    
    # Utilities
    'cal_plate_prop'
]