"""
Excavation Level Configuration Module

This module provides UI components for configuring excavation level coordinates
organized by excavation stages. Each stage can contain multiple polyline sets,
where each set represents a complete excavation boundary polyline with X,Y coordinates.

The module follows the established patterns from anchor geometry configuration
and integrates with the excel_master data structure for default value loading.
"""

import logging
import tkinter as tk
from tkinter import messagebox, ttk
from typing import Dict, List, Optional, Any

# Import shared utilities and constants
from .common import (
    FONT_BOLD, ACCENT_COLOR, DEFAULT_WINDOW_SIZES, create_modal_window, create_scrollable_content_area,
    create_standard_frame, create_info_label, create_header_label,
    create_status_label, create_primary_button, create_success_button, create_danger_button,
    safe_float_conversion, validate_dataframe, find_coordinate_columns, create_default_stage_data, 
    convert_coordinates_to_polylines, bind_mouse_wheel_globally
)
from .excavation.models import ExcavationLevelUIComponents


# Configuration constants


def create_excavation_level_window(parent_window: tk.Widget) -> tk.Toplevel:
    """
    Create the main excavation level configuration window.
    
    Args:
        parent_window: The parent window
        
    Returns:
        The created excavation level window
    """
    return create_modal_window(parent_window, "Edit Excavation Level Coordinates", 
                              DEFAULT_WINDOW_SIZES["excavation"], resizable=True, center=True)




def create_stage_info_section(window: tk.Toplevel, ui_components: ExcavationLevelUIComponents,
                              selected_stage: Optional[str] = None) -> tk.Frame:
    """
    Create the stage information section.
    
    Args:
        window: The excavation level window
        ui_components: UI components manager
        selected_stage: The selected stage to display
        
    Returns:
        The frame containing the stage info UI
    """
    info_frame = create_standard_frame(window)
    info_frame.pack(fill="x", padx=20, pady=15)
    
    # Header label
    header_label = create_header_label(info_frame, "Excavation Level Coordinate Configuration")
    header_label.pack(pady=(0, 10))
    
    # Note about single stage configuration
    note_label = create_info_label(info_frame, "Configure excavation level coordinates for the current design stage only", 
                                  fg_color="#666666")
    note_label.pack(pady=(0, 5))
    
    # Current stage information label
    stage_info_frame = create_standard_frame(info_frame)
    stage_info_frame.pack(fill="x")
    
    create_info_label(stage_info_frame, "Current Design Stage:", font=FONT_BOLD).pack(side=tk.LEFT, padx=5)
    
    stage_value = selected_stage if selected_stage else "Not specified"
    ui_components.stage_label = create_info_label(stage_info_frame, stage_value, 
                                                 fg_color=ACCENT_COLOR)
    ui_components.stage_label.pack(side=tk.LEFT, padx=5)
    
    # Dependency status label
    ui_components.dependency_label = create_info_label(stage_info_frame, 
                                                      "✓ Anchor geometry dependency satisfied",
                                                      fg_color="#4CAF50")
    ui_components.dependency_label.pack(side=tk.LEFT, padx=20)
    
    # Status label for operations
    ui_components.status_label = create_status_label(info_frame)
    ui_components.status_label.pack(pady=5)
    
    return info_frame


def create_excavation_level_input_area(window: tk.Toplevel, ui_components: ExcavationLevelUIComponents) -> tk.Frame:
    """
    Create the excavation level input area with scrollable content.
    
    Args:
        window: The excavation level window
        ui_components: UI components manager
        
    Returns:
        The frame containing the input area
    """
    main_frame, canvas, scrollable_frame = create_scrollable_content_area(window)
    
    # Store reference to content frame and canvas
    ui_components.content_frame = scrollable_frame
    ui_components.canvas = canvas
    
    # Enable global mouse wheel scrolling across the entire window
    bind_mouse_wheel_globally(window, canvas)
    
    # Store the binding function for later use when new widgets are added
    ui_components.bind_mouse_wheel_to_new_widgets = lambda widget: bind_mouse_wheel_globally(widget, canvas)
    
    return main_frame


def create_el_action_buttons(window: tk.Toplevel, ui_components: ExcavationLevelUIComponents,
                             app_instance, selected_stage: Optional[str] = None) -> tk.Frame:
    """
    Create action buttons for the excavation level window.
    
    Args:
        window: The excavation level window
        ui_components: UI components manager
        app_instance: The main application instance
        
    Returns:
        The frame containing the action buttons
    """
    buttons_frame = create_standard_frame(window)
    buttons_frame.pack(fill="x", padx=20, pady=15)
    
    def load_defaults():
        """Load default values from excel_master.EL dataframe for the current design stage."""
        try:
            if not hasattr(app_instance, 'excel_master') or app_instance.excel_master is None:
                messagebox.showwarning("Data Not Available", 
                                     "Excel data not loaded. Please load input files first.")
                return
            
            if not hasattr(app_instance.excel_master, 'EL'):
                messagebox.showwarning("Data Not Available",
                                     "EL worksheet not found in excel data.")
                return
            
            if not selected_stage:
                messagebox.showwarning("Stage Not Selected",
                                     "No design stage selected. Please select a stage first.")
                return
            
            # Get available stages from anchor data to validate selection
            stage_options = get_dynamic_stage_options(app_instance.excel_master)
            
            # Validate that the selected stage exists in available options
            if selected_stage not in stage_options:
                messagebox.showwarning("Invalid Stage",
                                     f"Selected stage '{selected_stage}' not found in anchor data.\n"
                                     f"Available stages: {', '.join(stage_options)}")
                return
            
            # Parse excavation level data for all stages (but only use current stage)
            el_data = parse_excavation_level_data(app_instance.excel_master.EL, stage_options)
            
            # Ensure stage section exists before loading data
            if selected_stage not in ui_components.stage_sections:
                # Create stage section if it doesn't exist
                logging.info(f"Creating stage section for {selected_stage}")
                ui_components.add_stage_section(selected_stage)
                
                # Enable mouse wheel scrolling for the newly added stage section
                if hasattr(ui_components, 'bind_mouse_wheel_to_new_widgets'):
                    stage_section = ui_components.stage_sections.get(selected_stage)
                    if stage_section and hasattr(stage_section, 'frame'):
                        ui_components.bind_mouse_wheel_to_new_widgets(stage_section.frame)
            
            # Load data for the current design stage
            stage_section = ui_components.stage_sections[selected_stage]
            if selected_stage in el_data and el_data[selected_stage]:
                # Convert flat coordinate list to polyline sets
                coordinates = el_data[selected_stage]
                logging.info(f"Loading {len(coordinates)} coordinates for stage {selected_stage}: {coordinates}")
                
                polyline_data = convert_coordinates_to_polylines(coordinates)
                logging.info(f"Converted to polyline data: {polyline_data}")
                
                stage_section.set_values(polyline_data)
                ui_components.status_label.configure(text=f"* Default values loaded for Stage {selected_stage} ({len(coordinates)} points)")
                logging.info(f"Successfully loaded {len(coordinates)} coordinates for stage {selected_stage}")
            else:
                # Clear existing data if no defaults found
                stage_section.clear_all_sets()
                ui_components.status_label.configure(text=f"! No default data found for Stage {selected_stage}")
                logging.warning(f"No valid data found in el_data for stage {selected_stage}. Available stages: {list(el_data.keys())}")
            
            ui_components.dependency_label.configure(text="* Using anchor geometry stages")
            
            logging.info(f"Excavation level default values loaded for stage {selected_stage}")
            
        except Exception as e:
            error_msg = f"Error loading default values: {str(e)}"
            logging.error(error_msg)
            messagebox.showerror("Error", error_msg)
    
    def save_configuration():
        """Save excavation level configuration for the current design stage."""
        try:
            # Validate all stage data
            stage_data = ui_components.get_all_stage_data()
            
            # Here you would typically save to excel_master or configuration
            # For now, just validate the data structure
            if not selected_stage:
                messagebox.showerror("Error", "No design stage specified for saving.")
                return
            
            if selected_stage not in stage_data:
                messagebox.showerror("Error", f"No data found for stage {selected_stage}")
                return
            
            current_stage_data = stage_data[selected_stage]
            total_polylines = len(current_stage_data)
            total_points = sum(len(points) for points in current_stage_data)
            
            success_msg = (f"Configuration saved successfully for Stage {selected_stage}!\n\n"
                          f"Polylines: {total_polylines}\n"
                          f"Total Points: {total_points}")
            
            ui_components.status_label.configure(text=f"* Stage {selected_stage} configuration saved")
            
            # Update comprehensive configuration using the integrated system
            if app_instance:
                try:
                    from app.gen_design.config_details import update_excavation_level_config
                    excavation_data = {'stage_data': stage_data}
                    update_excavation_level_config(app_instance, excavation_data, selected_stage)
                except ImportError:
                    logging.warning("Could not import update_excavation_level_config function")
                except Exception as e:
                    logging.error(f"Error updating comprehensive configuration: {str(e)}")
            
            messagebox.showinfo("Success", success_msg)
            
            logging.info(f"Excavation level configuration saved for stage {selected_stage}: "
                        f"{total_polylines} polylines, {total_points} points")
            
            # Close the window after successful save
            window.destroy()
            
        except ValueError as e:
            messagebox.showerror("Validation Error", str(e))
        except Exception as e:
            error_msg = f"Error saving configuration: {str(e)}"
            logging.error(error_msg)
            messagebox.showerror("Error", error_msg)
    
    def cancel_operation():
        """Cancel and close the window."""
        window.destroy()
    
# Load Defaults button
    load_btn = create_primary_button(buttons_frame, "Load Default Values", load_defaults,
                                    width=18, height=2, font=FONT_BOLD)
    
    # Save button
    save_btn = create_success_button(buttons_frame, "Save", save_configuration,
                                    width=15, height=2, font=FONT_BOLD)
    
    # Cancel button
    cancel_btn = create_danger_button(buttons_frame, "Cancel", cancel_operation,
                                     width=10, height=2, font=FONT_BOLD, pack_side=tk.RIGHT)
    
    return buttons_frame


def get_dynamic_stage_options(excel_master: Any) -> List[str]:
    """
    Extract stage options from anchor data (matching _GenDesignFrame pattern).
    
    Args:
        excel_master: ExcelMaster object containing anchor data
        
    Returns:
        List of stage options as strings
    """
    stage_options = ["0", "1", "2"]  # Default fallback values
    try:
        if hasattr(excel_master, 'AnchorLoc') and 'Stage' in excel_master.AnchorLoc.columns:
            stage_options = sorted(excel_master.AnchorLoc['Stage'].unique().tolist())
            # Convert any non-string values to strings
            stage_options = [str(option) for option in stage_options]
            logging.info(f"Dynamic stage options extracted: {stage_options}")
        else:
            logging.warning("AnchorLoc data not found, using default stage options")
    except Exception as e:
        logging.error(f"Error extracting dynamic stage options: {e}")
    
    return stage_options


def parse_excavation_level_data(el_dataframe, stage_options: List[str]) -> Dict[str, List[Dict[str, float]]]:
    """
    Parse excavation level data from the EL dataframe.
    
    Args:
        el_dataframe: DataFrame containing excavation level data
        stage_options: List of available stage options
        
    Returns:
        Dictionary mapping stage names to lists of coordinate dictionaries
    """
    try:
        if not validate_dataframe(el_dataframe, "EL"):
            return create_default_stage_data(stage_options)
        
        # Log dataframe info for debugging
        logging.info(f"EL dataframe shape: {el_dataframe.shape}")
        logging.info(f"EL dataframe columns: {list(el_dataframe.columns)}")
        logging.info(f"EL dataframe head:\n{el_dataframe.head()}")
        
        stage_data = {}
        
        # Group by stage and extract X,Y coordinates
        if 'Stage' not in el_dataframe.columns:
            logging.warning("No 'Stage' column found in EL dataframe")
            logging.warning(f"Available columns: {list(el_dataframe.columns)}")
            return create_default_stage_data(stage_options)
        
        # Find coordinate columns once
        x_col, y_col = find_coordinate_columns(el_dataframe)
        
        if not x_col or not y_col:
            logging.warning(f"No suitable X,Y columns found")
            logging.warning(f"Available columns: {list(el_dataframe.columns)}")
            return create_default_stage_data(stage_options)
        
        logging.info(f"Using columns: X='{x_col}', Y='{y_col}'")
        
        for stage in stage_options:  # Use dynamic stage options
            stage_str = str(stage)  # Ensure stage is string for comparison
            stage_rows = el_dataframe[el_dataframe['Stage'].astype(str) == stage_str]
            coordinates = []
            
            logging.info(f"Processing stage {stage_str}: found {len(stage_rows)} rows")
            
            if not stage_rows.empty:
                for idx, row in stage_rows.iterrows():
                    try:
                        x_val = safe_float_conversion(row[x_col])
                        y_val = safe_float_conversion(row[y_col])
                        
                        coordinate_point = {"X": x_val, "Y": y_val}
                        coordinates.append(coordinate_point)
                        logging.info(f"Added coordinate for stage {stage_str}: {coordinate_point}")
                        
                    except (KeyError) as e:
                        logging.warning(f"Skipping invalid coordinate pair in stage {stage_str}, row {idx}: {str(e)}")
                        continue
            else:
                logging.info(f"No data found for stage {stage_str}")
            
            # Ensure each stage has at least one coordinate point
            if not coordinates:
                coordinates = [{"X": 0.0, "Y": 0.0}]
                logging.info(f"No valid coordinates found for stage {stage_str}, using default (0,0)")
            
            stage_data[stage] = coordinates
            logging.info(f"Final coordinates for stage {stage_str}: {coordinates}")
        
        logging.info(f"Final parsed stage data: {stage_data}")
        return stage_data
        
    except Exception as e:
        logging.error(f"Error parsing excavation level data: {str(e)}")
        import traceback
        logging.error(f"Full traceback: {traceback.format_exc()}")
        # Return default structure if parsing fails
        return create_default_stage_data(stage_options)





def check_anchor_geometry_dependency(app_instance) -> bool:
    """
    Check if anchor geometry has been properly configured following the anchor parameter pattern.
    
    Args:
        app_instance: The main application instance
        
    Returns:
        True if anchor geometry is available, False otherwise
    """
    try:
        # Check if geometry_configuration exists (following anchor_param pattern)
        if not hasattr(app_instance, 'geometry_configuration') or not app_instance.geometry_configuration:
            logging.warning("geometry_configuration not found or empty")
            return False
        
        # Check if excel_master exists and has AnchorLoc data
        if not hasattr(app_instance, 'excel_master') or app_instance.excel_master is None:
            logging.warning("excel_master not found")
            return False
        
        if not hasattr(app_instance.excel_master, 'AnchorLoc'):
            logging.warning("AnchorLoc not found in excel_master")
            return False
        
        # Check if AnchorLoc has meaningful data
        if app_instance.excel_master.AnchorLoc.empty:
            logging.warning("AnchorLoc dataframe is empty")
            return False
        
        logging.info("Anchor geometry dependency check passed")
        return True
        
    except Exception as e:
        logging.error(f"Error checking anchor geometry dependency: {e}")
        return False


def load_saved_excavation_level_data(ui_components: ExcavationLevelUIComponents, app_instance,
                                     selected_stage: Optional[str] = None) -> bool:
    """
    Load previously saved excavation level configuration data into UI components.
    
    This function checks for existing excavation level configurations in the app instance
    and populates the UI components with the saved values for the specified stage.
    
    Args:
        ui_components: UI components manager
        app_instance: The application instance containing saved configurations
        selected_stage: The current design stage to load data for
        
    Returns:
        bool: True if data was successfully loaded, False otherwise
    """
    try:
        if not app_instance:
            logging.info("No app instance provided")
            return False
            
        if not hasattr(app_instance, 'comprehensive_config'):
            logging.info("No comprehensive configuration found in app instance, initializing...")
            # Initialize comprehensive config if it doesn't exist
            try:
                from .common import initialize_comprehensive_config
                app_instance.comprehensive_config = initialize_comprehensive_config()
            except ImportError:
                logging.error("Could not import initialize_comprehensive_config")
                return False
        
        if not selected_stage:
            logging.info("No stage specified for loading excavation level data")
            return False
        
        config = app_instance.comprehensive_config
        excavation_config = config.get('excavation_level', {})
        
        if not excavation_config.get('configured', False):
            logging.info("No saved excavation level configuration found")
            return False
        
        # Check stage matching
        saved_stage = excavation_config.get('stage')
        if saved_stage and saved_stage != selected_stage:
            logging.info(f"Saved excavation configuration stage mismatch. "
                        f"Selected: {selected_stage}, Saved: {saved_stage}")
            return False
        
        # Load stage data
        stage_data = excavation_config.get('stage_data', {})
        if selected_stage not in stage_data:
            logging.info(f"No saved data found for stage {selected_stage}")
            return False
        
        current_stage_data = stage_data[selected_stage]
        if not current_stage_data:
            logging.info(f"Empty data found for stage {selected_stage}")
            return False
        
        # Ensure the stage section exists
        if selected_stage not in ui_components.stage_sections:
            ui_components.add_stage_section(selected_stage)
        
        stage_section = ui_components.stage_sections[selected_stage]
        
        # Complete reset - remove all existing sets and their UI elements
        stage_section.remove_all_sets()
        
        # Load the saved polyline sets
        polyline_count = 0
        total_points = 0
        
        # Convert the saved coordinate data to polyline sets
        # The saved data is a list of coordinate lists (each list is a polyline)
        if isinstance(current_stage_data, list):
            for polyline_index, polyline_data in enumerate(current_stage_data):
                if isinstance(polyline_data, list) and polyline_data:
                    logging.info(f"Loading polyline {polyline_index + 1} with {len(polyline_data)} points")
                    
                    # Add a new polyline set (starts fresh with no initial points due to complete reset)
                    stage_section.add_polyline_set()
                    
                    # Get the latest polyline set
                    if stage_section.polyline_sets:
                        latest_polyline = stage_section.polyline_sets[-1]
                        
                        # Remove the default point created by add_polyline_set
                        latest_polyline.remove_all_points()
                        
                        # Add coordinate points to this polyline set
                        for point_index, point_data in enumerate(polyline_data):
                            if isinstance(point_data, dict) and 'X' in point_data and 'Y' in point_data:
                                logging.info(f"Adding point {point_index + 1} to polyline {polyline_index + 1}: X={point_data['X']}, Y={point_data['Y']}")
                                
                                # Add a coordinate point
                                latest_polyline.add_coordinate_point()
                                
                                # Set the coordinate values for the latest added point
                                if latest_polyline.coordinate_points:
                                    latest_point = latest_polyline.coordinate_points[-1]
                                    if hasattr(latest_point, 'set_values'):
                                        latest_point.set_values({
                                            'X': point_data['X'],
                                            'Y': point_data['Y']
                                        })
                                        logging.info(f"Successfully set values for point {point_index + 1} in polyline {polyline_index + 1}")
                                total_points += 1
                        
                        polyline_count += 1
        
        # Update button states
        stage_section.update_button_states()
        
        if polyline_count > 0:
            logging.info(f"Successfully loaded excavation level data for stage {selected_stage}: "
                        f"{polyline_count} polylines, {total_points} points")
            
            # Update status if available
            if hasattr(ui_components, 'status_label') and ui_components.status_label:
                ui_components.status_label.configure(
                    text=f"✓ Previously saved configuration loaded: {polyline_count} polylines, {total_points} points",
                    fg="#4CAF50"
                )
            
            # Update dependency status
            if hasattr(ui_components, 'dependency_label') and ui_components.dependency_label:
                ui_components.dependency_label.configure(
                    text=f"✓ Loaded saved excavation level for Stage {selected_stage}",
                    fg="#4CAF50"
                )
            
            return True
        else:
            logging.info("No valid polyline data found in saved configuration")
            # Ensure stage has at least one polyline set for user interaction
            if not stage_section.polyline_sets:
                stage_section.add_polyline_set()
                logging.info(f"Added initial polyline set to stage {selected_stage}")
            return False
        
    except Exception as e:
        logging.error(f"Error loading saved excavation level data: {str(e)}")
        # Ensure stage has at least one polyline set for user interaction even if loading failed
        try:
            if selected_stage in ui_components.stage_sections:
                stage_section = ui_components.stage_sections[selected_stage]
                if not stage_section.polyline_sets:
                    stage_section.add_polyline_set()
                    logging.info(f"Added fallback polyline set to stage {selected_stage} after load failure")
        except Exception as fallback_error:
            logging.error(f"Failed to add fallback polyline set: {str(fallback_error)}")
        return False

def edit_excavation_level(self, anchor_table: ttk.Treeview, progress_window: tk.Widget, 
                         selected_stage: Optional[str] = None) -> None:
    """
    Open a new window to edit excavation level coordinates for the current design stage.
    
    This function creates a modal window for configuring excavation level
    coordinates for the specified design stage. Each stage can contain multiple
    polyline sets, where each set represents a complete excavation boundary.
    
    Args:
        self: The application instance
        anchor_table: The anchor table widget (for consistency with other edit functions)
        progress_window: The parent window
        selected_stage: The current design stage to configure (required)
        
    Returns:
        None
        
    Raises:
        Exception: If there are errors in UI creation or data processing
    """
    try:
        # Create excavation level window
        el_window = create_excavation_level_window(progress_window)
        
        # Initialize UI components manager
        ui_components = ExcavationLevelUIComponents()
        
        # Create UI sections with enhanced error handling
        try:
            create_stage_info_section(el_window, ui_components, selected_stage)
            create_excavation_level_input_area(el_window, ui_components)
            create_el_action_buttons(el_window, ui_components, self, selected_stage)
        except Exception as ui_error:
            logging.error(f"Error creating UI sections: {ui_error}")
            el_window.destroy()
            raise
        
        # Initialize with single stage section for the current design stage
        try:
            # Validate that selected_stage is provided
            if not selected_stage:
                messagebox.showerror(
                    "Stage Required",
                    "No design stage specified. Please select a design stage before editing excavation levels."
                )
                el_window.destroy()
                return
            
            # Get available stages from anchor data to validate selection
            stage_options = get_dynamic_stage_options(self.excel_master)
            
            # Validate that the selected stage exists in available options
            if selected_stage not in stage_options:
                messagebox.showerror(
                    "Invalid Stage",
                    f"Selected stage '{selected_stage}' not found in anchor geometry data.\n\n"
                    f"Available stages: {', '.join(stage_options)}\n\n"
                    f"Please configure anchor geometry first or select a valid stage."
                )
                el_window.destroy()
                return
            
            # Try to load previously saved configuration first
            saved_data_loaded = load_saved_excavation_level_data(ui_components, self, selected_stage)
            
            if not saved_data_loaded:
                # If no saved data found, create empty stage section for the current design stage
                ui_components.add_stage_section(selected_stage)
                ui_components._saved_data_loaded = False
            else:
                # Mark that saved data was loaded
                ui_components._saved_data_loaded = True
            
            # Enable mouse wheel scrolling for the newly added stage section
            if hasattr(ui_components, 'bind_mouse_wheel_to_new_widgets'):
                # Find the newly added stage section and bind mouse wheel events
                stage_section = ui_components.stage_sections.get(selected_stage)
                if stage_section and hasattr(stage_section, 'frame'):
                    ui_components.bind_mouse_wheel_to_new_widgets(stage_section.frame)
            
            # Update dependency status (only if no saved data was loaded)
            if not hasattr(ui_components, '_saved_data_loaded') or not ui_components._saved_data_loaded:
                ui_components.dependency_label.configure(
                    text=f"* Configuring excavation level for Stage {selected_stage}",
                    fg="#4CAF50"
                )
            
        except Exception as stage_error:
            logging.error(f"Error initializing stage section: {stage_error}")
            messagebox.showerror(
                "Initialization Error",
                f"Failed to initialize excavation level configuration:\n{str(stage_error)}"
            )
            el_window.destroy()
            return
        
        logging.info(f"Excavation level configuration window created successfully")
        
    except Exception as e:
        error_msg = f"Error creating excavation level window: {str(e)}"
        logging.error(error_msg)
        messagebox.showerror("Error", error_msg)