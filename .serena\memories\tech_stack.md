# Tech Stack and Dependencies

## Programming Language
- **Python 3.6+** (minimum requirement for compatibility)
- **Recommended: Python 3.8+** for optimal performance and feature support

## Core Dependencies (requirements.txt)

### GUI Framework
- **tkinter** - Main GUI framework (built-in with Python)
- **ttk** - Themed tkinter widgets for modern UI components

### Scientific Computing
- **matplotlib** - Plotting and visualization for results and charts
- **plxscripting** - Official PLAXIS scripting interface for automation
- **scipy** - Scientific computing library for advanced operations
- **numpy** - Numerical computing for calculations
- **pandas** - Data manipulation and analysis for Excel processing

### File Processing
- **openpyxl** - Excel file reading/writing (.xlsx format)
- **shapely** - Geometric operations and spatial analysis

### Build and Deployment
- **nuitka** - Python-to-executable compilation for Windows deployment

### Standard Libraries Used
- **logging** - Comprehensive logging throughout application
- **threading** - Background tasks and session monitoring
- **tkinter.filedialog** - File selection dialogs
- **datetime** - Time/date operations for sessions and logging
- **json** - Configuration and data storage
- **os, sys** - System operations and path handling
- **pathlib** - Modern path handling for cross-platform compatibility
- **typing** - Type hints for better code clarity and IDE support

## Architecture and Design Patterns

### Advanced Component-Based UI Architecture
- **Component Hierarchy**: Sophisticated UI component system with managers and specialized components
- **Apple-Inspired Design System**: Consistent color palette, typography, and modern UI principles
- **Dynamic Component Generation**: UI elements created based on data and user selections
- **Reusable Components**: `CoordinateSet`, `AnchorSection`, `GeometryUIComponents`, etc.

### Advanced UI Patterns
- **Modular Component Design**: Each component manages its own UI, validation, and state
- **Type-Specific Components**: Different components for different anchor types (e.g., `GTypeCoordinateSet`)
- **Scrollable Content Areas**: Canvas-based scrolling for complex forms with many components
- **Modal Dialog Management**: Proper parent-child window relationships
- **Dynamic State Management**: Components sync with paired components automatically

### Data Management Patterns
- **Excel Integration**: Multi-sheet Excel processing with validation and default value loading
- **Configuration Management**: Centralized configuration using `version_config.py` (v1.8.1)
- **Data Validation**: Multi-level validation with user-friendly error messages
- **Type Safety**: Extensive use of dataclasses and type hints

## Development Environment

### Primary Platform
- **Windows** - Primary target platform with PowerShell commands
- **VS Code** - Recommended IDE with Serena MCP integration (`.serena/` configuration)

### Version Control
- **Git** - Source control with feature branch workflow (currently on developing_v1.8.2)
- **GitHub** - Repository hosting with automated workflows (`.github/` directory)

## External Integrations

### PLAXIS Software Integration
- **PLAXIS 2D/3D** - Primary geotechnical analysis software
- **plxscripting API** - Direct integration for model building and analysis
- **PLAXIS Output** - Result extraction and processing

### Email and Authentication
- **Gmail SMTP** - Email integration for authentication and logging
- **Google Drive** - License file hosting and distribution

### Data Sources
- **Excel Files** - Primary input method for engineering data with sophisticated parsing
- **Steel Section Library** - External steel section database integration (`Library_Steel/ELS_Steel_Section.xlsx`)

## Build and Deployment

### Application Packaging
- **Nuitka** - Python-to-executable compilation (nuitka embed.txt configuration)
- **Windows Executable** - Standalone .exe generation for distribution

### Dependencies Management
- **requirements.txt** - Complete dependency specification with all required packages
- **Virtual Environment** - Isolated Python environment for development

## Advanced UI Technology Stack

### Component-Based Architecture
- **GeometryUIComponents** - Main UI component manager for anchor geometry
- **AnchorSection** - Manages multiple coordinate sets for individual anchors
- **CoordinateSet / GTypeCoordinateSet** - Individual coordinate input components
- **ExcavationLevelUIComponents** - Manages excavation configuration
- **Dynamic UI Generation** - Components created based on anchor types and data

### UI Constants and Styling System
- **Centralized Constants**: All styling constants in `app/gen_design/common/ui_constants.py`
- **Apple-Inspired Design**: Consistent color scheme with `BG_COLOR`, `ACCENT_COLOR`, etc.
- **Typography System**: Consistent fonts with `FONT_REGULAR`, `FONT_BOLD`, `FONT_HEADER`
- **Component Styling**: Standardized button, entry, and frame styling

### Advanced Validation and Error Handling
- **Multi-Level Validation**: Component-level, section-level, and application-level validation
- **User-Friendly Error Messages**: Specific error messages with context
- **Excel Integration Validation**: Validation of Excel data with fallback defaults
- **Type-Specific Validation**: Different validation rules for different anchor types

## Performance Considerations

### Threading and Concurrency
- **Background Operations**: Non-blocking UI during PLAXIS operations
- **Session Management**: Timer-based session monitoring
- **Email Threading**: Asynchronous email sending

### Memory Management
- **Large Dataset Handling**: Efficient processing of large Excel files
- **UI Component Cleanup**: Proper resource management for dynamic UI components
- **PLAXIS Integration**: Memory-efficient model building

### Component Performance
- **Dynamic UI Management**: Efficient creation and destruction of UI components
- **State Synchronization**: Automatic synchronization between paired anchor components
- **Scrollable Content**: Efficient handling of large forms with many components

## Security Architecture

### Authentication
- **Password Generation**: Secure random password generation
- **Session Management**: Time-based session expiration
- **Email Verification**: Email-based authentication workflow

### Data Protection
- **Input Validation**: Comprehensive user input validation at multiple levels
- **Password Storage**: Obfuscated password storage (not encryption)
- **Audit Trail**: Comprehensive logging for security monitoring

## Platform-Specific Features

### Windows Integration
- **PowerShell Commands** - Native Windows command support
- **Windows Paths** - Proper Windows path handling with pathlib
- **File Associations** - Excel and PLAXIS file integration
- **Windows Services** - SMTP and system service integration

### Cross-Platform Considerations
- **pathlib Usage** - Modern path handling for compatibility
- **Conditional Imports** - Platform-specific import handling
- **Unicode Support** - Proper text encoding for international use

## Component Technology Details

### Anchor Geometry Components
- **CoordinateSet**: Handles X1, Y1, X2, Y2 coordinate input with validation
- **GTypeCoordinateSet**: Specialized for G-type anchors (X1, Y1 only)
- **AnchorSection**: Manages multiple coordinate sets with add/remove functionality
- **Paired Anchor Synchronization**: F1/F2 and G1/G2 automatically sync coordinate set counts

### Excavation Components
- **ExcavationCoordinatePoint**: Individual X,Y coordinate points
- **ExcavationPolylineSet**: Complete polyline with multiple points
- **ExcavationStageSection**: Multiple polylines per excavation stage

### Parameter Components
- **Dynamic Entry Components**: Add/remove functionality for parameter lists
- **Combo Selection Components**: Section-unit combination selectors
- **Settlement Components**: Settlement criteria configuration

## Future Technology Considerations

### Potential Enhancements
- **FastAPI** - For potential web interface
- **SQLite** - For local data caching and configuration
- **pytest** - For comprehensive testing framework
- **Docker** - For containerized deployment
- **CI/CD** - Automated testing and deployment pipelines
- **React/Electron** - For modern web-based UI alternative