"""
Excavation Management Package

This package provides excavation level configuration functionality including:
- Coordinate point management for excavation polylines
- Stage-based excavation configuration
- UI components for excavation level editing
- Data validation and processing

The package maintains backward compatibility with the original module structure.
"""

# Import all classes and functions for backward compatibility
try:
    from .models import (
        ExcavationCoordinatePoint,
        ExcavationPolylineSet,
        ExcavationStageSection,
        ExcavationLevelUIComponents
    )
except ImportError:
    pass

try:
    from .definitions import (
        edit_excavation_level,
        create_excavation_level_window,
        create_stage_selection_ui,
        create_action_buttons
    )
except ImportError:
    pass

__all__ = [
    # Models
    'ExcavationCoordinatePoint',
    'ExcavationPolylineSet', 
    'ExcavationStageSection',
    'ExcavationLevelUIComponents',
    
    # Definitions
    'edit_excavation_level',
    'create_excavation_level_window',
    'create_stage_selection_ui',
    'create_action_buttons'
]
