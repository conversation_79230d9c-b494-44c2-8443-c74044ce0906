from scipy.interpolate import interp1d


def draw_plate(g_i, x, y_top, y_bot, platemat):
    """
    Draws a plate in the PLAXIS server.

    Args:
        g_i: The PLAXIS server instance.
        x (float): The x-coordinate for the plate.
        y_top (float): The y-coordinate for the top of the plate.
        y_bot (float): The y-coordinate for the bottom of the plate.
        platemat: The plate material object in the PLAXIS server.

    Returns:
        target_plate: The plate object created in the PLAXIS server.
    """
    target_line = g_i.line((x, y_top), (x, y_bot))[-1]
    target_plate = g_i.plate(target_line)
    posinterface_g = g_i.posinterface(target_line)
    neginterface_g = g_i.neginterface(target_line)
    target_plate.setmaterial(platemat)
    return target_plate


def draw_anchor_n(g_i, x1, y1, x2, y2, anchormat):
    """
    Draws a node-to-node anchor in the PLAXIS server.

    Args:
        g_i: The PLAXIS server instance.
        x1 (float): The x-coordinate of the first point.
        y1 (float): The y-coordinate of the first point.
        x2 (float): The x-coordinate of the second point.
        y2 (float): The y-coordinate of the second point.
        anchormat: The anchor material object in the PLAXIS server.

    Returns:
        target_anchor: The node-to-node anchor object created in the PLAXIS server.
    """
    target_line = g_i.line((x1, y1), (x2, y2))[-1]
    target_anchor = g_i.n2nanchor(target_line)
    target_anchor.setmaterial(anchormat)
    return target_anchor


def draw_anchor_f(g_i, x1, y1, x2, y2, anchormat):
    """
    Draws a fixed-end anchor in the PLAXIS server.

    Args:
        g_i: The PLAXIS server instance.
        x1 (float): The x-coordinate of the first point.
        y1 (float): The y-coordinate of the first point.
        x2 (float): The x-coordinate of the second point.
        y2 (float): The y-coordinate of the second point.
        anchormat: The anchor material object in the PLAXIS server.

    Returns:
        target_anchor: The fixed-end anchor object created in the PLAXIS server.
    """
    target_point = g_i.point(x1, y1)
    dx, dy = (x2 - x1), (y2 - y1)

    target_anchor = g_i.fixedendanchor(target_point, "Direction_x", dx, "Direction_y", dy)
    target_anchor.setmaterial(anchormat)
    return target_anchor


def draw_embeddedbeam(g_i, x1, y1, x2, y2, ebmat):
    """
    Draws an embedded beam in the PLAXIS server.

    Args:
        g_i: The PLAXIS server instance.
        x1 (float): The x-coordinate of the first point.
        y1 (float): The y-coordinate of the first point.
        x2 (float): The x-coordinate of the second point.
        y2 (float): The y-coordinate of the second point.
        ebmat: The embedded beam material object in the PLAXIS server.

    Returns:
        target_eb: The embedded beam object created in the PLAXIS server.
    """
    target_line = g_i.line((x1, y1), (x2, y2))[-1]
    target_eb = g_i.embeddedbeam(target_line)
    target_eb.setmaterial(ebmat)
    return target_eb


def draw_line_load(g_i, x1, y1, x2, y2, fx1, fx2, fy1, fy2):
    """
    Draws a line load in the PLAXIS server.

    Args:
        g_i: The PLAXIS server instance.
        x1 (float): The x-coordinate of the first point.
        y1 (float): The y-coordinate of the first point.
        x2 (float): The x-coordinate of the second point.
        y2 (float): The y-coordinate of the second point.
        fx1 (float): The x-component of the load at the first point (kN/m).
        fx2 (float): The x-component of the load at the second point (kN/m).
        fy1 (float): The y-component of the load at the first point (kN/m).
        fy2 (float): The y-component of the load at the second point (kN/m).

    Returns:
        target_lineloads: The line load object created in the PLAXIS server.
    """
    line1_g = g_i.line((x1, y1), (x2, y2))[-1]
    target_lineloads = g_i.lineload(line1_g)
    target_lineloads = target_lineloads.setproperties(
        'Distribution', "Linear",
        "qx_start", fx1,
        "qx_end", fx2,
        "qy_start", fy1,
        "qy_end", fy2,
    )
    return target_lineloads


def draw_point_load(g_i, x, y, fx, fy, m):
    """
    Draws a point load in the PLAXIS server.

    Args:
        g_i: The PLAXIS server instance.
        x (float): The x-coordinate of the point.
        y (float): The y-coordinate of the point.
        fx (float): The x-component of the load (kN).
        fy (float): The y-component of the load (kN).
        m (float): The moment (kNm).

    Returns:
        target_pointloads: The point load object created in the PLAXIS server.
    """
    point_g = g_i.point(x, y)
    target_pointloads = g_i.pointload(point_g)
    target_pointloads = target_pointloads.setproperties(
        "Fx", fx,
        "Fy", fy,
        "M", m,
    )
    return target_pointloads


def draw_el(g_i, df_el, stage):
    """
    Draws the excavation level in the PLAXIS server for a given stage.

    Args:
        g_i: The PLAXIS server instance.
        df_el (pd.DataFrame): DataFrame containing the excavation level coordinates with columns 'X (m)' and 'Y (m)'.
        stage (int or str): The stage for which to draw the excavation level.

    Returns:
        None
    """
    # Filter and sort the DataFrame for the given stage
    df_el = df_el[df_el['Stage'] == stage].sort_values('X (m)').reset_index(drop=True)

    # Draw lines between consecutive points
    for i in range(len(df_el) - 1):
        ex1, ey1 = df_el.iloc[i][['X (m)', 'Y (m)']]
        ex2, ey2 = df_el.iloc[i + 1][['X (m)', 'Y (m)']]
        g_i.line((ex1, ey1), (ex2, ey2))[-1]


def draw_boundary(g_i, df_section, df_ground_profile):
    """
    Draws the boundary in the PLAXIS server.

    Args:
        g_i: The PLAXIS server instance.
        df_section (pd.DataFrame): DataFrame containing the section coordinates with columns 'Name' and 'X (m)'.
        df_ground_profile (pd.DataFrame): DataFrame containing the ground profile coordinates with columns 'X (m)' and 'Y (m)'.

    Returns:
        None
    """
    x1 = df_section.loc[df_section['Name'] == 'Section_Start', 'X (m)'].values[0]
    x2 = df_section.loc[df_section['Name'] == 'Section_End', 'X (m)'].values[0]

    y1, y2 = df_ground_profile.loc['Top'].iloc[[0, -1]]

    list_x = [x1, x1, x2, x2]
    list_y = [y1] + df_ground_profile.iloc[-1].tolist() + [y2]

    # Keep only the first two and last two items in list_y
    list_y = list_y[:2] + list_y[-2:]

    for x1, y1, x2, y2 in zip(list_x, list_y, list_x[1:], list_y[1:]):
        point_i = g_i.point(x1, y1)
        point_j = g_i.point(x2, y2)

        interface_g = g_i.neginterface(point_i, point_j)[-1]


def draw_structure_polygon(g_i, df_structure_polygon, df_structure_geometry, df_structure_material):
    """
    Draws structure polylines in the PLAXIS server.

    Args:
        g_i: The PLAXIS server instance.
        df_structure_polygon (pd.DataFrame): DataFrame containing the structure polyline coordinates
                                             with columns 'Polyline', 'X (m)', and 'Y (m)'.

    Returns:
        dict: Dictionary of created polylines grouped by name
    """
    # Group by polyline name
    polyline_groups = df_structure_polygon.groupby('Polyline')

    list_soilmat = [mat for mat in g_i.Materials[:] if mat.TypeName.value == 'SoilMat']

    # Iterate through each polyline group
    for polyline_name, group in polyline_groups:
        # Retrieve structure name and material abbreviation
        structure_name = \
            df_structure_geometry.loc[df_structure_geometry['Polyline'] == polyline_name, 'Structure Name'].values[0]
        material_abbv = \
            df_structure_material.loc[
                df_structure_material['Structure Name'] == structure_name, 'Material Abbv'].values[0]

        # Create polyline points
        points_g = [g_i.point(point) for point in zip(group['X (m)'], group['Y (m)'])]
        polygon = g_i.polygon(*points_g)

        # Assign material to the polygon
        for soilmat in list_soilmat:
            if material_abbv == soilmat.Name:
                polygon[1].setmaterial(soilmat)
                break


def get_intercept(df, x_coordinate):
    """
    Finds the y-coordinate intercept of a given x-coordinate on a polyline defined by a DataFrame.

    Args:
        df (pd.DataFrame): DataFrame containing the polyline coordinates with columns 'X (m)' and 'Y (m)'.
        x_coordinate (float): The x-coordinate for which to find the y-coordinate intercept.

    Returns:
        float: The y-coordinate intercept corresponding to the given x-coordinate.
    """
    # Sort the DataFrame and find the two nearest points
    nearest_points = df.sort_values('X (m)').iloc[
        (df['X (m)'] - x_coordinate).abs().argsort()[:2]
    ]

    # Interpolate the y-coordinate
    return interp1d(nearest_points['X (m)'], nearest_points['Y (m)'], kind='linear')(x_coordinate)


def draw_water_level(g_i, phase, df_iwl, df_el, df_plate_loc, stage):
    """
    Draws the water level in the PLAXIS server for a given stage.

    Args:
        g_i: The PLAXIS server instance.
        phase: The phase in which to draw the water level.
        df_iwl (pd.DataFrame): DataFrame containing the initial water level coordinates with columns 'X (m)' and 'Y (m)'.
        df_el (pd.DataFrame): DataFrame containing the excavation level coordinates with columns 'X (m)' and 'Y (m)'.
        df_plate_loc (pd.DataFrame): DataFrame containing the plate location coordinates with columns 'X (m)'.
        stage (int or str): The stage for which to draw the water level.

    Returns:
        None
    """
    condition = df_el['Stage'] == stage
    df_el = df_el.loc[condition]
    df_el = df_el.sort_values('X (m)').reset_index(drop=True)
    y_min = df_el['Y (m)'].min()
    wl_y1 = y_min - 0.5
    wl_y2 = y_min - 0.5

    # Draw water level
    g_i.gotoflow()
    iwl_list = [(row['X (m)'], row['Y (m)']) for _, row in df_iwl.iterrows()]
    condition = df_plate_loc['Plate Name'] == 'Plate_1'
    px1 = df_plate_loc.loc[condition, 'X (m)'].values[0]
    condition = df_plate_loc['Plate Name'] == 'Plate_2'
    px2 = df_plate_loc.loc[condition, 'X (m)'].values[0]
    pwly1 = get_intercept(df_iwl, px1)
    pwly2 = get_intercept(df_iwl, px2)

    # Find the stage water level
    fwl_list = [coord for coord in iwl_list if not (px1 <= coord[0] <= px2)]
    fwl_list.append((px1, pwly1))
    if wl_y1 <= pwly1:
        fwl_list.append((px1, wl_y1))
    if wl_y2 <= pwly2:
        fwl_list.append((px2, wl_y2))
    fwl_list.append((px2, pwly2))
    fwl_list = sorted(fwl_list, key=lambda x: x[0])
    wl = g_i.waterlevel(*fwl_list)
    g_i.setglobalwaterlevel(wl, phase)
