"""
Configuration Details Module

This module provides backward compatibility for the reorganized configuration system.
All functions have been moved to the config package but are re-exported here for compatibility.
"""

# Import all functions from the new location for backward compatibility
from .config.details import (
    show_configuration_details,
    update_anchor_geometry_config,
    update_anchor_parameters_config,
    update_excavation_level_config
)

from .config.manager import (
    ConfigurationManager,
    initialize_comprehensive_config,
    ensure_comprehensive_config_exists,
    get_configuration_status,
    is_configuration_complete,
    reset_configuration
)

# Re-export for backward compatibility
__all__ = [
    'show_configuration_details',
    'update_anchor_geometry_config',
    'update_anchor_parameters_config',
    'update_excavation_level_config',
    'ConfigurationManager',
    'initialize_comprehensive_config',
    'ensure_comprehensive_config_exists',
    'get_configuration_status',
    'is_configuration_complete',
    'reset_configuration'
]
    FONT_BOLD = ("Arial", 10, "bold")
    FONT_HEADER = ("Arial", 14, "bold")
    BG_COLOR = "#ffffff"
    ACCENT_COLOR = "#0066cc"
    SUCCESS_COLOR = "#4CAF50"
    DEFAULT_WINDOW_SIZES = {"config": "1300x900"}
    
    # Define minimal fallback functions
    def create_modal_window(parent, title, size, **kwargs):
        window = tk.Toplevel(parent)
        window.title(title)
        window.geometry(size)
        return window
    
    def create_scrollable_content_area(parent):
        frame = tk.Frame(parent)
        return frame, None, frame
    
    def create_standard_frame(parent, **kwargs):
        return tk.Frame(parent, **kwargs)
    
    def create_info_label(parent, text, **kwargs):
        return tk.Label(parent, text=text, **kwargs)
    
    def create_header_label(parent, text):
        return tk.Label(parent, text=text, font=FONT_HEADER)
    
    def create_section_header(parent, text):
        return tk.Label(parent, text=text, font=FONT_BOLD)
    
    def create_primary_button(parent, text, command, **kwargs):
        return tk.Button(parent, text=text, command=command)
    
    def create_secondary_button(parent, text, command, **kwargs):
        return tk.Button(parent, text=text, command=command)
    
    def bind_mouse_wheel_globally(widget, canvas):
        """Fallback implementation of bind_mouse_wheel_globally."""
        def on_mouse_wheel(event):
            if canvas.bbox("all") is not None:
                canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
        
        def on_mouse_wheel_linux(event):
            if canvas.bbox("all") is not None:
                if event.num == 4:
                    canvas.yview_scroll(-1, "units")
                elif event.num == 5:
                    canvas.yview_scroll(1, "units")
        
        try:
            widget.bind("<MouseWheel>", on_mouse_wheel, add=True)
            widget.bind("<Button-4>", on_mouse_wheel_linux, add=True)
            widget.bind("<Button-5>", on_mouse_wheel_linux, add=True)
        except tk.TclError:
            pass
        
        try:
            for child in widget.winfo_children():
                bind_mouse_wheel_globally(child, canvas)
        except tk.TclError:
            pass


# =============================================================================
# CONFIGURATION DATA STRUCTURES
# =============================================================================

class ConfigurationManager:
    """
    Central manager for configuration data across all generative design components.
    """
    
    def __init__(self, app_instance):
        self.app_instance = app_instance
        self.config_data = {
            'anchor_geometry': {'configured': False, 'data': {}, 'combinations': 1},
            'anchor_parameters': {'configured': False, 'data': {}, 'combinations': 1},
            'excavation_level': {'configured': False, 'data': {}, 'combinations': 1}
        }
    
    def update_geometry_config(self, geometry_data: Dict[str, Any], stage: Optional[str] = None):
        """Update anchor geometry configuration."""
        try:
            self.config_data['anchor_geometry'].update({
                'configured': True,
                'data': geometry_data,
                'stage': stage,
                'combinations': self._calculate_geometry_combinations(geometry_data)
            })
            logging.info(f"Updated anchor geometry config for stage {stage}")
        except Exception as e:
            logging.error(f"Error updating geometry config: {e}")
    
    def update_parameters_config(self, parameters_data: Dict[str, Any], stage: Optional[str] = None):
        """Update anchor parameters configuration."""
        try:
            self.config_data['anchor_parameters'].update({
                'configured': True,
                'data': parameters_data,
                'stage': stage,
                'combinations': self._calculate_parameter_combinations(parameters_data)
            })
            logging.info(f"Updated anchor parameters config for stage {stage}")
        except Exception as e:
            logging.error(f"Error updating parameters config: {e}")
    
    def update_excavation_config(self, excavation_data: Dict[str, Any], stage: Optional[str] = None):
        """Update excavation level configuration."""
        try:
            self.config_data['excavation_level'].update({
                'configured': True,
                'data': excavation_data,
                'stage': stage,
                'combinations': self._calculate_excavation_combinations(excavation_data)
            })
            logging.info(f"Updated excavation level config for stage {stage}")
        except Exception as e:
            logging.error(f"Error updating excavation config: {e}")
    
    def get_total_combinations(self) -> int:
        """Calculate total combinations across all configurations."""
        return (self.config_data['anchor_geometry']['combinations'] * 
                self.config_data['anchor_parameters']['combinations'] * 
                self.config_data['excavation_level']['combinations'])
    
    def _calculate_geometry_combinations(self, geometry_data: Dict[str, Any]) -> int:
        """Calculate geometry combinations."""
        try:
            anchors = geometry_data.get('anchors', {})
            if not anchors:
                return 1
            
            total_combinations = 1
            for anchor_name, anchor_data in anchors.items():
                coordinates = anchor_data.get('coordinates', [])
                if isinstance(coordinates, list):
                    total_combinations *= len(coordinates)
                else:
                    total_combinations *= 1
            
            return total_combinations
        except Exception as e:
            logging.error(f"Error calculating geometry combinations: {e}")
            return 1
    
    def _calculate_parameter_combinations(self, parameters_data: Dict[str, Any]) -> int:
        """Calculate parameter combinations - excludes settlement criteria as they are checking criteria, not design variables."""
        try:
            parameters = parameters_data.get('parameters', {})
            if not parameters:
                return 1
        
            # Define settlement criteria parameters that should be excluded from combination calculations
            settlement_criteria_keys = {
                'settlement', 'Settlement', 'max_settlement', 'settlement_limit', 
                'settlement_criteria', 'allowable_settlement', 'Settlement_Limit',
                'settlement_check', 'Settlement_Check', 'settlement_value',
                'max_allowable_settlement', 'settlement_threshold'
            }
            
            # Define other non-design parameters to exclude from combinations
            excluded_keys = {
                'anchor_name', 'Type', 'anchor_type', 'name', 'anchor_id'
            }
            
            # Combine all excluded keys
            all_excluded_keys = settlement_criteria_keys.union(excluded_keys)
        
            total_combinations = 1
            for anchor_name, params in parameters.items():
                anchor_combinations = 1
                
                # Debug logging
                logging.info(f"DEBUG: Calculating combinations for anchor {anchor_name}")
                logging.info(f"DEBUG: Params keys: {list(params.keys()) if isinstance(params, dict) else type(params)}")
                
                if isinstance(params, dict):
                    # Check for different parameter keys that might be lists (excluding settlement criteria)
                    list_parameters = []
                    for param_key, param_value in params.items():
                        if param_key not in all_excluded_keys and isinstance(param_value, list):
                            list_parameters.append((param_key, len(param_value)))
                            logging.info(f"DEBUG: Found design parameter {param_key} with {len(param_value)} items")
                        elif param_key in settlement_criteria_keys:
                            logging.info(f"DEBUG: Excluding settlement criteria {param_key} from combinations")
                
                    
                    # If we have list parameters, calculate combinations
                    if list_parameters:
                        for param_key, param_count in list_parameters:
                            anchor_combinations *= param_count
                    else:
                        # If no list parameters, this anchor contributes 1 combination
                        anchor_combinations = 1
                else:
                    anchor_combinations = 1
            
                
                total_combinations *= anchor_combinations
                logging.info(f"DEBUG: Anchor {anchor_name} combinations: {anchor_combinations}")
            
            logging.info(f"DEBUG: Total parameter combinations (excluding settlement criteria): {total_combinations}")
            return total_combinations
        
        except Exception as e:
            logging.error(f"Error calculating parameter combinations: {e}")
            return 1
    
    def _calculate_excavation_combinations(self, excavation_data: Dict[str, Any]) -> int:
        """Calculate excavation combinations."""
        try:
            stage_data = excavation_data.get('stage_data', {})
            if not stage_data:
                return 1
            
            total_combinations = 1
            for stage, polylines in stage_data.items():
                if polylines:
                    total_combinations *= len(polylines)
            
            return total_combinations
        except Exception as e:
            logging.error(f"Error calculating excavation combinations: {e}")
            return 1


# =============================================================================
# CONFIGURATION DETAIL VIEW
# =============================================================================

def show_configuration_details(app_instance):
    """
    Display comprehensive configuration details in an enhanced modal window.
    
    Args:
        app_instance: The application instance containing configuration data
    """
    try:
        # Create configuration manager
        config_manager = ConfigurationManager(app_instance)
        
        # Debug: Log the comprehensive_config structure
        if hasattr(app_instance, 'comprehensive_config'):
            logging.info("=== DEBUG: Comprehensive Config Structure ===")
            logging.info(f"comprehensive_config keys: {list(app_instance.comprehensive_config.keys())}")
            for key, value in app_instance.comprehensive_config.items():
                logging.info(f"{key}: configured={value.get('configured', False)}, keys={list(value.keys())}")
                if value.get('configured', False):
                    if key == 'anchor_geometry':
                        anchors = value.get('anchors', {})
                        logging.info(f"  anchor_geometry anchors: {list(anchors.keys())}")
                        for anchor_name, anchor_data in anchors.items():
                            logging.info(f"    {anchor_name}: {list(anchor_data.keys())}")
                    elif key == 'anchor_parameters':
                        parameters = value.get('parameters', {})
                        logging.info(f"  anchor_parameters: {list(parameters.keys())}")
                        for param_name, param_data in parameters.items():
                            logging.info(f"    {param_name}: {list(param_data.keys()) if isinstance(param_data, dict) else type(param_data)}")
                    elif key == 'excavation_level':
                        stage_data = value.get('stage_data', {})
                        logging.info(f"  excavation_level stages: {list(stage_data.keys())}")
                        for stage, stage_coords in stage_data.items():
                            logging.info(f"    {stage}: {len(stage_coords) if isinstance(stage_coords, (list, dict)) else type(stage_coords)}")
        else:
            logging.warning("No comprehensive_config found in app_instance")
        
        # Update configuration data from app instance
        if hasattr(app_instance, 'comprehensive_config'):
            _update_config_manager_from_comprehensive(config_manager, app_instance.comprehensive_config)
        
        # Debug: Log the config_manager structure after update
        logging.info("=== DEBUG: Config Manager Structure After Update ===")
        for key, value in config_manager.config_data.items():
            logging.info(f"{key}: configured={value.get('configured', False)}, combinations={value.get('combinations', 1)}")
            if value.get('configured', False):
                data = value.get('data', {})
                logging.info(f"  data keys: {list(data.keys()) if isinstance(data, dict) else type(data)}")
        
        # Create modal window
        parent = app_instance.config_window if hasattr(app_instance, 'config_window') else app_instance.root
        details_window = create_modal_window(
            parent,
            "Configuration Details",
            "1300x900",  # Larger window for better view
            resizable=True,
            center=True
        )
        
        # Create scrollable content area
        main_frame, canvas, scrollable_frame = create_scrollable_content_area(details_window)
        
        # Enable global mouse wheel scrolling across the entire window
        bind_mouse_wheel_globally(details_window, canvas)
        
        # Create enhanced header section with diagnostics
        _create_enhanced_header_section(scrollable_frame, app_instance)
        
        # Create main content area
        _create_main_content_area(scrollable_frame, config_manager)
        
        # Create summary section
        _create_summary_section(scrollable_frame, config_manager)
        
        # Create action buttons
        _create_action_buttons(details_window, config_manager)
        
        logging.info("Enhanced configuration details window displayed successfully")
        
    except Exception as e:
        error_msg = f"Error showing configuration details: {str(e)}"
        logging.error(error_msg)
        logging.exception("Detailed traceback:")
        
        # Fall back to simple message box
        messagebox.showerror("Error", "Unable to display configuration details.")


def _update_config_manager_from_comprehensive(config_manager: ConfigurationManager, comprehensive_config: Dict[str, Any]):
    """Update configuration manager from comprehensive config."""
    try:
        # Update geometry
        geometry_config = comprehensive_config.get('anchor_geometry', {})
        if geometry_config.get('configured', False):
            # Extract the actual data structure needed by the config manager
            geometry_data = {
                'anchor_type': geometry_config.get('anchor_type'),
                'anchors': geometry_config.get('anchors', {}),
                'stage': geometry_config.get('stage')
            }
            config_manager.update_geometry_config(geometry_data, geometry_config.get('stage'))
        
        # Update parameters - fix the data structure mapping
        parameters_config = comprehensive_config.get('anchor_parameters', {})
        if parameters_config.get('configured', False):
            # The parameters are stored directly under 'parameters' key
            parameters_data = {
                'parameters': parameters_config.get('parameters', {}),
                'stage': parameters_config.get('stage')
            }
            config_manager.update_parameters_config(parameters_data, parameters_config.get('stage'))
        
        # Update excavation
        excavation_config = comprehensive_config.get('excavation_level', {})
        if excavation_config.get('configured', False):
            # Extract the actual data structure needed by the config manager
            excavation_data = {
                'stage_data': excavation_config.get('stage_data', {}),
                'stage': excavation_config.get('stage')
            }
            config_manager.update_excavation_config(excavation_data, excavation_config.get('stage'))
            
        logging.info("Configuration manager updated from comprehensive config")
        
    except Exception as e:
        logging.error(f"Error updating config manager: {e}")
        logging.exception("Detailed traceback:")


def _create_header_section(scrollable_frame: tk.Widget, app_instance):
    """Create the header section with project information."""
    header_frame = create_standard_frame(scrollable_frame)
    header_frame.pack(fill="x", padx=20, pady=15)
    
    # Main header
    header_label = create_header_label(header_frame, "Generative Design Configuration Overview")
    header_label.pack(pady=(0, 10))
    
    # Project information
    current_date = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    info_text = f"Generated on: {current_date}"
    if hasattr(app_instance, 'user_name') and app_instance.user_name:
        info_text += f" | User: {app_instance.user_name}"
    
    info_label = create_info_label(header_frame, info_text, fg_color="#666666")
    info_label.pack(pady=(0, 10))


def _create_main_content_area(scrollable_frame: tk.Widget, config_manager: ConfigurationManager):
    """Create the main content area with configuration details."""
    # Create 3-column layout
    columns_frame = create_standard_frame(scrollable_frame)
    columns_frame.pack(fill="both", expand=True, padx=20, pady=10)
    
    # Configure equal column weights
    for i in range(3):
        columns_frame.columnconfigure(i, weight=1)
    
    # Create column frames
    geometry_frame = tk.LabelFrame(columns_frame, text="Anchor Geometry", 
                                  font=FONT_BOLD, bg="#f8f9fa", padx=10, pady=10)
    geometry_frame.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
    
    parameters_frame = tk.LabelFrame(columns_frame, text="Anchor Parameters", 
                                    font=FONT_BOLD, bg="#f8f9fa", padx=10, pady=10)
    parameters_frame.grid(row=0, column=1, sticky="nsew", padx=5, pady=5)
    
    excavation_frame = tk.LabelFrame(columns_frame, text="Excavation Level", 
                                    font=FONT_BOLD, bg="#f8f9fa", padx=10, pady=10)
    excavation_frame.grid(row=0, column=2, sticky="nsew", padx=5, pady=5)
    
    # Store config_manager reference in excavation_frame for access in _populate_excavation_column
    excavation_frame.config_manager = config_manager
    
    # Populate columns
    _populate_geometry_column(geometry_frame, config_manager.config_data['anchor_geometry'])
    _populate_parameters_column(parameters_frame, config_manager.config_data['anchor_parameters'])
    _populate_excavation_column(excavation_frame, config_manager.config_data['excavation_level'])


def _populate_geometry_column(parent_frame: tk.Widget, geometry_config: Dict[str, Any]):
    """Populate the geometry column with configuration data."""
    try:
        if not geometry_config.get('configured', False):
            no_config_label = create_info_label(parent_frame, "No geometry configuration available", 
                                               fg_color="#999999")
            no_config_label.pack(fill="x", pady=10)
            return
        
        # Basic information
        data = geometry_config.get('data', {})
        info_text = f"Type: {data.get('anchor_type', 'N/A')}\n"
        info_text += f"Stage: {geometry_config.get('stage', 'N/A')}\n"
        info_text += f"Combinations: {geometry_config.get('combinations', 1)}"
        
        info_label = create_info_label(parent_frame, info_text, font=FONT_BOLD)
        info_label.pack(fill="x", pady=5)
        
        # Detailed anchor information
        anchors = data.get('anchors', {})
        if anchors:
            for anchor_name, anchor_data in anchors.items():
                anchor_frame = tk.LabelFrame(parent_frame, text=anchor_name, font=FONT_REGULAR)
                anchor_frame.pack(fill="x", pady=5)
                
                # Show coordinate sets
                coordinates = anchor_data.get('coordinates', [])
                if isinstance(coordinates, list) and coordinates:
                    # Add header for coordinate sets
                    header_label = tk.Label(anchor_frame, text="Coordinate Sets:", font=FONT_BOLD)
                    header_label.pack(fill="x", padx=10, pady=2)
                    
                    for i, coord_set in enumerate(coordinates, 1):
                        coord_text = f"Set {i}: "
                        coord_parts = []
                        
                        # Handle different coordinate formats
                        if isinstance(coord_set, dict):
                            # Standard coordinate format
                            for coord_key in ['X1', 'Y1', 'X2', 'Y2']:
                                if coord_key in coord_set:
                                    value = coord_set[coord_key]
                                    if isinstance(value, (int, float)):
                                        coord_parts.append(f"{coord_key}={value:.2f}")
                                    else:
                                        coord_parts.append(f"{coord_key}={value}")
                                        
                            # Also check for other common coordinate keys
                            for coord_key in ['x', 'y', 'X', 'Y']:
                                if coord_key in coord_set and coord_key not in ['X1', 'Y1', 'X2', 'Y2']:
                                    value = coord_set[coord_key]
                                    if isinstance(value, (int, float)):
                                        coord_parts.append(f"{coord_key}={value:.2f}")
                                    else:
                                        coord_parts.append(f"{coord_key}={value}")
                        else:
                            # Handle other formats
                            coord_parts.append(str(coord_set))
                        
                        coord_text += ", ".join(coord_parts)
                        
                        coord_label = create_info_label(anchor_frame, coord_text)
                        coord_label.pack(fill="x", padx=15, pady=1)
                else:
                    # No coordinates or empty list
                    no_coords_label = create_info_label(anchor_frame, "No coordinate sets configured", 
                                                       fg_color="#999999")
                    no_coords_label.pack(fill="x", padx=10, pady=2)
                
                # Show additional anchor properties if available
                additional_props = {}
                excluded_keys = {'coordinates', 'anchor_name', 'name'}
                
                for key, value in anchor_data.items():
                    if key not in excluded_keys and value is not None and value != []:
                        additional_props[key] = value
                
                if additional_props:
                    props_label = tk.Label(anchor_frame, text="Properties:", font=FONT_BOLD)
                    props_label.pack(fill="x", padx=10, pady=(5, 2))
                    
                    for key, value in additional_props.items():
                        prop_text = f"  • {key}: {value}"
                        prop_label = create_info_label(anchor_frame, prop_text)
                        prop_label.pack(fill="x", padx=15, pady=1)
        else:
            # No anchors configured
            no_anchors_label = create_info_label(parent_frame, "No anchors configured", 
                                               fg_color="#999999")
            no_anchors_label.pack(fill="x", pady=10)
        
        logging.info("Geometry column populated successfully")
        
    except Exception as e:
        error_label = create_info_label(parent_frame, f"Error loading geometry: {str(e)}", 
                                       fg_color="#ff0000")
        error_label.pack(fill="x", pady=10)
        logging.error(f"Error populating geometry column: {e}")
        logging.exception("Detailed traceback:")


def _populate_parameters_column(parent_frame: tk.Widget, parameters_config: Dict[str, Any]):
    """Populate the parameters column with configuration data in enhanced 3-column format."""
    try:
        if not parameters_config.get('configured', False):
            no_config_label = create_info_label(parent_frame, "No parameters configuration available", 
                                               fg_color="#999999")
            no_config_label.pack(fill="x", pady=10)
            return

        # Basic information
        data = parameters_config.get('data', {})
        parameters = data.get('parameters', {})
        
        # Debug logging
        logging.info(f"DEBUG: _populate_parameters_column - data keys: {list(data.keys())}")
        logging.info(f"DEBUG: _populate_parameters_column - parameters keys: {list(parameters.keys())}")
        
        info_text = f"Stage: {parameters_config.get('stage', 'N/A')}\n"
        info_text += f"Anchors: {len(parameters)}\n"
        info_text += f"Combinations: {parameters_config.get('combinations', 1)}"
        
        info_label = create_info_label(parent_frame, info_text, font=FONT_BOLD)
        info_label.pack(fill="x", pady=5)

        # Detailed parameter information with enhanced formatting
        if parameters:
            for anchor_name, params in parameters.items():
                logging.info(f"DEBUG: Processing anchor {anchor_name} with params: {list(params.keys()) if isinstance(params, dict) else type(params)}")
                
                anchor_frame = tk.LabelFrame(parent_frame, text=anchor_name, font=FONT_REGULAR)
                anchor_frame.pack(fill="x", pady=5)
                
                # Create 3-column header frame
                header_frame = tk.Frame(anchor_frame)
                header_frame.pack(fill="x", padx=5, pady=2)
                
                # Configure equal column weights
                for i in range(3):
                    header_frame.columnconfigure(i, weight=1)
                
                # Column headers
                tk.Label(header_frame, text="Section_Unit", font=FONT_BOLD, anchor="w").grid(row=0, column=0, sticky="w", padx=2)
                tk.Label(header_frame, text="Spacing (m)", font=FONT_BOLD, anchor="w").grid(row=0, column=1, sticky="w", padx=2)
                
                # Determine which third column to show based on anchor type
                anchor_type = params.get('Type', params.get('anchor_type', 'N'))  # Check both possible keys
                if anchor_type in ['N', 'F']:
                    third_col_header = "Prestress (kN/m)"
                    third_col_key = "Prestress"
                else:  # G type
                    third_col_header = "Theta_g (deg)"
                    third_col_key = "Theta_g"
                
                tk.Label(header_frame, text=third_col_header, font=FONT_BOLD, anchor="w").grid(row=0, column=2, sticky="w", padx=2)
                
                # Create data content frame
                content_frame = tk.Frame(anchor_frame)
                content_frame.pack(fill="x", padx=5, pady=2)
                
                # Configure equal column weights
                for i in range(3):
                    content_frame.columnconfigure(i, weight=1)
                
                # Extract data arrays - handle both possible data structures
                section_units = params.get('Section_Unit', params.get('sections', []))
                spacings = params.get('Spacing', params.get('spacing', []))
                third_col_values = params.get(third_col_key, params.get(third_col_key.lower(), []))
                
                logging.info(f"DEBUG: {anchor_name} - section_units: {section_units}")
                logging.info(f"DEBUG: {anchor_name} - spacings: {spacings}")
                logging.info(f"DEBUG: {anchor_name} - third_col_values: {third_col_values}")
                
                # Handle different data structures for sections
                if section_units and isinstance(section_units[0], dict):
                    # If sections are dictionaries with 'section' and 'unit' keys
                    section_display = [(s.get('section', 'Unknown'), s.get('unit', 'Unknown')) for s in section_units]
                elif section_units and isinstance(section_units[0], (list, tuple)):
                    # If sections are tuples/lists
                    section_display = section_units
                else:
                    # If sections are simple strings or other format
                    section_display = [(str(s), 'N/A') for s in section_units] if section_units else []
                
                # Determine maximum number of rows needed
                max_rows = max(len(section_display), len(spacings), len(third_col_values))
                
                # If no data, show a message
                if max_rows == 0:
                    no_data_label = create_info_label(content_frame, "No parameter data available", fg_color="#999999")
                    no_data_label.grid(row=0, column=0, columnspan=3, sticky="w", padx=2, pady=2)
                else:
                    # Populate rows with data (1 item per row)
                    for row in range(max_rows):
                        # Section_Unit column
                        if row < len(section_display):
                            section, unit = section_display[row]
                            section_unit_text = f"{section} (Unit: {unit})"
                        else:
                            section_unit_text = "-"
                        
                        section_label = create_info_label(content_frame, section_unit_text)
                        section_label.grid(row=row, column=0, sticky="w", padx=2, pady=1)
                        
                        # Spacing column
                        spacing_text = str(spacings[row]) if row < len(spacings) else "-"
                        spacing_label = create_info_label(content_frame, spacing_text)
                        spacing_label.grid(row=row, column=1, sticky="w", padx=2, pady=1)
                        
                        # Third column (Prestress or Theta_g)
                        third_col_text = str(third_col_values[row]) if row < len(third_col_values) else "-"
                        third_col_label = create_info_label(content_frame, third_col_text)
                        third_col_label.grid(row=row, column=2, sticky="w", padx=2, pady=1)
                        
                # Add additional parameters if they exist - separate design parameters from checking criteria
                additional_params = {}
                settlement_criteria = {}
                excluded_keys = {'Section_Unit', 'Spacing', 'Prestress', 'Theta_g', 'Type', 'anchor_type', 
                               'sections', 'spacing', 'prestress', 'theta_g', 'anchor_name'}
                
                # Define settlement criteria parameters
                settlement_criteria_keys = {
                    'settlement', 'Settlement', 'max_settlement', 'settlement_limit', 
                    'settlement_criteria', 'allowable_settlement', 'Settlement_Limit',
                    'settlement_check', 'Settlement_Check', 'settlement_value',
                    'max_allowable_settlement', 'settlement_threshold'
                }
                
                for key, value in params.items():
                    if key not in excluded_keys and value is not None and value != []:
                        if key in settlement_criteria_keys:
                            settlement_criteria[key] = value
                        else:
                            additional_params[key] = value
                
                # Display design parameters first
                if additional_params:
                    additional_frame = tk.Frame(anchor_frame)
                    additional_frame.pack(fill="x", padx=5, pady=2)
                    
                    additional_label = tk.Label(additional_frame, text="Other Design Parameters:", font=FONT_BOLD)
                    additional_label.pack(anchor="w")
                    
                    for key, value in additional_params.items():
                        param_text = f"  • {key}: {value}"
                        param_label = create_info_label(additional_frame, param_text)
                        param_label.pack(anchor="w")
                
                # Display settlement criteria separately (not counted in combinations)
                if settlement_criteria:
                    criteria_frame = tk.Frame(anchor_frame)
                    criteria_frame.pack(fill="x", padx=5, pady=2)
                    
                    criteria_label = tk.Label(criteria_frame, text="Settlement Criteria (Checking Only):", 
                                            font=FONT_BOLD, fg="#666666")
                    criteria_label.pack(anchor="w")
                    
                    for key, value in settlement_criteria.items():
                        criteria_text = f"  • {key}: {value}"
                        criteria_param_label = create_info_label(criteria_frame, criteria_text, fg_color="#666666")
                        criteria_param_label.pack(anchor="w")
        else:
            # No parameters configured
            no_params_label = create_info_label(parent_frame, "No parameter configurations found", 
                                               fg_color="#999999")
            no_params_label.pack(fill="x", pady=10)

        logging.info("Parameters column populated successfully with enhanced 3-column format")

    except Exception as e:
        error_label = create_info_label(parent_frame, f"Error loading parameters: {str(e)}", 
                                       fg_color="#ff0000")
        error_label.pack(fill="x", pady=10)
        logging.error(f"Error populating parameters column: {e}")
        logging.exception("Detailed traceback:")


def _populate_excavation_column(parent_frame: tk.Widget, excavation_config: Dict[str, Any]):
    """Populate the excavation column with configuration data - simplified to show only excavation sets and coordinates."""
    try:
        if not excavation_config.get('configured', False):
            no_config_label = create_info_label(parent_frame, "No excavation configuration available", 
                                              fg_color="#999999")
            no_config_label.pack(fill="x", pady=10)
            return
        
        # Basic information
        data = excavation_config.get('data', {})
        stage_data = data.get('stage_data', {})
        info_text = f"Stage: {excavation_config.get('stage', 'N/A')}\n"
        info_text += f"Stages: {len(stage_data)}\n"
        info_text += f"Combinations: {excavation_config.get('combinations', 1)}"
        
        info_label = create_info_label(parent_frame, info_text, font=FONT_BOLD)
        info_label.pack(fill="x", pady=5)
        
        # Display excavation stages and their coordinate sets
        if stage_data:
            for stage, stage_coordinates in stage_data.items():
                stage_frame = tk.LabelFrame(parent_frame, text=f"Excavation Stage {stage}", 
                                          font=FONT_REGULAR)
                stage_frame.pack(fill="x", pady=5)
                
                if stage_coordinates:
                    # Determine number of sets and handle different data structures
                    sets_list = []
                    if isinstance(stage_coordinates, list):
                        if stage_coordinates and isinstance(stage_coordinates[0], dict):
                            # Single set with list of coordinate dictionaries
                            sets_list = [stage_coordinates]
                        else:
                            # Multiple sets - each item is a set of coordinates
                            sets_list = stage_coordinates
                    else:
                        # Other format - treat as single set
                        sets_list = [stage_coordinates]
                    
                    # Show number of sets
                    sets_info = f"Excavation Sets: {len(sets_list)}"
                    sets_label = create_info_label(stage_frame, sets_info, font=FONT_BOLD, fg_color="#2e7d32")
                    sets_label.pack(fill="x", padx=10, pady=2)
                    
                    # Display each set with its coordinates
                    for set_idx, coordinate_set in enumerate(sets_list, 1):
                        set_frame = tk.LabelFrame(stage_frame, text=f"Set {set_idx}", font=FONT_REGULAR)
                        set_frame.pack(fill="x", padx=15, pady=3)
                        
                        # Display coordinates
                        if isinstance(coordinate_set, list):
                            if coordinate_set:
                                coords_header = create_info_label(set_frame, "Coordinates:", font=FONT_BOLD)
                                coords_header.pack(fill="x", padx=5, pady=2)
                                
                                for coord_idx, coord in enumerate(coordinate_set, 1):
                                    coord_text = f"Point {coord_idx}: "
                                    if isinstance(coord, dict):
                                        # Handle coordinate dictionary
                                        coord_parts = []
                                        for key in ['X', 'x', 'X1', 'X2']:
                                            if key in coord:
                                                coord_parts.append(f"X={coord[key]}")
                                        for key in ['Y', 'y', 'Y1', 'Y2']:
                                            if key in coord:
                                                coord_parts.append(f"Y={coord[key]}")
                                        if not coord_parts:
                                            # If no standard coordinate keys, show all key-value pairs
                                            coord_parts = [f"{k}={v}" for k, v in coord.items()]
                                        coord_text += ", ".join(coord_parts)
                                    else:
                                        # Handle other coordinate formats
                                        coord_text += str(coord)
                                    
                                    coord_label = create_info_label(set_frame, coord_text)
                                    coord_label.pack(fill="x", padx=10, pady=1)
                            else:
                                empty_label = create_info_label(set_frame, "No coordinates available", fg_color="#999999")
                                empty_label.pack(fill="x", padx=10, pady=2)
                        else:
                            # Handle non-list coordinate format
                            coord_text = f"Coordinates: {coordinate_set}"
                            coord_label = create_info_label(set_frame, coord_text)
                            coord_label.pack(fill="x", padx=10, pady=2)
                else:
                    no_data_label = create_info_label(stage_frame, "No excavation data configured for this stage", 
                                                    fg_color="#999999")
                    no_data_label.pack(fill="x", padx=10, pady=2)
        else:
            # No stages configured
            no_stages_label = create_info_label(parent_frame, "No excavation stages configured", 
                                              fg_color="#999999")
            no_stages_label.pack(fill="x", pady=10)
        
        logging.info("Excavation column populated successfully (simplified)")
        
    except Exception as e:
        error_label = create_info_label(parent_frame, f"Error loading excavation: {str(e)}", 
                                       fg_color="#ff0000")
        error_label.pack(fill="x", pady=10)
        logging.error(f"Error populating excavation column: {e}")
        logging.exception("Detailed traceback:")


def _create_summary_section(scrollable_frame: tk.Widget, config_manager: ConfigurationManager):
    """Create the summary section with combination calculations."""
    summary_frame = create_standard_frame(scrollable_frame)
    summary_frame.pack(fill="x", padx=20, pady=15)
    
    # Summary header
    summary_header = create_header_label(summary_frame, "Combination Analysis Summary")
    summary_header.pack(pady=(0, 10))
    
    # Combination details
    geometry_combos = config_manager.config_data['anchor_geometry']['combinations']
    param_combos = config_manager.config_data['anchor_parameters']['combinations']
    excavation_combos = config_manager.config_data['excavation_level']['combinations']
    total_combos = config_manager.get_total_combinations()
    
    summary_text = f"Geometry Combinations: {geometry_combos}\n"
    summary_text += f"Parameter Combinations: {param_combos}*\n"
    summary_text += f"Excavation Combinations: {excavation_combos}\n"
    summary_text += f"Total Brute Force Combinations: {total_combos}\n\n"
    summary_text += f"* Settlement criteria are excluded from parameter combinations\n"
    summary_text += f"  as they are checking criteria, not design variables."
    
    # Style the summary based on total combinations
    if total_combos > 1000:
        color = "#ff6b6b"  # Red for high combinations
    elif total_combos > 100:
        color = "#ffa500"  # Orange for medium combinations
    else:
        color = SUCCESS_COLOR  # Green for low combinations
    
    summary_label = create_info_label(summary_frame, summary_text, font=FONT_BOLD, fg_color=color)
    summary_label.pack(fill="x", pady=5)


def _create_action_buttons(details_window: tk.Toplevel, config_manager: ConfigurationManager):
    """Create action buttons for the configuration details window."""
    buttons_frame = create_standard_frame(details_window)
    buttons_frame.pack(fill="x", padx=20, pady=15)
    
    # Close button
    create_secondary_button(
        buttons_frame,
        "Close",
        details_window.destroy,
        width=12, height=1,
        pack_side=tk.RIGHT
    )
    
    # Export button (future enhancement)
    create_primary_button(
        buttons_frame,
        "Export Configuration",
        lambda: _export_configuration(config_manager),
        width=18, height=1,
        pack_side=tk.RIGHT,
        padx=(0, 10)
    )


def _export_configuration(config_manager: ConfigurationManager):
    """Export configuration to file (future enhancement)."""
    messagebox.showinfo("Export Configuration", 
        "Configuration export functionality will be available in a future update.")
    logging.info("Configuration export requested (feature not yet implemented)")

def _create_diagnostic_section(scrollable_frame: tk.Widget, app_instance):
    """Create a diagnostic section to help troubleshoot configuration issues."""
    try:
        # Create diagnostic frame
        diagnostic_frame = tk.LabelFrame(scrollable_frame, text="Configuration Diagnostics", 
                                       font=FONT_BOLD, bg="#fff3cd", padx=10, pady=10)
        diagnostic_frame.pack(fill="x", padx=20, pady=10)
        
        # Check if comprehensive_config exists
        if hasattr(app_instance, 'comprehensive_config'):
            status_text = "✓ comprehensive_config exists\n"
            
            # Check each configuration type
            for config_type in ['anchor_geometry', 'anchor_parameters', 'excavation_level']:
                config_data = app_instance.comprehensive_config.get(config_type, {})
                configured = config_data.get('configured', False)
                
                if configured:
                    status_text += f"✓ {config_type}: configured\n"
                    
                    # Show data structure keys
                    if config_type == 'anchor_geometry':
                        anchors = config_data.get('anchors', {})
                        status_text += f"  - {len(anchors)} anchor(s): {list(anchors.keys())}\n"
                    elif config_type == 'anchor_parameters':
                        parameters = config_data.get('parameters', {})
                        status_text += f"  - {len(parameters)} parameter set(s): {list(parameters.keys())}\n"
                    elif config_type == 'excavation_level':
                        stage_data = config_data.get('stage_data', {})
                        status_text += f"  - {len(stage_data)} stage(s): {list(stage_data.keys())}\n"
                else:
                    status_text += f"✗ {config_type}: not configured\n"
        else:
            status_text = "✗ comprehensive_config not found\n"
        
        # Display diagnostic information
        diagnostic_label = create_info_label(diagnostic_frame, status_text, font=FONT_REGULAR)
        diagnostic_label.pack(fill="x", pady=5)
        
        # Add instructions
        instructions_text = ("To fix missing configurations:\n"
                           "1. Click 'Edit Anchor' to configure anchor geometry and parameters\n"
                           "2. Click 'Edit Excavation Level' to configure excavation data\n"
                           "3. Save each configuration before viewing details")
        
        instructions_label = create_info_label(diagnostic_frame, instructions_text, 
                                             font=FONT_REGULAR, fg_color="#856404")
        instructions_label.pack(fill="x", pady=(10, 0))
        
    except Exception as e:
        logging.error(f"Error creating diagnostic section: {e}")


def _create_enhanced_header_section(scrollable_frame: tk.Widget, app_instance):
    """Create the enhanced header section with project information and diagnostics."""
    header_frame = create_standard_frame(scrollable_frame)
    header_frame.pack(fill="x", padx=20, pady=15)
    
    # Main header
    header_label = create_header_label(header_frame, "Generative Design Configuration Overview")
    header_label.pack(pady=(0, 10))
    
    # Project information
    current_date = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    info_text = f"Generated on: {current_date}"
    if hasattr(app_instance, 'user_name') and app_instance.user_name:
        info_text += f" | User: {app_instance.user_name}"
    
    info_label = create_info_label(header_frame, info_text, fg_color="#666666")
    info_label.pack(pady=(0, 10))
    
    # Add diagnostic section
    _create_diagnostic_section(scrollable_frame, app_instance)


# =============================================================================
# INTEGRATION FUNCTIONS
# =============================================================================

