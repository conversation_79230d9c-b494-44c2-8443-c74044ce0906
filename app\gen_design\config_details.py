"""
Configuration Details Module

This module provides backward compatibility for the reorganized configuration system.
All functions have been moved to the config package but are re-exported here for compatibility.
"""

# Import all functions from the new location for backward compatibility
from .config.details import (
    show_configuration_details,
    update_anchor_geometry_config,
    update_anchor_parameters_config,
    update_excavation_level_config
)

from .config.manager import (
    ConfigurationManager,
    initialize_comprehensive_config,
    ensure_comprehensive_config_exists,
    get_configuration_status,
    is_configuration_complete,
    reset_configuration
)

# Re-export for backward compatibility
__all__ = [
    'show_configuration_details',
    'update_anchor_geometry_config',
    'update_anchor_parameters_config',
    'update_excavation_level_config',
    'ConfigurationManager',
    'initialize_comprehensive_config',
    'ensure_comprehensive_config_exists',
    'get_configuration_status',
    'is_configuration_complete',
    'reset_configuration'
]
