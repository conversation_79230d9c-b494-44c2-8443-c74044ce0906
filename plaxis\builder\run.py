import logging

logger = logging.getLogger(__name__)


def run_plaxis(g_i, max_cores=None, boundary_condition=4, view_last_phase=True):
    """
    Configure and execute PLAXIS calculation for specified phases.

    Parameters:
        g_i: PLAXIS input object
        end_phase_num: Maximum phase number to calculate (inclusive)
        max_cores: Number of CPU cores to use (None uses system default)
        boundary_condition: Boundary condition type (default: 4 - fully fixed)
        view_last_phase: Whether to open the viewer for the last phase after calculation

    Returns:
        bool: True if calculation completed successfully, False otherwise
    """
    logger.info(f"Configuring PLAXIS calculation...")

    # Configure calculation phases
    for phase_i in g_i.Phases:
        phase_i.ShouldCalculate = True

        phase_i.setproperties("MaxCores", 1)

        # Set deformation boundary conditions
        g_i.Deformations.BoundaryXMin.set(phase_i, boundary_condition)
        g_i.Deformations.BoundaryXMax.set(phase_i, boundary_condition)
        g_i.Deformations.BoundaryYMin.set(phase_i, boundary_condition)
        g_i.Deformations.BoundaryYMax.set(phase_i, boundary_condition)

    # Execute calculation
    logger.info("Starting PLAXIS calculation...")
    try:
        success = g_i.calculate()
        if success:
            logger.info("Calculation completed successfully.")
        else:
            logger.warning("Calculation failed or had convergence issues.")

        if view_last_phase and success:
            g_i.view(g_i.Phases[-1])

        return success
    except Exception as e:
        logger.error(f"Error during PLAXIS calculation: {str(e)}")
        return False
