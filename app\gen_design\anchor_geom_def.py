"""
PLAXIS Anchor Geometry Configuration Module

This module provides functionality for configuring anchor geometry coordinates
for different anchor types (N, F, G) in generative design iterations.
"""
from .anchors.geometry import CoordinateSet, GTypeCoordinateSet, GeometryUIComponents

"""
PLAXIS Anchor Geometry Configuration Module

This module provides functionality for configuring anchor geometry coordinates
for different anchor types (N, F, G) in generative design iterations.
"""
import logging
import tkinter as tk
from tkinter import messagebox, ttk
from typing import Dict, List, Optional, Any

# Import shared utilities and constants
from .common import (
    FONT_REGULAR, FONT_BOLD, BG_COLOR, ANCHOR_TYPES, create_scrollable_content_area, create_primary_button, create_danger_button, create_secondary_button,
    create_success_button, create_info_label, show_temporary_status,
    safe_float_conversion, handle_error_with_logging,
    handle_validation_error
)

# Coordinate field labels


def create_geometry_window(parent_window: tk.Widget) -> tk.Toplevel:
    """
    Create and configure the geometry editing window.
    
    Args:
        parent_window: The parent window
        
    Returns:
        The configured geometry window
    """
    from .common.window_utils import create_geometry_window as create_geom_win
    return create_geom_win(parent_window)


def create_anchor_type_selection(window: tk.Toplevel, ui_components: GeometryUIComponents, selected_stage: Optional[str] = None) -> tk.Frame:
    """
    Create the anchor type selection dropdown with stage information.
    
    Args:
        window: The geometry window
        ui_components: UI components manager
        selected_stage: The selected stage to display
        
    Returns:
        The frame containing the selection UI
    """
    selection_frame = tk.Frame(window, bg=BG_COLOR)
    selection_frame.pack(fill="x", padx=20, pady=15)
    
    # Stage information label (left side)
    stage_info_label = create_info_label(selection_frame, "Design Stage:")
    stage_info_label.pack(side=tk.LEFT, padx=5)
    
    stage_value = selected_stage if selected_stage else "Not specified"
    ui_components.stage_label = create_info_label(selection_frame, stage_value)
    ui_components.stage_label.pack(side=tk.LEFT, padx=5)
    
    # Separator
    separator_label = create_info_label(selection_frame, "|")
    separator_label.pack(side=tk.LEFT, padx=15)
    
    # Anchor type label
    anchor_type_label = create_info_label(selection_frame, "Select Anchor Type:", font=FONT_BOLD)
    anchor_type_label.pack(side=tk.LEFT, padx=5)
    
    # Dropdown
    ui_components.anchor_type_var = tk.StringVar(window)
    ui_components.anchor_type_var.set(ANCHOR_TYPES[0])  # Default to N
    
    style = ttk.Style()
    style.configure('TCombobox', padding=(5, 7), font=FONT_REGULAR)
    
    type_dropdown = ttk.Combobox(selection_frame, 
                                textvariable=ui_components.anchor_type_var,
                                values=ANCHOR_TYPES, 
                                width=10,
                                font=FONT_REGULAR, 
                                style='TCombobox',
                                state="readonly")
    type_dropdown.pack(side=tk.LEFT, padx=5)
    
    # Add status label for showing when defaults are loaded
    ui_components.status_label = tk.Label(
        selection_frame,
        text="",
        font=FONT_REGULAR,
        bg=BG_COLOR,
        fg="#4CAF50"  # Green color for success messages
    )
    ui_components.status_label.pack(side=tk.LEFT, padx=20)
    
    return selection_frame


def create_coordinate_input_area(window: tk.Toplevel, ui_components: GeometryUIComponents) -> tk.Frame:
    """
    Create the coordinate input area with scrollable content.
    
    Args:
        window: The geometry window
        ui_components: UI components manager
        
    Returns:
        The frame containing the coordinate input area
    """
    main_frame, canvas, scrollable_frame = create_scrollable_content_area(window)
    
    # Store reference to content frame
    ui_components.content_frame = scrollable_frame
    
    return main_frame


def create_add_remove_buttons(window: tk.Toplevel, ui_components: GeometryUIComponents) -> tk.Frame:
    """
    Create add/remove buttons for anchor management (F2/G2 anchors).
    
    Args:
        window: The geometry window
        ui_components: UI components manager
        
    Returns:
        The frame containing the buttons
    """
    buttons_frame = tk.Frame(window, bg=BG_COLOR)
    # Don't pack the frame initially - it will be shown/hidden based on anchor type
    
    # Store reference to the frame
    ui_components.anchor_management_frame = buttons_frame
    
    # Add anchor button (for F2/G2)
    ui_components.add_anchor_button = create_primary_button(
        buttons_frame,
        "Add Anchor",
        lambda: add_anchor_handler(ui_components),
        width=18, height=1
    )
    
    # Remove anchor button (for F2/G2)
    ui_components.remove_anchor_button = create_danger_button(
        buttons_frame,
        "Remove Anchor",
        lambda: remove_anchor_handler(ui_components),
        width=18, height=1
    )
    
    return buttons_frame


def create_action_buttons(window: tk.Toplevel, ui_components: GeometryUIComponents, app_instance=None) -> tk.Frame:
    """
    Create save/cancel buttons for the geometry window.
    
    Args:
        window: The geometry window
        ui_components: UI components manager
        
    Returns:
        The frame containing the action buttons
    """
    actions_frame = tk.Frame(window, bg=BG_COLOR)
    actions_frame.pack(fill="x", padx=20, pady=20)
    
    # Cancel button
    create_secondary_button(
        actions_frame,
        "Cancel", 
        window.destroy,
        width=12, height=1,
        pack_side=tk.RIGHT
    )
    
    # Save button
    create_success_button(
        actions_frame,
        "Save",
        lambda: save_geometry_configuration(window, ui_components, app_instance),
        width=12, height=1,
        pack_side=tk.RIGHT
    )
    
    return actions_frame


def _populate_anchor_sections_with_defaults(ui_components: GeometryUIComponents, anchor_type: str,
                                            default_coordinates: List[Dict[str, float]]):
    """
    Populate anchor sections with default coordinate values.
    
    Args:
        ui_components: UI components manager
        anchor_type: The anchor type (N, F, G)
        default_coordinates: List of coordinate dictionaries to populate
    """
    try:
        # Determine which anchor sections to populate based on anchor type
        sections_to_populate = []
        
        if anchor_type == "N":
            sections_to_populate = ["N"]
        elif anchor_type == "F":
            sections_to_populate = ["F1"]
            # F2 would be added by user interaction if needed
        elif anchor_type == "G":
            sections_to_populate = ["G1"]
            # G2 would be added by user interaction if needed
        
        # Populate each section with available coordinate sets
        for section_name in sections_to_populate:
            if section_name in ui_components.anchor_sections:
                anchor_section = ui_components.anchor_sections[section_name]
                
                # Clear existing coordinate sets first
                anchor_section.clear_all_sets()
                
                # Add coordinate sets based on default data
                for i, coord_data in enumerate(default_coordinates):
                    # Add a new coordinate set
                    set_number = len(anchor_section.coordinate_sets) + 1
                    row = len(anchor_section.coordinate_sets) + 1  # Start from row 1 (row 0 is for anchor name)
                    
                    # Use appropriate coordinate set based on anchor type
                    if section_name.startswith('G'):
                        # G-type anchors only need X1, Y1
                        coord_set = GTypeCoordinateSet(anchor_section.frame, set_number, row)
                    else:
                        # N-type and F-type anchors need X1, Y1, X2, Y2
                        coord_set = CoordinateSet(anchor_section.frame, set_number, row)
                    
                    anchor_section.coordinate_sets.append(coord_set)
                    
                    # Set the default values
                    coord_set.set_values(coord_data)
                
                # Update button states
                anchor_section.update_button_states()
                
                logging.info(f"Populated {len(default_coordinates)} coordinate sets for section {section_name}")
    
    except Exception as e:
        logging.error(f"Error populating anchor sections with defaults: {e}")

def _get_default_anchor_name(excel_master: Any, anchor_type: str, anchor_section: str, selected_stage: Optional[str]) -> str:
    """
    Get the default anchor name from Excel data or generate a default name.
    
    Args:
        excel_master: ExcelMaster object containing anchor data
        anchor_type: The anchor type (N, F, G)
        anchor_section: The anchor section (N, F1, F2, G1, G2)
        selected_stage: The selected stage
        
    Returns:
        Default anchor name
    """
    try:
        # Check if Excel data is available
        if not excel_master or not hasattr(excel_master, 'AnchorLoc') or excel_master.AnchorLoc.empty:
            return f"Anchor_{selected_stage}_{anchor_section}" if selected_stage else f"Anchor_{anchor_section}"
        
        # Convert stage to int for filtering
        stage_filter = None
        if selected_stage:
            try:
                stage_filter = int(selected_stage)
            except (ValueError, TypeError):
                return f"Anchor_{selected_stage}_{anchor_section}"
        
        # Filter anchor data by type and stage
        anchor_data = excel_master.AnchorLoc
        
        # Filter by anchor type
        type_filtered = anchor_data[anchor_data['Anchor Type (F/N/G)'] == anchor_type]
        
        # Further filter by stage if specified
        if stage_filter is not None:
            stage_filtered = type_filtered[type_filtered['Stage'] == stage_filter]
        else:
            stage_filtered = type_filtered
        
        # Try to find a matching anchor name
        if not stage_filtered.empty:
            # Get the first anchor name that matches the criteria
            first_anchor = stage_filtered.iloc[0]
            excel_name = first_anchor.get('Anchor Name', '')
            if excel_name:
                return str(excel_name)
        
        # If no matching name found, generate default name
        return f"Anchor_{selected_stage}_{anchor_section}" if selected_stage else f"Anchor_{anchor_section}"
        
    except Exception as e:
        logging.error(f"Error getting default anchor name: {e}")
        return f"Anchor_{selected_stage}_{anchor_section}" if selected_stage else f"Anchor_{anchor_section}"

def get_default_coordinates_from_excel(excel_master: Any, anchor_type: str, selected_stage: Optional[str]) -> List[Dict[str, float]]:
    """
    Extract default coordinate values from Excel data for the specified anchor type and stage.
    
    Args:
        excel_master: ExcelMaster object containing anchor location data
        anchor_type: The anchor type (N, F, G) to filter by
        selected_stage: The stage to filter by (can be None)
        
    Returns:
        List of coordinate dictionaries with X1, Y1, X2, Y2 keys for N/F types
        or X1, Y1 keys only for G types
    """
    try:
        # Check if Excel data is available
        if not hasattr(excel_master, 'AnchorLoc') or excel_master.AnchorLoc.empty:
            logging.info("No anchor location data available in Excel")
            return []
        
        # Convert stage to int for filtering
        stage_filter = None
        if selected_stage:
            try:
                stage_filter = int(selected_stage)
            except (ValueError, TypeError):
                logging.warning(f"Invalid stage value for filtering: {selected_stage}")
                return []
        
        # Filter anchor data by type and stage
        anchor_data = excel_master.AnchorLoc
        
        # Filter by anchor type
        type_filtered = anchor_data[anchor_data['Anchor Type (F/N/G)'] == anchor_type]
        
        # Further filter by stage if specified
        if stage_filter is not None:
            stage_filtered = type_filtered[type_filtered['Stage'] == stage_filter]
        else:
            stage_filtered = type_filtered
        
        if stage_filtered.empty:
            logging.info(f"No anchor data found for type {anchor_type}" + 
                        (f" and stage {stage_filter}" if stage_filter else ""))
            return []
        
        # Extract coordinate data
        coordinate_list = []
        for _, row in stage_filtered.iterrows():
            try:
                if anchor_type == "G":
                    # G-type anchors only need X1, Y1
                    coordinates = {
                        'X1': safe_float_conversion(row.get('X1 (m)', 0.0), 0.0),
                        'Y1': safe_float_conversion(row.get('Y1 (m)', 0.0), 0.0)
                    }
                else:
                    # N-type and F-type anchors need X1, Y1, X2, Y2
                    coordinates = {
                        'X1': safe_float_conversion(row.get('X1 (m)', 0.0), 0.0),
                        'Y1': safe_float_conversion(row.get('Y1 (m)', 0.0), 0.0),
                        'X2': safe_float_conversion(row.get('X2 (m)', 0.0), 0.0),
                        'Y2': safe_float_conversion(row.get('Y2 (m)', 0.0), 0.0)
                    }
                coordinate_list.append(coordinates)
            except (ValueError, TypeError) as e:
                logging.warning(f"Error extracting coordinates from row: {e}")
                continue
        
        logging.info(f"Found {len(coordinate_list)} coordinate sets for anchor type {anchor_type}" +
                    (f" in stage {stage_filter}" if stage_filter else ""))
        return coordinate_list
        
    except Exception as e:
        logging.error(f"Error extracting default coordinates from Excel: {e}")
        return []


def update_coordinate_fields(ui_components: GeometryUIComponents, excel_master: Optional[Any] = None,
                             selected_stage: Optional[str] = None):
    """
    Update coordinate fields based on selected anchor type with default values from Excel data.
    
    Args:
        ui_components: UI components manager
        excel_master: ExcelMaster object containing anchor data (optional)
        selected_stage: The stage to filter Excel data by (optional)
    """
    # Store excel_master and selected_stage for use in other functions
    ui_components.excel_master = excel_master
    ui_components.selected_stage = selected_stage
    
    anchor_type = ui_components.anchor_type_var.get()
    
    # Clear existing anchor sections
    ui_components.clear_all_anchors()
    
    # Create anchor sections based on anchor type with default anchor names
    if anchor_type == "N":
        # Type N: Only one anchor_n allowed per stage
        section = ui_components.add_anchor_section("N")
        default_name = _get_default_anchor_name(excel_master, anchor_type, "N", selected_stage)
        section.set_anchor_name(default_name)
    
    elif anchor_type == "F":
        # Type F: anchor_f1 (required), anchor_f2 (optional)
        section = ui_components.add_anchor_section("F1")
        default_name = _get_default_anchor_name(excel_master, anchor_type, "F1", selected_stage)
        section.set_anchor_name(default_name)
        # F2 can be added via button
    
    elif anchor_type == "G":
        # Type G: anchor_g1 (required), anchor_g2 (optional)
        section = ui_components.add_anchor_section("G1")
        default_name = _get_default_anchor_name(excel_master, anchor_type, "G1", selected_stage)
        section.set_anchor_name(default_name)
        # G2 can be added via button
    
    # Populate default values from Excel data if available
    if excel_master and selected_stage:
        default_coordinates = get_default_coordinates_from_excel(excel_master, anchor_type, selected_stage)
        
        if default_coordinates:
            _populate_anchor_sections_with_defaults(ui_components, anchor_type, default_coordinates)
            logging.info(f"Populated {len(default_coordinates)} default coordinate sets for anchor type {anchor_type}")
            
            # Show status message to user
            if hasattr(ui_components, 'status_label') and ui_components.status_label:
                info_text = f"✓ Loaded {len(default_coordinates)} coordinate set(s) from Excel data"
                show_temporary_status(ui_components.status_label, info_text, duration=5000)
        else:
            logging.info(f"No default coordinates found for anchor type {anchor_type} in stage {selected_stage}")
            # Clear any previous status messages
            if hasattr(ui_components, 'status_label') and ui_components.status_label:
                ui_components.status_label.config(text="")
    
    # Update anchor management buttons
    ui_components.update_anchor_management_buttons()
    ui_components.current_anchor_type = anchor_type
    
    logging.info(f"Updated coordinate fields for anchor type {anchor_type}")


def add_anchor_handler(ui_components: GeometryUIComponents):
    """
    Handle adding a new anchor (F2 or G2).
    
    Args:
        ui_components: UI components manager
    """
    anchor_type = ui_components.anchor_type_var.get()
    
    if anchor_type == "F":
        if "F2" not in ui_components.anchor_sections:
            section = ui_components.add_anchor_section("F2")
            default_name = _get_default_anchor_name(ui_components.excel_master, anchor_type, "F2", ui_components.selected_stage)
            section.set_anchor_name(default_name)
            # Sync F2 to match F1's coordinate set count
            section.sync_to_paired_anchor()
            ui_components.update_anchor_management_buttons()
            logging.info("Added F2 anchor section")
        else:
            messagebox.showwarning("Already Exists", "F2 anchor already exists")
    
    elif anchor_type == "G":
        if "G2" not in ui_components.anchor_sections:
            section = ui_components.add_anchor_section("G2")
            default_name = _get_default_anchor_name(ui_components.excel_master, anchor_type, "G2", ui_components.selected_stage)
            section.set_anchor_name(default_name)
            # Sync G2 to match G1's coordinate set count
            section.sync_to_paired_anchor()
            ui_components.update_anchor_management_buttons()
            logging.info("Added G2 anchor section")
        else:
            messagebox.showwarning("Already Exists", "G2 anchor already exists")


def remove_anchor_handler(ui_components: GeometryUIComponents):
    """
    Handle removing an anchor (F2 or G2).
    
    Args:
        ui_components: UI components manager
    """
    anchor_type = ui_components.anchor_type_var.get()
    
    if anchor_type == "F":
        if "F2" in ui_components.anchor_sections:
            ui_components.remove_anchor_section("F2")
            ui_components.update_anchor_management_buttons()
            logging.info("Removed F2 anchor section")
        else:
            messagebox.showwarning("Not Found", "F2 anchor does not exist")
    
    elif anchor_type == "G":
        if "G2" in ui_components.anchor_sections:
            ui_components.remove_anchor_section("G2")
            ui_components.update_anchor_management_buttons()
            logging.info("Removed G2 anchor section")
        else:
            messagebox.showwarning("Not Found", "G2 anchor does not exist")





def validate_geometry_data(ui_components: GeometryUIComponents) -> Dict[str, Any]:
    """
    Validate and collect geometry data from the UI.
    
    Args:
        ui_components: UI components manager
        
    Returns:
        Dictionary containing validated geometry data
        
    Raises:
        ValueError: If validation fails
    """
    anchor_type = ui_components.anchor_type_var.get()
    
    if not ui_components.anchor_sections:
        raise ValueError("No anchor sections defined")
    
    geometry_data = {
        "anchor_type": anchor_type,
        "anchors": {}
    }
    
    # Validate each anchor section
    for anchor_name, anchor_section in ui_components.anchor_sections.items():
        try:
            anchor_data = anchor_section.get_all_values()
            if not anchor_data or not anchor_data.get("coordinates"):
                raise ValueError(f"No coordinate sets defined for {anchor_name}")
            geometry_data["anchors"][anchor_name] = anchor_data
        except ValueError as e:
            raise ValueError(f"Validation error in {anchor_name}: {str(e)}")
    
    return geometry_data


def save_geometry_configuration(window: tk.Toplevel, ui_components: GeometryUIComponents, app_instance=None):
    """
    Save the geometry configuration and close the window.
    
    Args:
        window: The geometry window
        ui_components: UI components manager
    """
    try:
        # Validate the geometry data
        geometry_data = validate_geometry_data(ui_components)
        
        # Store the geometry data in the app instance if provided
        if app_instance:
            app_instance.geometry_configuration = geometry_data
            
            # Update comprehensive configuration using the integrated system
            if app_instance:
                try:
                    from .config.details import update_anchor_geometry_config
                    stage = getattr(ui_components, 'selected_stage', None)
                    update_anchor_geometry_config(app_instance, geometry_data, stage)
                except ImportError:
                    logging.warning("Could not import update_anchor_geometry_config function")
                except Exception as e:
                    logging.error(f"Error updating comprehensive configuration: {str(e)}")
        
        # Log the saved configuration
        logging.info(f"Geometry configuration saved for anchor type {geometry_data['anchor_type']}")
        
        # Display success message with detailed summary
        anchor_type = geometry_data['anchor_type']
        summary_lines = []
        summary_lines.append(f"Anchor Type: {anchor_type}")
        summary_lines.append("")
        
        total_iterations = 1
        for anchor_name, anchor_data in geometry_data['anchors'].items():
            coordinate_sets = anchor_data.get('coordinates', [])
            actual_anchor_name = anchor_data.get('anchor_name', anchor_name)
            set_count = len(coordinate_sets)
            summary_lines.append(f"{anchor_name}:")
            summary_lines.append(f"  - Anchor Name: {actual_anchor_name}")
            summary_lines.append(f"  - Number of coordinate sets: {set_count}")
            
            for i, coord_set in enumerate(coordinate_sets, 1):
                coord_str = ", ".join([f"{k}: {v}" for k, v in coord_set.items()])
                summary_lines.append(f"  - Set {i}: {coord_str}")
            
            summary_lines.append("")
            total_iterations *= set_count
        
        # Calculate total possible iterations
        summary_lines.append(f"Total possible iterations: {total_iterations}")
        
        summary_text = "\n".join(summary_lines)
        
        logging.info(f"Total anchors configured: {len(geometry_data['anchors'])}")
        logging.info(f"Total possible iterations: {total_iterations}")
        
        messagebox.showinfo("Configuration Saved", 
                           f"Geometry configuration saved successfully!\n\n{summary_text}")
        
        # Close the window
        window.destroy()
        
    except ValueError as e:
        handle_validation_error(str(e), "Validation Error")
    except Exception as e:
        handle_error_with_logging(e, "Error saving geometry configuration", 
                                 "Failed to save configuration")


def edit_anchor_geometry(self, anchor_table: ttk.Treeview, progress_window: tk.Widget, 
                        selected_stage: Optional[str] = None) -> None:
    """
    Open a new window to edit geometry coordinates for anchor types.
    
    This function creates a modal window for configuring anchor geometry
    coordinates for different anchor types (N, F, G) with their specific
    requirements and constraints.
    
    Args:
        self: The application instance
        anchor_table: The anchor table widget containing anchor data
        progress_window: The parent window
        selected_stage: The stage value selected in the parent window
        
    Returns:
        None
        
    Raises:
        Exception: If there are errors in UI creation or data processing
    """
    try:
        # Create geometry window
        geom_window = create_geometry_window(progress_window)
        
        # Initialize UI components manager
        ui_components = GeometryUIComponents()
        
        # Create UI sections
        create_anchor_type_selection(geom_window, ui_components, selected_stage)
        create_coordinate_input_area(geom_window, ui_components)
        create_add_remove_buttons(geom_window, ui_components)
        create_action_buttons(geom_window, ui_components, self)
        
        # Set up anchor type change handler
        def on_anchor_type_change(*args):
            update_coordinate_fields(ui_components, self.excel_master, selected_stage)
        
        ui_components.anchor_type_var.trace("w", on_anchor_type_change)
        
        # Initialize with default anchor type and populate with Excel data
        update_coordinate_fields(ui_components, self.excel_master, selected_stage)
        
        logging.info(f"Anchor geometry configuration window created successfully")
        
    except Exception as e:
        handle_error_with_logging(e, "Error creating anchor geometry window", 
                                 "Failed to create anchor geometry window")