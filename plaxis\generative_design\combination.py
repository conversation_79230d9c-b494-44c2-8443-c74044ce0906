import itertools
from typing import List, Tuple, Dict, Any, Union

import pandas as pd
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# Constants for anchor types and column patterns
ANCHOR_TYPE_STRUT = ['N', 'F']
ANCHOR_TYPE_GROUND = ['G']
VALID_ANCHOR_TYPES = ANCHOR_TYPE_STRUT + ANCHOR_TYPE_GROUND

# Column patterns for different anchor types
NAIL_FRICTION_COLUMNS = ['Name', 'Section', 'Unit', 'Spacing (m)', 'Prestress (kN/m)']
GROUND_COLUMNS = ['Name', 'Section', 'Unit', 'Spacing (m)', 'Theta_s (deg)', 'Theta_g (deg)', 'Length (m)']


def validate_anchors_input(anchors: List[Any]) -> None:
    """
    Validates the anchors input parameter.
    
    Args:
        anchors: List of anchor objects to validate
        
    Raises:
        ValueError: If anchors is empty or invalid
        AttributeError: If anchor objects are missing required attributes
    """
    if not anchors:
        raise ValueError("Anchors list cannot be empty")
    
    if not isinstance(anchors, list):
        raise TypeError("Anchors must be a list")
    
    for i, anchor in enumerate(anchors):
        # Check for required attributes
        required_attrs = ['type', 'name', 'list_section_unit']
        for attr in required_attrs:
            if not hasattr(anchor, attr):
                raise AttributeError(f"Anchor {i+1} is missing required attribute: {attr}")
        
        # Validate anchor type
        if anchor.type not in VALID_ANCHOR_TYPES:
            raise ValueError(f"Invalid anchor type '{anchor.type}' for anchor {i+1}. "
                           f"Valid types are: {VALID_ANCHOR_TYPES}")
        
        # Check type-specific attributes
        if anchor.type in ANCHOR_TYPE_STRUT:
            type_specific_attrs = ['list_spacing', 'list_prestress']
        elif anchor.type in ANCHOR_TYPE_GROUND:
            type_specific_attrs = ['list_spacing', 'list_theta_s', 'list_theta_g', 'list_length']
        
        for attr in type_specific_attrs:
            if not hasattr(anchor, attr):
                raise AttributeError(f"Anchor {i+1} of type '{anchor.type}' is missing attribute: {attr}")


def validate_anchor_type(anchor_type: str) -> None:
    """
    Validates the anchor_type parameter.
    
    Args:
        anchor_type: The anchor type to validate
        
    Raises:
        ValueError: If anchor_type is invalid
    """
    if anchor_type not in VALID_ANCHOR_TYPES:
        raise ValueError(f"Invalid anchor_type '{anchor_type}'. "
                        f"Valid types are: {VALID_ANCHOR_TYPES}")


def get_anchor_type_columns(anchor_type: str, anchor_index: int) -> List[str]:
    """
    Gets the column names for a specific anchor type and index.
    
    Args:
        anchor_type: Type of anchor ('N', 'F', or 'G')
        anchor_index: Zero-based index of the anchor
        
    Returns:
        List of column names for the anchor
    """
    prefix = f"Anchor{anchor_index + 1}_"
    
    if anchor_type in ANCHOR_TYPE_STRUT:
        return [f"{prefix}{col}" for col in NAIL_FRICTION_COLUMNS]
    elif anchor_type in ANCHOR_TYPE_GROUND:
        return [f"{prefix}{col}" for col in GROUND_COLUMNS]
    else:
        raise ValueError(f"Unsupported anchor type: {anchor_type}")


def generate_anchor_columns(anchors: List[Any]) -> List[str]:
    """
    Generates column headers for the DataFrame based on anchor types.
    
    Args:
        anchors: List of anchor objects
        
    Returns:
        List of column names for the DataFrame
    """
    columns = ['Phase Number', 'Identification']
    
    for i, anchor in enumerate(anchors):
        anchor_columns = get_anchor_type_columns(anchor.type, i)
        columns.extend(anchor_columns)
    
    return columns


def validate_anchor_parameters(anchor: Any) -> None:
    """
    Validates that anchor has non-empty parameter lists.
    
    Args:
        anchor: Anchor object to validate
        
    Raises:
        ValueError: If any parameter list is empty
    """
    if not anchor.list_section_unit:
        raise ValueError(f"Anchor '{anchor.name}' has empty list_section_unit")
    
    if anchor.type in ANCHOR_TYPE_STRUT:
        if not anchor.list_spacing:
            raise ValueError(f"Anchor '{anchor.name}' has empty list_spacing")
        if not anchor.list_prestress:
            raise ValueError(f"Anchor '{anchor.name}' has empty list_prestress")
    elif anchor.type in ANCHOR_TYPE_GROUND:
        if not anchor.list_spacing:
            raise ValueError(f"Anchor '{anchor.name}' has empty list_spacing")
        if not anchor.list_theta_s:
            raise ValueError(f"Anchor '{anchor.name}' has empty list_theta_s")
        if not anchor.list_theta_g:
            raise ValueError(f"Anchor '{anchor.name}' has empty list_theta_g")
        if not anchor.list_length:
            raise ValueError(f"Anchor '{anchor.name}' has empty list_length")


def generate_strut_anchor_combinations(anchor: Any) -> List[Tuple]:
    """
    Generates parameter combinations for strut/nail/friction anchors using itertools.product.
    
    Args:
        anchor: Strut anchor object with parameter lists
        
    Returns:
        List of parameter tuples (section, unit, spacing, prestress)
    """
    logger.debug(f"Generating combinations for strut anchor '{anchor.name}': "
                f"{len(anchor.list_section_unit)} sections, "
                f"{len(anchor.list_spacing)} spacings, "
                f"{len(anchor.list_prestress)} prestress values")
    
    # Use itertools.product for efficient Cartesian product
    combinations = list(itertools.product(
        anchor.list_section_unit,      # [(section, unit), ...]
        anchor.list_spacing,           # [spacing1, spacing2, ...]
        anchor.list_prestress          # [prestress1, prestress2, ...]
    ))
    
    # Flatten the section_unit tuples to match expected format
    flattened_combinations = []
    for (section, unit), spacing, prestress in combinations:
        flattened_combinations.append((section, unit, spacing, prestress))
    
    logger.info(f"Generated {len(flattened_combinations)} combinations for strut anchor '{anchor.name}'")
    return flattened_combinations


def generate_ground_anchor_combinations(anchor: Any) -> List[Tuple]:
    """
    Generates parameter combinations for ground anchors using itertools.product.
    
    Args:
        anchor: Ground anchor object with parameter lists
        
    Returns:
        List of parameter tuples (section, unit, spacing, theta_s, theta_g, length)
    """
    logger.debug(f"Generating combinations for ground anchor '{anchor.name}': "
                f"{len(anchor.list_section_unit)} sections, "
                f"{len(anchor.list_theta_s)} theta_s values, "
                f"{len(anchor.list_spacing)} spacings, "
                f"{len(anchor.list_theta_g)} theta_g values, "
                f"{len(anchor.list_length)} lengths")
    
    # Use itertools.product for efficient Cartesian product
    combinations = list(itertools.product(
        anchor.list_section_unit,      # [(section, unit), ...]
        anchor.list_theta_s,           # [theta_s1, theta_s2, ...]
        anchor.list_spacing,           # [spacing1, spacing2, ...]
        anchor.list_theta_g,           # [theta_g1, theta_g2, ...]
        anchor.list_length             # [length1, length2, ...]
    ))
    
    # Flatten the section_unit tuples to match expected format
    flattened_combinations = []
    for (section, unit), theta_s, spacing, theta_g, length in combinations:
        flattened_combinations.append((section, unit, spacing, theta_s, theta_g, length))
    
    logger.info(f"Generated {len(flattened_combinations)} combinations for ground anchor '{anchor.name}'")
    return flattened_combinations


def generate_single_anchor_combinations(anchor: Any) -> List[Tuple]:
    """
    Generates all parameter combinations for a single anchor using optimized algorithms.
    
    This function uses itertools.product() instead of nested loops for better performance
    and readability. It also includes parameter validation and detailed logging.
    
    Args:
        anchor: Anchor object with parameter lists
        
    Returns:
        List of parameter tuples for the anchor
        
    Raises:
        ValueError: If anchor has invalid type or empty parameter lists
    """
    # Validate anchor parameters before processing
    validate_anchor_parameters(anchor)
    
    logger.info(f"Generating combinations for anchor '{anchor.name}' of type '{anchor.type}'")
    
    try:
        if anchor.type in ANCHOR_TYPE_STRUT:
            return generate_strut_anchor_combinations(anchor)
        elif anchor.type in ANCHOR_TYPE_GROUND:
            return generate_ground_anchor_combinations(anchor)
        else:
            raise ValueError(f"Unsupported anchor type '{anchor.type}' for anchor '{anchor.name}'")
            
    except Exception as e:
        logger.error(f"Error generating combinations for anchor '{anchor.name}': {e}")
        raise


def generate_all_anchor_combinations(anchors: List[Any]) -> List[Tuple]:
    """
    Generates all possible combinations across all anchors using Cartesian product.
    
    This function processes each anchor to generate its parameter combinations,
    then creates the Cartesian product of all anchor combinations for comprehensive
    testing scenarios.
    
    Args:
        anchors: List of anchor objects
        
    Returns:
        List of combination tuples, where each tuple contains parameter combinations for all anchors
        
    Raises:
        ValueError: If any anchor has no parameter combinations
    """
    logger.info(f"Starting combination generation for {len(anchors)} anchors")
    
    anchor_param_lists = []
    total_individual_combinations = 1
    
    for i, anchor in enumerate(anchors):
        try:
            logger.debug(f"Processing anchor {i+1}/{len(anchors)}: '{anchor.name}' (type: {anchor.type})")
            
            anchor_combinations = generate_single_anchor_combinations(anchor)
            
            if not anchor_combinations:
                raise ValueError(f"No combinations generated for anchor '{anchor.name}' of type '{anchor.type}'")
            
            anchor_param_lists.append(anchor_combinations)
            total_individual_combinations *= len(anchor_combinations)
            
            logger.debug(f"Anchor '{anchor.name}' generated {len(anchor_combinations)} combinations")
            
        except Exception as e:
            logger.error(f"Failed to generate combinations for anchor '{anchor.name}': {e}")
            raise
    
    logger.info(f"Total individual anchor combinations: {total_individual_combinations}")
    logger.info("Generating Cartesian product of all anchor combinations...")
    
    try:
        # Generate Cartesian product of all anchor combinations
        all_combinations = list(itertools.product(*anchor_param_lists))
        
        logger.info(f"Successfully generated {len(all_combinations)} total combinations across {len(anchors)} anchors")
        
        # Log memory usage warning for large combinations
        if len(all_combinations) > 10000:
            logger.warning(f"Large number of combinations ({len(all_combinations)}) may consume significant memory")
        
        return all_combinations
        
    except MemoryError as e:
        logger.error(f"Memory error during combination generation: {e}")
        logger.error(f"Consider reducing parameter lists. Current total: {total_individual_combinations}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during combination generation: {e}")
        raise


def create_nail_friction_identification(anchor_name: str, params: Tuple) -> str:
    """
    Creates identification string for nail/friction anchor parameters.
    
    Args:
        anchor_name: Name of the anchor
        params: Tuple containing (section, unit, spacing, prestress)
        
    Returns:
        Formatted identification string
        
    Raises:
        ValueError: If params tuple has incorrect length
    """
    if len(params) != 4:
        raise ValueError(f"Expected 4 parameters for strut anchor, got {len(params)}")
    
    section, unit, spacing, prestress = params
    return f"({anchor_name})_{unit}x{section}_S{spacing}m_{prestress}kN/m"


def create_ground_identification(anchor_name: str, params: Tuple) -> str:
    """
    Creates identification string for ground anchor parameters.
    
    Args:
        anchor_name: Name of the anchor
        params: Tuple containing (section, unit, spacing, theta_s, theta_g, length)
        
    Returns:
        Formatted identification string
        
    Raises:
        ValueError: If params tuple has incorrect length
    """
    if len(params) != 6:
        raise ValueError(f"Expected 6 parameters for ground anchor, got {len(params)}")
    
    section, unit, spacing, theta_s, theta_g, length = params
    return f"({anchor_name})_{unit}x{section}_S{spacing}m_As{theta_s}deg_Ag{theta_g}deg_L{length}m"


def create_identification_string(anchors: List[Any], combination: Tuple, anchor_type: str) -> str:
    """
    Creates a comprehensive identification string for a combination of anchor parameters.
    
    Args:
        anchors: List of anchor objects
        combination: Tuple of parameter combinations for all anchors
        anchor_type: Type of anchors being processed
        
    Returns:
        Formatted identification string
        
    Raises:
        ValueError: If anchor_type is unsupported or combination length doesn't match anchors
    """
    if len(combination) != len(anchors):
        raise ValueError(f"Combination length ({len(combination)}) doesn't match number of anchors ({len(anchors)})")
    
    id_parts = []
    
    for j, anchor_params in enumerate(combination):
        try:
            anchor_name = anchors[j].name
            
            if anchor_type in ANCHOR_TYPE_STRUT:
                id_part = create_nail_friction_identification(anchor_name, anchor_params)
            elif anchor_type in ANCHOR_TYPE_GROUND:
                id_part = create_ground_identification(anchor_name, anchor_params)
            else:
                raise ValueError(f"Unsupported anchor type for identification: {anchor_type}")
            
            id_parts.append(id_part)
            
        except Exception as e:
            logger.error(f"Error creating identification for anchor {j+1}: {e}")
            raise
    
    return "_".join(id_parts)


def build_dataframe_row(phase_num: int, anchors: List[Any], combination: Tuple, anchor_type: str) -> List[Any]:
    """
    Builds a single row for the DataFrame from a combination of anchor parameters.
    
    Args:
        phase_num: Phase number for this combination
        anchors: List of anchor objects
        combination: Tuple of parameter combinations for all anchors
        anchor_type: Type of anchors being processed
        
    Returns:
        List representing a row in the DataFrame
    """
    # Start with phase number
    row = [phase_num]
    
    # Add identification string
    identification = create_identification_string(anchors, combination, anchor_type)
    row.append(identification)
    
    # Add all parameters to the row
    for j, anchor_params in enumerate(combination):
        anchor_name = anchors[j].name
        
        if anchor_type in ANCHOR_TYPE_STRUT:
            section, unit, spacing, prestress = anchor_params
            row.extend([anchor_name, section, unit, spacing, prestress])
        elif anchor_type in ANCHOR_TYPE_GROUND:
            section, unit, spacing, theta_s, theta_g, length = anchor_params
            row.extend([anchor_name, section, unit, spacing, theta_s, theta_g, length])
    
    return row


def process_combinations_to_rows(anchors: List[Any], combinations: List[Tuple], anchor_type: str) -> List[List[Any]]:
    """
    Processes all combinations into DataFrame rows.
    
    Args:
        anchors: List of anchor objects
        combinations: List of all parameter combinations
        anchor_type: Type of anchors being processed
        
    Returns:
        List of rows for the DataFrame
    """
    rows = []
    
    for phase_num, combination in enumerate(combinations, 1):
        try:
            row = build_dataframe_row(phase_num, anchors, combination, anchor_type)
            rows.append(row)
        except Exception as e:
            logger.error(f"Error processing combination {phase_num}: {e}")
            raise
    
    logger.info(f"Processed {len(rows)} combinations into DataFrame rows")
    return rows


def create_anchor_combinations_log(anchors: List[Any], anchor_type: str) -> pd.DataFrame:
    """
    Creates a DataFrame with all possible combinations of anchor parameters for testing in the same phase.
    
    This function orchestrates the entire process of generating parameter combinations,
    creating identification strings, and building a structured DataFrame for analysis.
    
    Args:
        anchors: List of anchor objects, each with type-specific parameter lists:
                - For types 'N'/'F': list_section_unit, list_spacing, list_prestress
                - For type 'G': list_section_unit, list_spacing, list_theta_s, 
                                list_theta_g, list_length
        anchor_type: Type of anchors being processed ('N', 'F', or 'G')
    
    Returns:
        pd.DataFrame: DataFrame containing all parameter combinations with columns for:
                     - Phase Number: Sequential numbering of combinations
                     - Identification: Descriptive string for each combination
                     - Parameter columns for each anchor (name, section, unit, spacing, etc.)
    
    Raises:
        ValueError: If inputs are invalid or no combinations can be generated
        AttributeError: If anchor objects are missing required attributes
        TypeError: If inputs are not of expected types
    """
    # Validate inputs
    validate_anchors_input(anchors)
    validate_anchor_type(anchor_type)
    
    logger.info(f"Creating combinations log for {len(anchors)} anchors of type '{anchor_type}'")
    
    try:
        # Generate column headers
        columns = generate_anchor_columns(anchors)
        
        # Generate all parameter combinations
        all_combinations = generate_all_anchor_combinations(anchors)
        
        # Process combinations into DataFrame rows
        rows = process_combinations_to_rows(anchors, all_combinations, anchor_type)
        
        # Create and validate the DataFrame
        df_autorunlog = pd.DataFrame(rows, columns=columns)
        
        if df_autorunlog.empty:
            raise ValueError("Generated DataFrame is empty")
        
        logger.info(f"Successfully created combinations log with {len(df_autorunlog)} parameter combinations")
        return df_autorunlog
        
    except Exception as e:
        logger.error(f"Error creating anchor combinations log: {e}")
        raise
