# PLAXIS Package Installation Guide

This guide provides step-by-step instructions for copying PLAXIS packages (`plxscripting` and `plxencryption`) from the PLAXIS Python Distribution to a new Python virtual environment for use with Nuitka compilation.

## 📋 Overview

The PLAXIS Python Distribution contains proprietary packages that are required for PLAXIS automation but are not available through standard package managers like pip or conda. To use these packages with Nuitka or other Python environments, they must be manually copied from the PLAXIS installation.

## 📦 Required Packages

### 1. plxscripting (v1.0.7)
- **Purpose**: Main PLAXIS scripting API
- **Key Module**: `plxscripting.easy` (contains `new_server` function)
- **Dependencies**: `requests`
- **License**: Plaxis Public License 1.0

### 2. plxencryption (v1.0.0)
- **Purpose**: Handles transparent encryption/decryption for PLAXIS API communications
- **Dependencies**: `pycryptodome`
- **License**: Plaxis Public License 1.0

## 🎯 Prerequisites

1. **PLAXIS Installation**: PLAXIS software must be installed on your system
2. **Python Virtual Environment**: A Python virtual environment where you want to install the packages
3. **Administrative Access**: May be required to access PLAXIS installation directory
4. **Python 3.6+**: Compatible Python version (supports 3.6-3.12)

## 📍 Source Locations

The PLAXIS packages are located in the PLAXIS Python Distribution:

```
C:\ProgramData\Seequent\PLAXIS Python Distribution V3\python\Lib\site-packages\
├── plxscripting\                    # Complete package directory
├── plxscripting-1.0.7.dist-info\   # Package metadata
├── encryption.py                    # plxencryption module
└── plxencryption-1.0.0.dist-info\  # Package metadata
```

## 🔧 Step-by-Step Installation

### Step 1: Verify Source Packages Exist

Open PowerShell as Administrator and verify the PLAXIS packages exist:

```powershell
# Check if plxscripting directory exists
Test-Path "C:\ProgramData\Seequent\PLAXIS Python Distribution V3\python\Lib\site-packages\plxscripting"

# Check if encryption.py exists
Test-Path "C:\ProgramData\Seequent\PLAXIS Python Distribution V3\python\Lib\site-packages\encryption.py"
```

**Expected Output**: Both commands should return `True`

### Step 2: Identify Your Target Environment

Determine where you want to install the packages. For a virtual environment named `.venv`:

```powershell
# Navigate to your project directory
cd "C:\path\to\your\project"

# Verify virtual environment exists
Test-Path ".venv\Lib\site-packages"
```

**Expected Output**: Should return `True` if virtual environment is properly set up

### Step 3: Copy plxscripting Package

Copy the entire plxscripting package directory:

```powershell
# Copy plxscripting package (recursive copy)
Copy-Item -Path "C:\ProgramData\Seequent\PLAXIS Python Distribution V3\python\Lib\site-packages\plxscripting" -Destination ".venv\Lib\site-packages\" -Recurse -Force
```

**Expected Output**: No output on success

**Verification**:
```powershell
# Verify plxscripting was copied
Get-ChildItem ".venv\Lib\site-packages\plxscripting" -Name | Select-Object -First 5
```

**Expected Output**: Should show files like `easy.py`, `connection.py`, `console.py`, etc.

### Step 4: Copy plxencryption Module

Copy the encryption.py file:

```powershell
# Copy plxencryption file
Copy-Item -Path "C:\ProgramData\Seequent\PLAXIS Python Distribution V3\python\Lib\site-packages\encryption.py" -Destination ".venv\Lib\site-packages\" -Force
```

**Expected Output**: No output on success

**Verification**:
```powershell
# Verify encryption.py was copied
Test-Path ".venv\Lib\site-packages\encryption.py"
```

**Expected Output**: Should return `True`

### Step 5: Install Dependencies

Install the required dependencies using pip:

```powershell
# Install dependencies in the virtual environment
.venv\Scripts\pip.exe install requests pycryptodome
```

**Expected Output**: 
```
Collecting requests
...
Successfully installed requests-x.x.x pycryptodome-x.x.x
```

### Step 6: Verify Installation

Test that the packages can be imported successfully:

```powershell
# Test imports
.venv\Scripts\python.exe -c "from plxscripting.easy import new_server; import encryption; print('✅ All packages working!')"
```

**Expected Output**: 
```
✅ All packages working!
```

## 📁 Complete Directory Structure

After successful installation, your virtual environment should contain:

```
.venv\
└── Lib\
    └── site-packages\
        ├── plxscripting\
        │   ├── __init__.py
        │   ├── easy.py              # Contains new_server function
        │   ├── connection.py
        │   ├── console.py
        │   ├── const.py
        │   ├── error_mode.py
        │   ├── image.py
        │   ├── interactive.py
        │   ├── logger.py
        │   ├── plaxis_connection.py
        │   ├── plaxis_jupyter.py
        │   ├── plxobjects.py
        │   ├── plxproxy.py
        │   ├── plxproxyfactory.py
        │   ├── plx_scripting_exceptions.py
        │   ├── run_jupyter.py
        │   ├── selection.py
        │   ├── server.py
        │   ├── tokenizer.py
        │   ├── utils.py
        │   ├── win32_plxutil.py
        │   ├── __version__.py
        │   ├── __pycache__\
        │   ├── template_notebooks\
        │   └── unittests\
        ├── encryption.py            # plxencryption module
        ├── requests\                # Dependency
        ├── pycryptodome\           # Dependency
        └── ... (other packages)
```

## 🧪 Testing Your Installation

### Basic Import Test

Create a test script to verify the installation:

```python
# test_plaxis_installation.py
try:
    # Test plxscripting import
    from plxscripting.easy import new_server
    print("✅ plxscripting.easy imported successfully")
    
    # Test plxencryption import
    import encryption
    print("✅ plxencryption imported successfully")
    
    # Test dependencies
    import requests
    print("✅ requests imported successfully")
    
    from Crypto.Cipher import AES  # from pycryptodome
    print("✅ pycryptodome imported successfully")
    
    print("\n🎉 All PLAXIS packages installed and working correctly!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
except Exception as e:
    print(f"❌ Unexpected error: {e}")
```

Run the test:
```powershell
.venv\Scripts\python.exe test_plaxis_installation.py
```

### Integration Test

Test creating a PLAXIS server connection (requires PLAXIS to be running):

```python
# test_plaxis_connection.py
from plxscripting.easy import new_server

try:
    # Attempt to connect to PLAXIS (this will fail if PLAXIS is not running)
    server, g_i = new_server('localhost', 10000, password='your_password')
    print("✅ Successfully connected to PLAXIS server")
    g_i.close()
except Exception as e:
    print(f"⚠️  Connection test failed (expected if PLAXIS not running): {e}")
    print("✅ But package import was successful!")
```

## 🚀 Using with Nuitka

Once the packages are installed in your virtual environment, you can compile your PLAXIS automation tool with Nuitka:

```powershell
# Activate virtual environment
.venv\Scripts\Activate.ps1

# Compile with Nuitka using the virtual environment's Python
& ".venv\Scripts\python.exe" -m nuitka --msvc=latest --standalone --enable-plugin=tk-inter --windows-console-mode=disable --windows-icon-from-ico=AIS.ico --include-data-dir=C:/Users/<USER>/CursorProjects/Plaxis-Automation/Library_Steel=Library_Steel --include-data-files=C:/Users/<USER>/CursorProjects/Plaxis-Automation/AIS.ico=AIS.ico --windows-company-name="Alex Sze" --windows-file-description="ELS Design RPA" --windows-product-version="1.8.1" main.py
```

## ⚠️ Important Notes

### License Compliance
- Both packages use the **Plaxis Public License 1.0 (PPL-1.0)**
- You must comply with the license terms when distributing applications
- The PPL requires sharing source code when you "Deploy" the software

### Version Compatibility
- **plxscripting v1.0.7**: Supports Python 3.6-3.12
- **plxencryption v1.0.0**: Supports Python 3.6-3.12
- Ensure your target Python version is compatible

### Security Considerations
- The encryption module handles sensitive PLAXIS communications
- Ensure your compiled application maintains proper security practices
- Keep credentials and passwords secure

### Troubleshooting Common Issues

#### Issue: "Access Denied" Error
```powershell
# Solution: Run PowerShell as Administrator
```

#### Issue: Package Not Found
```powershell
# Verify PLAXIS installation path
Get-ChildItem "C:\ProgramData\Seequent\" -Directory
```

#### Issue: Import Errors
```powershell
# Check Python version compatibility
.venv\Scripts\python.exe --version

# Verify package structure
Get-ChildItem ".venv\Lib\site-packages\plxscripting"
```

#### Issue: Missing Dependencies
```powershell
# Reinstall dependencies
.venv\Scripts\pip.exe install --force-reinstall requests pycryptodome
```

## 📞 Support

If you encounter issues:

1. **Check PLAXIS Documentation**: Official PLAXIS Python API documentation
2. **Verify File Permissions**: Ensure you have read access to PLAXIS installation
3. **Test Step by Step**: Follow each step carefully and verify outputs
4. **Check Logs**: Review any error messages in detail

## 📄 References

- **PLAXIS API Documentation**: [https://communities.bentley.com/products/geotech-analysis/w/plaxis-soilvision-wiki/45393/api-python-scripting---plaxis](https://communities.bentley.com/products/geotech-analysis/w/plaxis-soilvision-wiki/45393/api-python-scripting---plaxis)
- **Nuitka Documentation**: [https://nuitka.net/](https://nuitka.net/)
- **Virtual Environments**: [https://docs.python.org/3/tutorial/venv.html](https://docs.python.org/3/tutorial/venv.html)

---

**Last Updated**: July 13, 2025  
**Version**: 1.0  
**Tested With**: PLAXIS Python Distribution V3, Python 3.12
