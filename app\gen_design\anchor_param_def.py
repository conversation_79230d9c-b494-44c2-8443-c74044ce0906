import logging
import tkinter as tk
from tkinter import messagebox, ttk
import pandas as pd
import _Main_Class
from .anchors.parameters import DEFAULT_ANCHOR_VALUES, STEEL_SECTION_SHEETS, FALLBACK_SECTIONS, AnchorConfig, \
    UIComponentData

# Import shared utilities and constants
from .common import (
    FONT_REGULAR, FONT_BOLD, FONT_HEADER, ACCENT_COLOR, ERROR_COLOR, ENTRY_WIDTH, BUTTON_WIDTH, COMBO_WIDTH, PARAM_WINDOW_GEOMETRY, UNIT_OPTIONS,
    SETTLEMENT_COLUMNS, SETTLEMENT_DEFAULT_VALUES,
    create_scrollable_content_area, create_success_button, create_secondary_button, create_standard_frame
)
from .common.window_utils import create_parameter_window

# =============================================================================
# CONFIGURATION AND CONSTANTS
# =============================================================================

# UI Configuration (use shared constants where possible)

# Default Values

# Steel Sections Configuration

# =============================================================================
# DATA MODELS AND TYPE DEFINITIONS
# =============================================================================

from typing import Dict, List, Tuple, Any, Optional


# =============================================================================
# GENERIC UI COMPONENT BUILDERS
# =============================================================================

def create_dynamic_entry_component(parent_frame: tk.Widget, 
                                   title: str, 
                                   initial_values: List[str],
                                   entry_width: int = ENTRY_WIDTH) -> UIComponentData:
    """
    Create a dynamic entry component with add/remove functionality.
    
    Args:
        parent_frame: The parent widget to contain the component
        title: Title label for the component
        initial_values: List of initial values for entries
        entry_width: Width of entry widgets
        
    Returns:
        UIComponentData: Contains variables, entries, and frame references
    """
    entries_frame = create_standard_frame(parent_frame)
    entries_frame.pack(fill="both", expand=True, pady=5)
    
    # Add title
    tk.Label(entries_frame, text=title, font=FONT_BOLD).pack(anchor="w", pady=2)
    
    # Create container for entries
    values_frame = create_standard_frame(entries_frame)
    values_frame.pack(fill="x", pady=2)
    
    # Initialize variables and entries
    vars_list = [tk.StringVar(value=val) for val in initial_values]
    entries_list = []
    
    # Create initial entries
    for var in vars_list:
        entry = tk.Entry(values_frame, textvariable=var, width=entry_width)
        entry.pack(anchor="w", pady=2)
        entries_list.append(entry)
    
    component_data = UIComponentData(vars_list, entries_list, values_frame)
    
    # Create add/remove buttons using common utilities
    btn_frame = create_standard_frame(entries_frame)
    btn_frame.pack(fill="x", pady=5)
    
    # Create add button
    add_btn = tk.Button(btn_frame, text="+", width=BUTTON_WIDTH, 
                       command=lambda: add_entry_to_component(component_data, "", entry_width),
                       bg=ACCENT_COLOR, fg="white")
    add_btn.pack(side=tk.LEFT, padx=2)
    
    # Create remove button
    remove_btn = tk.Button(btn_frame, text="-", width=BUTTON_WIDTH,
                          command=lambda: remove_entry_from_component(component_data),
                          bg=ERROR_COLOR, fg="white")
    remove_btn.pack(side=tk.LEFT, padx=2)
    
    return component_data


def add_entry_to_component(component_data: UIComponentData,
                           default_value: str = "",
                           entry_width: int = ENTRY_WIDTH) -> None:
    """
    Add a new entry to a dynamic component.
    
    Args:
        component_data: The component data structure
        default_value: Default value for new entry
        entry_width: Width of the new entry
    """
    var = tk.StringVar(value=default_value)
    component_data.vars_list.append(var)
    
    entry = tk.Entry(component_data.parent_frame, textvariable=var, width=entry_width)
    entry.pack(anchor="w", pady=2)
    component_data.entries_list.append(entry)


def remove_entry_from_component(component_data: UIComponentData) -> None:
    """
    Remove the last entry from a dynamic component.
    
    Args:
        component_data: The component data structure
    """
    if len(component_data.entries_list) > 1:
        # Remove the last variable and entry
        last_var = component_data.vars_list.pop()
        last_entry = component_data.entries_list.pop()
        last_entry.destroy()


def create_combo_selection_component(parent_frame: tk.Widget,
                                    section_options: List[str],
                                    unit_options: List[str], 
                                    default_combos: List[Tuple[str, int]]) -> UIComponentData:
    """
    Create a section-unit combination selector component.
    
    Args:
        parent_frame: Parent frame for the component
        section_options: Available section options
        unit_options: Available unit options
        default_combos: Default section-unit combinations
        
    Returns:
        UIComponentData: Component data structure
    """
    combo_frame = create_standard_frame(parent_frame)
    combo_frame.pack(fill="both", expand=True, pady=5)
    
    # Headers
    section_header = tk.Label(combo_frame, text="Section", font=FONT_BOLD)
    section_header.grid(row=0, column=0, padx=5, pady=2)
    
    unit_header = tk.Label(combo_frame, text="Unit", font=FONT_BOLD)
    unit_header.grid(row=0, column=1, padx=5, pady=2)
    
    combo_vars = []
    entries_list = []  # Store combo boxes as entries
    
    # Create initial combos
    for j, (section, unit) in enumerate(default_combos, 1):
        section_var = tk.StringVar(value=section)
        section_dropdown = ttk.Combobox(combo_frame, textvariable=section_var,
                                       values=section_options, width=COMBO_WIDTH, state="readonly")
        section_dropdown.grid(row=j, column=0, padx=5, pady=2)
        
        unit_var = tk.StringVar(value=str(unit))
        unit_dropdown = ttk.Combobox(combo_frame, textvariable=unit_var,
                                    values=unit_options, width=5, state="readonly")
        unit_dropdown.grid(row=j, column=1, padx=5, pady=2)
        
        combo_vars.append({'section': section_var, 'unit': unit_var})
        entries_list.extend([section_dropdown, unit_dropdown])
    
    component_data = UIComponentData(combo_vars, entries_list, combo_frame)
    
    # Add/remove buttons
    add_btn = tk.Button(combo_frame, text="+", width=BUTTON_WIDTH)
    remove_btn = tk.Button(combo_frame, text="-", width=BUTTON_WIDTH)
    
    # Configure button commands
    add_btn.configure(command=lambda: add_combo_to_component(
        component_data, section_options, unit_options, add_btn, remove_btn))
    remove_btn.configure(command=lambda: remove_combo_from_component(
        component_data, add_btn, remove_btn))
    
    # Position buttons
    add_btn.grid(row=len(default_combos) + 1, column=0, padx=5, pady=2)
    remove_btn.grid(row=len(default_combos) + 1, column=1, padx=5, pady=2)
    
    return component_data


def add_combo_to_component(component_data: UIComponentData,
                           section_options: List[str],
                           unit_options: List[str],
                           add_btn: tk.Button,
                           remove_btn: tk.Button) -> None:
    """Add a new combo selection to the component."""
    row_num = len(component_data.vars_list) + 1
    
    # Remove buttons temporarily
    add_btn.grid_forget()
    remove_btn.grid_forget()
    
    # Create new combo boxes
    section_var = tk.StringVar(value=section_options[0] if section_options else "")
    section_dropdown = ttk.Combobox(component_data.parent_frame, textvariable=section_var,
                                   values=section_options, width=COMBO_WIDTH, state="readonly")
    section_dropdown.grid(row=row_num, column=0, padx=5, pady=2)
    
    unit_var = tk.StringVar(value="1")
    unit_dropdown = ttk.Combobox(component_data.parent_frame, textvariable=unit_var,
                                values=unit_options, width=5, state="readonly")
    unit_dropdown.grid(row=row_num, column=1, padx=5, pady=2)
    
    # Update component data
    component_data.vars_list.append({'section': section_var, 'unit': unit_var})
    component_data.entries_list.extend([section_dropdown, unit_dropdown])
    
    # Reposition buttons
    add_btn.grid(row=row_num + 1, column=0, padx=5, pady=2)
    remove_btn.grid(row=row_num + 1, column=1, padx=5, pady=2)


def remove_combo_from_component(component_data: UIComponentData,
                                add_btn: tk.Button,
                                remove_btn: tk.Button) -> None:
    """Remove the last combo selection from the component."""
    if len(component_data.vars_list) > 1:
        # Remove buttons temporarily
        add_btn.grid_forget()
        remove_btn.grid_forget()
        
        # Remove the last combo vars and entries
        last_combo_vars = component_data.vars_list.pop()
        
        # Remove the last two entries (section and unit dropdowns)
        for _ in range(2):
            if component_data.entries_list:
                last_entry = component_data.entries_list.pop()
                last_entry.destroy()
        
        # Reposition buttons
        new_row = len(component_data.vars_list) + 1
        add_btn.grid(row=new_row, column=0, padx=5, pady=2)
        remove_btn.grid(row=new_row, column=1, padx=5, pady=2)


def create_settlement_component(parent_frame: tk.Widget) -> UIComponentData:
    """
    Create a settlement configuration component.
    
    Args:
        parent_frame: Parent frame for the component
        
    Returns:
        UIComponentData: Component data structure
    """
    settlement_container = create_standard_frame(parent_frame)
    settlement_container.pack(fill="x", padx=5, pady=5)
    
    # Configure columns
    for i in range(4):
        settlement_container.columnconfigure(i, weight=1, uniform="settlement_col")
    
    # Create header row
    header_row = create_standard_frame(settlement_container)
    header_row.pack(fill="x")
    
    for i in range(4):
        header_row.columnconfigure(i, weight=1, uniform="settlement_col")
    
    # Add headers
    for i, header in enumerate(SETTLEMENT_COLUMNS):
        tk.Label(header_row, text=header, font=FONT_BOLD).grid(
            row=0, column=i, padx=5, sticky="w")
    
    # Add separator
    separator = ttk.Separator(settlement_container, orient="horizontal")
    separator.pack(fill="x", pady=2)
    
    # Container for settlement rows
    settlement_rows_container = create_standard_frame(settlement_container)
    settlement_rows_container.pack(fill="x")
    
    for i in range(4):
        settlement_rows_container.columnconfigure(i, weight=1, uniform="settlement_col")
    
    # Initialize with one row
    settlement_vars = []
    settlement_rows = []
    
    # Create first row
    vars_dict = {col: tk.StringVar(value=val) 
                for col, val in zip(SETTLEMENT_COLUMNS, SETTLEMENT_DEFAULT_VALUES)}
    
    row_frame = create_standard_frame(settlement_rows_container)
    row_frame.pack(fill="x", pady=2)
    
    # Configure columns
    for i in range(4):
        row_frame.columnconfigure(i, weight=1, uniform="settlement_col")
    
    # Create entry fields
    for i, col in enumerate(SETTLEMENT_COLUMNS):
        entry = tk.Entry(row_frame, textvariable=vars_dict[col], width=8)
        entry.grid(row=0, column=i, padx=5, sticky="w")
    
    settlement_vars.append(vars_dict)
    settlement_rows.append(row_frame)
    
    # Create add/remove buttons
    btn_frame = create_standard_frame(settlement_container)
    btn_frame.pack(fill="x", pady=5)
    
    component_data = UIComponentData(settlement_vars, settlement_rows, settlement_rows_container)
    
    add_btn = tk.Button(btn_frame, text="+", width=BUTTON_WIDTH,
                       command=lambda: add_settlement_row(component_data),
                       bg=ACCENT_COLOR, fg="white")
    add_btn.pack(side=tk.LEFT, padx=2)
    
    remove_btn = tk.Button(btn_frame, text="-", width=BUTTON_WIDTH,
                          command=lambda: remove_settlement_row(component_data),
                          bg=ERROR_COLOR, fg="white")
    remove_btn.pack(side=tk.LEFT, padx=2)
    
    return component_data


def add_settlement_row(component_data: UIComponentData) -> None:
    """
    Add a new settlement row to the component.
    
    Args:
        component_data: The settlement component data structure
    """
    # Create variables for the new row
    vars_dict = {col: tk.StringVar(value=val) 
                for col, val in zip(SETTLEMENT_COLUMNS, SETTLEMENT_DEFAULT_VALUES)}
    
    # Create frame for this row
    row_frame = create_standard_frame(component_data.parent_frame)
    row_frame.pack(fill="x", pady=2)
    
    # Configure columns
    for i in range(4):
        row_frame.columnconfigure(i, weight=1, uniform="settlement_col")
    
    # Create entry fields
    for i, col in enumerate(SETTLEMENT_COLUMNS):
        entry = tk.Entry(row_frame, textvariable=vars_dict[col], width=8)
        entry.grid(row=0, column=i, padx=5, sticky="w")
    
    # Add to component data
    component_data.vars_list.append(vars_dict)
    component_data.entries_list.append(row_frame)


def remove_settlement_row(component_data: UIComponentData) -> None:
    """
    Remove the last settlement row from the component.
    
    Args:
        component_data: The settlement component data structure
    """
    if len(component_data.entries_list) > 1:
        # Remove the last row
        last_vars = component_data.vars_list.pop()
        last_row = component_data.entries_list.pop()
        last_row.destroy()

# =============================================================================
# DATA LOADING AND VALIDATION
# =============================================================================

def load_steel_sections(anchor_type: str) -> List[str]:
    """
    Load steel sections from Excel file or return fallback options.
    
    Args:
        anchor_type: The anchor type (N, F, G)
        
    Returns:
        List of available steel sections
    """
    try:
        import pandas as pd
        import os
        
        # Try to load sections from Excel file
        excel_path = os.path.join("Library_Steel", "ELS_Steel_Section.xlsx")
        
        if os.path.exists(excel_path):
            sheet_name = STEEL_SECTION_SHEETS.get(anchor_type, "Anchor_List_N")
            df = pd.read_excel(excel_path, sheet_name=sheet_name)
            
            if 'Section' in df.columns:
                sections = df['Section'].dropna().unique().tolist()
                return [str(section) for section in sections]
    
    except Exception as e:
        logging.warning(f"Could not load steel sections from Excel: {e}")
    
    # Return fallback sections
    return FALLBACK_SECTIONS


def parse_anchor_data_from_geometry(geometry_config: Dict[str, Any]) -> Dict[str, List[AnchorConfig]]:
    """
    Parse anchor data from geometry configuration into structured format.
    
    Args:
        geometry_config: The geometry configuration dictionary
        
    Returns:
        Dictionary mapping anchor types to lists of anchor configurations
    """
    anchors_by_type = {"N": [], "F": [], "G": []}
    
    try:
        anchor_type = geometry_config.get('anchor_type', '')
        anchors_data = geometry_config.get('anchors', {})
        
        for anchor_section, anchor_data in anchors_data.items():
            # Get anchor name from geometry config
            anchor_name = anchor_data.get('anchor_name', f'Anchor_{anchor_section}')
            
            # Create anchor config with defaults
            anchor_config = AnchorConfig(
                name=anchor_name,
                anchor_type=anchor_type,
                section='',  # Will be populated from defaults
                unit='1',    # Default unit
                spacing='3', # Default spacing
                prestress='0' # Default prestress
            )
            
            # Map anchor section to anchor type
            if anchor_section in ['N']:
                anchors_by_type['N'].append(anchor_config)
            elif anchor_section in ['F1', 'F2']:
                anchors_by_type['F'].append(anchor_config)
            elif anchor_section in ['G1', 'G2']:
                anchors_by_type['G'].append(anchor_config)
        
        return anchors_by_type
        
    except Exception as e:
        logging.error(f"Error parsing anchor data from geometry: {str(e)}")
        return {"N": [], "F": [], "G": []}

def parse_anchor_data(anchor_table: ttk.Treeview) -> Dict[str, List[AnchorConfig]]:
    """
    Parse anchor data from the table into structured format.
    
    Args:
        anchor_table: The anchor table widget
        
    Returns:
        Dictionary mapping anchor types to lists of anchor configurations
    """
    anchors_by_type = {"N": [], "F": [], "G": []}
    
    for item_id in anchor_table.get_children():
        values = anchor_table.item(item_id, "values")
        if len(values) >= 6:
            anchor_config = AnchorConfig(
                name=values[0],
                anchor_type=values[1],
                section=values[2],
                unit=values[3],
                spacing=values[4],
                prestress=values[5]
            )
            
            if anchor_config.anchor_type in anchors_by_type:
                anchors_by_type[anchor_config.anchor_type].append(anchor_config)
    
    return anchors_by_type


def validate_anchor_parameters(anchor_ui_components: Dict[str, Any]) -> bool:
    """
    Validate anchor parameters before processing.
    
    Args:
        anchor_ui_components: Dictionary containing UI component data
        
    Returns:
        True if validation passes, False otherwise
    """
    try:
        for anchor_name, components in anchor_ui_components.items():
            # Validate combo selections
            if 'combo_vars' in components:
                for combo in components['combo_vars']:
                    if not combo['section'].get() or not combo['unit'].get():
                        logging.warning(f"Empty section/unit for anchor {anchor_name}")
                        return False
            
            # Validate numeric entries
            for key in ['spacing_vars', 'prestress_vars', 'theta_s_vars', 'theta_g_vars', 'anchor_length_vars']:
                if key in components:
                    for var in components[key]:
                        try:
                            float(var.get())
                        except ValueError:
                            logging.warning(f"Invalid numeric value in {key} for anchor {anchor_name}")
                            return False
        
        return True
        
    except Exception as e:
        logging.error(f"Error during parameter validation: {str(e)}")
        return False

# =============================================================================
# ANCHOR TYPE SPECIFIC HANDLERS
# =============================================================================

def create_anchor_type_n_ui(anchors: List[AnchorConfig],
                            type_frame: tk.Widget,
                            anchor_ui_components: Dict[str, Any]) -> None:
    """
    Create UI components for Type N anchors.
    
    Args:
        anchors: List of N-type anchor configurations
        type_frame: Parent frame for the anchor type
        anchor_ui_components: Dictionary to store UI component references
    """
    # Create horizontal container
    n_anchors_container = create_standard_frame(type_frame)
    n_anchors_container.pack(fill="x", padx=5, pady=5)
    
    section_options = load_steel_sections("N")
    
    for i, anchor in enumerate(anchors):
        anchor_name = anchor.name
        anchor_ui_components[anchor_name] = {}
        
        # Create anchor frame
        anchor_frame = tk.LabelFrame(n_anchors_container, text=f"Anchor: {anchor_name}",
                                   font=FONT_BOLD)
        col_index = i % 2
        row_index = i // 2
        anchor_frame.grid(row=row_index, column=col_index, sticky="nsew", padx=5, pady=5)
        
        # Create parameter frame
        param_frame = tk.Frame(anchor_frame)
        param_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Create entries frame
        entries_frame = tk.Frame(param_frame)
        entries_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Create subframes
        section_unit_frame = tk.Frame(entries_frame, width=180)
        section_unit_frame.grid(row=0, column=0, padx=5, sticky="n")
        section_unit_frame.grid_propagate(False)
        
        spacing_frame = tk.Frame(entries_frame, width=140)
        spacing_frame.grid(row=0, column=1, padx=5, sticky="n")
        spacing_frame.grid_propagate(False)
        
        prestress_frame = tk.Frame(entries_frame, width=140)
        prestress_frame.grid(row=0, column=2, padx=5, sticky="n")
        prestress_frame.grid_propagate(False)
        
        # Section-Unit combo component
        combo_data = create_combo_selection_component(
            section_unit_frame, section_options, UNIT_OPTIONS, 
            DEFAULT_ANCHOR_VALUES["N"]["combos"])
        anchor_ui_components[anchor_name]['combo_vars'] = combo_data.vars_list
        
        # Spacing component
        spacing_data = create_dynamic_entry_component(
            spacing_frame, "Spacing (m)", [anchor.spacing])
        anchor_ui_components[anchor_name]['spacing_vars'] = spacing_data.vars_list
        anchor_ui_components[anchor_name]['spacing_entries'] = spacing_data.entries_list
        
        # Prestress component
        prestress_data = create_dynamic_entry_component(
            prestress_frame, "Prestress (kN/m)", [anchor.prestress])
        anchor_ui_components[anchor_name]['prestress_vars'] = prestress_data.vars_list
        anchor_ui_components[anchor_name]['prestress_entries'] = prestress_data.entries_list
        
        # Criteria frame
        criteria_frame = tk.LabelFrame(param_frame, text="Criteria")
        criteria_frame.pack(fill="x", padx=5, pady=5, expand=True)
        
        # Settlement component
        settlement_data = create_settlement_component(criteria_frame)
        anchor_ui_components[anchor_name]['settlement_rows'] = settlement_data.entries_list
        anchor_ui_components[anchor_name]['settlement_vars'] = settlement_data.vars_list


def create_anchor_type_f_ui(anchors: List[AnchorConfig],
                            type_frame: tk.Widget,
                            anchor_ui_components: Dict[str, Any]) -> None:
    """
    Create UI components for Type F anchors.
    
    Args:
        anchors: List of F-type anchor configurations
        type_frame: Parent frame for the anchor type
        anchor_ui_components: Dictionary to store UI component references
    """
    # Create horizontal container
    f_anchors_container = create_standard_frame(type_frame)
    f_anchors_container.pack(fill="x", padx=5, pady=5)
    
    section_options = load_steel_sections("F")  # F type now has its own configuration
    
    for i, anchor in enumerate(anchors):
        anchor_name = anchor.name
        anchor_ui_components[anchor_name] = {}
        
        # Create anchor frame
        anchor_frame = tk.LabelFrame(f_anchors_container, text=f"Anchor: {anchor_name}",
                                   font=FONT_BOLD)
        col_index = i % 2
        row_index = i // 2
        anchor_frame.grid(row=row_index, column=col_index, sticky="nsew", padx=5, pady=5)
        
        # Create parameter frame
        param_frame = tk.Frame(anchor_frame)
        param_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Create entries frame
        entries_frame = tk.Frame(param_frame)
        entries_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Create subframes
        section_unit_frame = tk.Frame(entries_frame, width=180)
        section_unit_frame.grid(row=0, column=0, padx=5, sticky="n")
        section_unit_frame.grid_propagate(False)
        
        spacing_frame = tk.Frame(entries_frame, width=140)
        spacing_frame.grid(row=0, column=1, padx=5, sticky="n")
        spacing_frame.grid_propagate(False)
        
        prestress_frame = tk.Frame(entries_frame, width=140)
        prestress_frame.grid(row=0, column=2, padx=5, sticky="n")
        prestress_frame.grid_propagate(False)
        
        # Section-Unit combo component
        combo_data = create_combo_selection_component(
            section_unit_frame, section_options, UNIT_OPTIONS, 
            DEFAULT_ANCHOR_VALUES["F"]["combos"])  # F now uses its own explicit defaults
        anchor_ui_components[anchor_name]['combo_vars'] = combo_data.vars_list
        
        # Spacing component
        spacing_data = create_dynamic_entry_component(
            spacing_frame, "Spacing (m)", [anchor.spacing])
        anchor_ui_components[anchor_name]['spacing_vars'] = spacing_data.vars_list
        anchor_ui_components[anchor_name]['spacing_entries'] = spacing_data.entries_list
        
        # Prestress component
        prestress_data = create_dynamic_entry_component(
            prestress_frame, "Prestress (kN/m)", [anchor.prestress])
        anchor_ui_components[anchor_name]['prestress_vars'] = prestress_data.vars_list
        anchor_ui_components[anchor_name]['prestress_entries'] = prestress_data.entries_list
        
        # Criteria frame
        criteria_frame = tk.LabelFrame(param_frame, text="Criteria")
        criteria_frame.pack(fill="x", padx=5, pady=5, expand=True)
        
        # Settlement component
        settlement_data = create_settlement_component(criteria_frame)
        anchor_ui_components[anchor_name]['settlement_rows'] = settlement_data.entries_list
        anchor_ui_components[anchor_name]['settlement_vars'] = settlement_data.vars_list
        
        logging.info(f"Created Type F UI for anchor: {anchor_name} with same defaults as Type N")


def create_anchor_type_g_ui(anchors: List[AnchorConfig],
                            type_frame: tk.Widget,
                            anchor_ui_components: Dict[str, Any]) -> None:
    """
    Create UI components for Type G anchors.
    
    Args:
        anchors: List of G-type anchor configurations
        type_frame: Parent frame for the anchor type
        anchor_ui_components: Dictionary to store UI component references
    """
    # Create horizontal container
    g_anchors_container = tk.Frame(type_frame)
    g_anchors_container.pack(fill="x", padx=5, pady=5)
    
    section_options = load_steel_sections("G")
    
    for i, anchor in enumerate(anchors):
        anchor_name = anchor.name
        anchor_ui_components[anchor_name] = {}
        
        # Create anchor frame
        anchor_frame = tk.LabelFrame(g_anchors_container, text=f"Anchor: {anchor_name}",
                                   font=FONT_BOLD)
        col_index = i % 2
        row_index = i // 2
        anchor_frame.grid(row=row_index, column=col_index, sticky="nsew", padx=5, pady=5)
        
        # Create parameter frame
        param_frame = tk.Frame(anchor_frame)
        param_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Create entries frame
        entries_frame = tk.Frame(param_frame)
        entries_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Create subframes for G-type specific parameters
        section_unit_frame = tk.Frame(entries_frame, width=180)
        section_unit_frame.grid(row=0, column=0, padx=5, sticky="n")
        section_unit_frame.grid_propagate(False)
        
        spacing_frame = tk.Frame(entries_frame, width=140)
        spacing_frame.grid(row=0, column=1, padx=5, sticky="n")
        spacing_frame.grid_propagate(False)
        
        theta_s_frame = tk.Frame(entries_frame, width=140)
        theta_s_frame.grid(row=0, column=2, padx=5, sticky="n")
        theta_s_frame.grid_propagate(False)
        
        theta_g_frame = tk.Frame(entries_frame, width=140)
        theta_g_frame.grid(row=0, column=3, padx=5, sticky="n")
        theta_g_frame.grid_propagate(False)
        
        anchor_length_frame = tk.Frame(entries_frame, width=140)
        anchor_length_frame.grid(row=0, column=4, padx=5, sticky="n")
        anchor_length_frame.grid_propagate(False)
        
        # Section-Unit combo component
        combo_data = create_combo_selection_component(
            section_unit_frame, section_options, UNIT_OPTIONS, 
            DEFAULT_ANCHOR_VALUES["G"]["combos"])
        anchor_ui_components[anchor_name]['combo_vars'] = combo_data.vars_list
        
        # Spacing component
        current_spacing = anchor.spacing if anchor.spacing else "3"
        spacing_data = create_dynamic_entry_component(
            spacing_frame, "Spacing (m)", [current_spacing])
        anchor_ui_components[anchor_name]['spacing_vars'] = spacing_data.vars_list
        anchor_ui_components[anchor_name]['spacing_entries'] = spacing_data.entries_list
        
        # Theta_s component
        theta_s_data = create_dynamic_entry_component(
            theta_s_frame, "Theta_s (deg)", DEFAULT_ANCHOR_VALUES["G"]["theta_s"][:1])
        anchor_ui_components[anchor_name]['theta_s_vars'] = theta_s_data.vars_list
        anchor_ui_components[anchor_name]['theta_s_entries'] = theta_s_data.entries_list
        
        # Theta_g component
        theta_g_data = create_dynamic_entry_component(
            theta_g_frame, "Theta_g (deg)", DEFAULT_ANCHOR_VALUES["G"]["theta_g"])
        anchor_ui_components[anchor_name]['theta_g_vars'] = theta_g_data.vars_list
        anchor_ui_components[anchor_name]['theta_g_entries'] = theta_g_data.entries_list
        
        # Anchor length component
        anchor_length_data = create_dynamic_entry_component(
            anchor_length_frame, "Anchor Length (m)", DEFAULT_ANCHOR_VALUES["G"]["anchor_length"])
        anchor_ui_components[anchor_name]['anchor_length_vars'] = anchor_length_data.vars_list
        anchor_ui_components[anchor_name]['anchor_length_entries'] = anchor_length_data.entries_list
        
        # Criteria frame
        criteria_frame = tk.LabelFrame(param_frame, text="Criteria")
        criteria_frame.pack(fill="x", padx=5, pady=5, expand=True)
        
        # Settlement component
        settlement_data = create_settlement_component(criteria_frame)
        anchor_ui_components[anchor_name]['settlement_rows'] = settlement_data.entries_list
        anchor_ui_components[anchor_name]['settlement_vars'] = settlement_data.vars_list

# =============================================================================
# WINDOW AND UI MANAGEMENT
# =============================================================================







def create_stage_selection_ui(scrollable_frame: tk.Widget, 
                             selected_stage: Optional[str] = None) -> tk.StringVar:
    """
    Create the stage selection UI component.
    
    Args:
        scrollable_frame: The scrollable frame container
        selected_stage: The initially selected stage
        
    Returns:
        StringVar containing the stage value
    """
    stage_value = selected_stage if selected_stage else "1"
    
    stage_frame = create_standard_frame(scrollable_frame)
    stage_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
    
    tk.Label(stage_frame, text="Design Stage:", font=FONT_BOLD).pack(side=tk.LEFT, padx=5)
    tk.Label(stage_frame, text=stage_value, font=FONT_REGULAR).pack(side=tk.LEFT, padx=5)
    
    return tk.StringVar(value=stage_value)


def create_action_buttons(param_window: tk.Toplevel, 
                         app_instance: Any) -> None:
    """
    Create the action buttons (Save/Cancel) at the bottom of the window.
    
    Args:
        param_window: The parameter window
        app_instance: The application instance for callbacks
    """
    # Import save function
    from .run import save_anchor_parameters
    
    # Add buttons at the bottom
    button_frame = create_standard_frame(param_window)
    button_frame.pack(side=tk.BOTTOM, pady=10, fill=tk.X)
    
    # Add separator
    ttk.Separator(param_window, orient="horizontal").pack(fill="x", pady=5)
    
    # Create action buttons frame
    action_buttons_frame = create_standard_frame(button_frame)
    action_buttons_frame.pack(fill=tk.X)
    
    # Save button using common utility
    create_success_button(action_buttons_frame, "Save",
                         lambda: save_anchor_parameters(app_instance),
                         width=15, height=2, pack_side=tk.RIGHT)
    
    # Cancel button using common utility
    create_secondary_button(action_buttons_frame, "Cancel",
                           param_window.destroy,
                           width=15, height=2, pack_side=tk.RIGHT)


# Legacy factory functions replaced by generic UI component builders above


def edit_anchor_parameters(self, 
                           anchor_table: ttk.Treeview, 
                           progress_window: tk.Widget, 
                           selected_stage: Optional[str] = None) -> None:
    """
    Open a new window to edit parameters for selected anchor types.
    
    This function creates a modal window for configuring anchor parameters
    based on the anchor data from the provided table. It supports different
    anchor types (N, F, G) with their specific parameter requirements.
    
    Args:
        self: The application instance
        anchor_table: The anchor table widget containing anchor data
        progress_window: The parent window
        selected_stage: The stage value selected in the parent window
        
    Returns:
        None
        
    Raises:
        Exception: If there are errors in UI creation or data processing
    """
    try:
        # Check if geometry has been defined
        if not hasattr(self, 'geometry_configuration') or not self.geometry_configuration:
            messagebox.showinfo("Geometry Required", 
                               "Please define anchor geometry first before editing parameters.\n\n"
                               "Use the 'Edit Anchor' button to configure anchor coordinates.")
            return
        
        # Parse anchor data from geometry configuration instead of table
        anchors_by_type = parse_anchor_data_from_geometry(self.geometry_configuration)
        
        # Create parameter window
        param_window = create_parameter_window(progress_window, selected_stage)
        
        # Store references in the class for access by other functions
        self.param_window = param_window
        self.anchors_by_type = anchors_by_type
        self.anchor_ui_components = {}
        self.anchor_table = anchor_table
        
        # Create scrollable content area
        main_frame, canvas, scrollable_frame = create_scrollable_content_area(param_window)
        
        # Create stage selection UI
        self.stage_var = create_stage_selection_ui(scrollable_frame, selected_stage)
        
        # Process each anchor type
        row = 1  # Start after stage selection
        for anchor_type, anchors in anchors_by_type.items():
            if not anchors:
                continue
            
            # Adjust window geometry based on anchor type
            if anchor_type in PARAM_WINDOW_GEOMETRY:
                param_window.geometry(PARAM_WINDOW_GEOMETRY[anchor_type])
            
            # Create type-specific frame
            type_frame = tk.LabelFrame(scrollable_frame, 
                                     text=f"Type {anchor_type} Anchors",
                                     font=FONT_HEADER)
            type_frame.grid(row=row, column=0, sticky="ew", padx=10, pady=10)
            row += 1
            
            # Create anchor type-specific UI
            if anchor_type == "N":
                create_anchor_type_n_ui(anchors, type_frame, self.anchor_ui_components)
            elif anchor_type == "F":
                create_anchor_type_f_ui(anchors, type_frame, self.anchor_ui_components)
            elif anchor_type == "G":
                create_anchor_type_g_ui(anchors, type_frame, self.anchor_ui_components)
            else:
                logging.warning(f"Unknown anchor type: {anchor_type}")
        
        # Create action buttons
        create_action_buttons(param_window, self)
        
        logging.info(f"Anchor parameters window created successfully with {len(self.anchor_ui_components)} anchors")
        
    except Exception as e:
        error_msg = f"Error creating anchor parameters window: {str(e)}"
        logging.error(error_msg)
        messagebox.showerror("Error", error_msg)
