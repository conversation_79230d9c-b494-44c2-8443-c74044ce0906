"""
Unified Anchor Configuration Module

This module provides functionality for configuring both anchor geometry and parameters
in a single unified interface for different anchor types (N, F, G) in generative design iterations.
"""
import logging
import tkinter as tk
from tkinter import messagebox, ttk
from typing import Dict, List, Optional, Any

# Import shared utilities and constants
from .common import (
    FONT_REGULAR, FONT_BOLD, FONT_HEADER, BG_COLOR, ANCHOR_TYPES, PARAM_WINDOW_GEOMETRY, COMBO_WIDTH,
    create_scrollable_content_area, create_primary_button, create_danger_button,
    create_secondary_button, create_success_button, create_info_label,
    show_temporary_status, safe_float_conversion, handle_error_with_logging,
    handle_validation_error, bind_mouse_wheel_globally
)

# Import unified anchor classes
from .anchor_class import UnifiedAnchorUIComponents, UnifiedAnchorData

# Import functions from existing modules for compatibility
from .anchor_geom_def import (
    get_default_coordinates_from_excel, _get_default_anchor_name,
    _populate_anchor_sections_with_defaults
)
from .anchor_param_def import (
    parse_anchor_data_from_geometry, validate_anchor_parameters,
    create_stage_selection_ui, create_action_buttons as create_param_action_buttons
)


def create_unified_anchor_window(parent_window: tk.Widget, selected_stage: Optional[str] = None) -> tk.Toplevel:
    """
    Create and configure the unified anchor editing window.
    
    Args:
        parent_window: The parent window
        selected_stage: The stage value selected in the parent window
        
    Returns:
        The configured unified anchor window
    """
    from .common.window_utils import create_modal_window
    from .common.ui_constants import DEFAULT_WINDOW_SIZES
    
    return create_modal_window(
        parent_window,
        "Unified Anchor Configuration",
        DEFAULT_WINDOW_SIZES.get("unified_anchor", "1000x700"),
        resizable=True,
        center=False
    )


def create_anchor_type_selection_section(window: tk.Toplevel, ui_components: UnifiedAnchorUIComponents,
                                        selected_stage: Optional[str] = None) -> tk.Frame:
    """
    Create the anchor type selection section with stage information.
    
    Args:
        window: The unified anchor window
        ui_components: UI components manager
        selected_stage: The selected stage to display
        
    Returns:
        The frame containing the selection UI
    """
    selection_frame = tk.Frame(window, bg=BG_COLOR)
    selection_frame.pack(fill="x", padx=20, pady=15)
    
    # Stage information label (left side)
    stage_info_label = create_info_label(selection_frame, "Design Stage:")
    stage_info_label.pack(side=tk.LEFT, padx=5)
    
    stage_value = selected_stage if selected_stage else "Not specified"
    ui_components.stage_label = create_info_label(selection_frame, stage_value)
    ui_components.stage_label.pack(side=tk.LEFT, padx=5)
    
    # Separator
    separator_label = create_info_label(selection_frame, "|")
    separator_label.pack(side=tk.LEFT, padx=15)
    
    # Anchor type label
    anchor_type_label = create_info_label(selection_frame, "Select Anchor Type:", font=FONT_BOLD)
    anchor_type_label.pack(side=tk.LEFT, padx=5)
    
    # Dropdown
    ui_components.anchor_type_var = tk.StringVar(window)
    ui_components.anchor_type_var.set(ANCHOR_TYPES[0])  # Default to N
    
    style = ttk.Style()
    style.configure('TCombobox', padding=(5, 7), font=FONT_REGULAR)
    
    type_dropdown = ttk.Combobox(selection_frame,
                                textvariable=ui_components.anchor_type_var,
                                values=ANCHOR_TYPES,
                                width=10,
                                font=FONT_REGULAR,
                                style='TCombobox',
                                state="readonly")
    type_dropdown.pack(side=tk.LEFT, padx=5)
    
    # Add status label for showing when defaults are loaded
    ui_components.status_label = tk.Label(
        selection_frame,
        text="",
        font=FONT_REGULAR,
        bg=BG_COLOR,
        fg="#4CAF50"  # Green color for success messages
    )
    ui_components.status_label.pack(side=tk.LEFT, padx=20)
    
    return selection_frame




def create_unified_content_area(window: tk.Toplevel, ui_components: UnifiedAnchorUIComponents) -> tk.Frame:
    """
    Create the unified content area with scrollable content for both geometry and parameters.
    
    Args:
        window: The unified anchor window
        ui_components: UI components manager
        
    Returns:
        The frame containing the content area
    """
    main_frame, canvas, scrollable_frame = create_scrollable_content_area(window)
    
    # Store reference to content frame and canvas
    ui_components.content_frame = scrollable_frame
    ui_components.canvas = canvas
    
    # Enable global mouse wheel scrolling across the entire window
    bind_mouse_wheel_globally(window, canvas)
    
    # Store the binding function for later use when new widgets are added
    ui_components.bind_mouse_wheel_to_new_widgets = lambda widget: bind_mouse_wheel_globally(widget, canvas)
    
    return main_frame


def create_anchor_management_buttons(window: tk.Toplevel, ui_components: UnifiedAnchorUIComponents) -> tk.Frame:
    """
    Create add/remove buttons for anchor management (F2/G2 anchors).
    
    Args:
        window: The unified anchor window
        ui_components: UI components manager
        
    Returns:
        The frame containing the buttons
    """
    buttons_frame = tk.Frame(window, bg=BG_COLOR)
    # Don't pack the frame initially - it will be shown/hidden based on anchor type
    
    # Store reference to the frame
    ui_components.anchor_management_frame = buttons_frame
    
    # Add anchor button (for F2/G2)
    ui_components.add_anchor_button = create_primary_button(
        buttons_frame,
        "Add Anchor",
        lambda: add_unified_anchor_handler(ui_components),
        width=18, height=1
    )
    
    # Remove anchor button (for F2/G2)
    ui_components.remove_anchor_button = create_danger_button(
        buttons_frame,
        "Remove Anchor",
        lambda: remove_unified_anchor_handler(ui_components),
        width=18, height=1
    )
    
    return buttons_frame


def create_unified_action_buttons(window: tk.Toplevel, ui_components: UnifiedAnchorUIComponents,
                                 app_instance=None) -> tk.Frame:
    """
    Create save/cancel buttons for the unified anchor window.
    
    Args:
        window: The unified anchor window
        ui_components: UI components manager
        app_instance: The application instance
        
    Returns:
        The frame containing the action buttons
    """
    actions_frame = tk.Frame(window, bg=BG_COLOR)
    actions_frame.pack(fill="x", padx=20, pady=20)
    
    # Cancel button
    create_secondary_button(
        actions_frame,
        "Cancel",
        window.destroy,
        width=12, height=1,
        pack_side=tk.RIGHT
    )
    
    # Save button
    create_success_button(
        actions_frame,
        "Save",
        lambda: save_unified_anchor_configuration(window, ui_components, app_instance),
        width=12, height=1,
        pack_side=tk.RIGHT
    )
    
    return actions_frame


def update_unified_anchor_fields(ui_components: UnifiedAnchorUIComponents, excel_master: Optional[Any] = None,
                                selected_stage: Optional[str] = None):
    """
    Update anchor fields based on selected anchor type with default values from Excel data.
    
    Args:
        ui_components: UI components manager
        excel_master: ExcelMaster object containing anchor data (optional)
        selected_stage: The stage to filter Excel data by (optional)
    """
    # Store excel_master and selected_stage for use in other functions
    ui_components.excel_master = excel_master
    ui_components.selected_stage = selected_stage
    
    anchor_type = ui_components.anchor_type_var.get()
    
    # Clear existing anchor sections
    ui_components.clear_all_anchors()
    
    # Create anchor sections based on anchor type with default anchor names
    if anchor_type == "N":
        # Type N: Only one anchor_n allowed per stage
        section = ui_components.add_anchor_section("N")
        default_name = _get_default_anchor_name(excel_master, anchor_type, "N", selected_stage)
        section.set_anchor_name(default_name)
    
    elif anchor_type == "F":
        # Type F: anchor_f1 (required), anchor_f2 (optional)
        section = ui_components.add_anchor_section("F1")
        default_name = _get_default_anchor_name(excel_master, anchor_type, "F1", selected_stage)
        section.set_anchor_name(default_name)
        # F2 can be added via button
    
    elif anchor_type == "G":
        # Type G: anchor_g1 (required), anchor_g2 (optional)
        section = ui_components.add_anchor_section("G1")
        default_name = _get_default_anchor_name(excel_master, anchor_type, "G1", selected_stage)
        section.set_anchor_name(default_name)
        # G2 can be added via button
    
    # Bind mouse wheel events to newly created sections
    if hasattr(ui_components, 'bind_mouse_wheel_to_new_widgets'):
        for section_name, section in ui_components.anchor_sections.items():
            if hasattr(section, 'frame'):
                ui_components.bind_mouse_wheel_to_new_widgets(section.frame)
    
    # Populate default values from Excel data if available
    if excel_master and selected_stage:
        default_coordinates = get_default_coordinates_from_excel(excel_master, anchor_type, selected_stage)
        
        if default_coordinates:
            populate_unified_anchor_defaults(ui_components, anchor_type, default_coordinates)
            logging.info(f"Populated {len(default_coordinates)} default coordinate sets for anchor type {anchor_type}")
            
            # Show status message to user
            if hasattr(ui_components, 'status_label') and ui_components.status_label:
                info_text = f"✓ Loaded {len(default_coordinates)} coordinate set(s) from Excel data"
                show_temporary_status(ui_components.status_label, info_text, duration=5000)
        else:
            logging.info(f"No default coordinates found for anchor type {anchor_type} in stage {selected_stage}")
            # Clear any previous status messages
            if hasattr(ui_components, 'status_label') and ui_components.status_label:
                ui_components.status_label.config(text="")
    
    # Update anchor management buttons
    ui_components.update_anchor_management_buttons()
    ui_components.current_anchor_type = anchor_type
    
    logging.info(f"Updated unified anchor fields for anchor type {anchor_type}")


def populate_unified_anchor_defaults(ui_components: UnifiedAnchorUIComponents, anchor_type: str,
                                    default_coordinates: List[Dict[str, float]]):
    """
    Populate unified anchor sections with default coordinate values.
    
    Args:
        ui_components: UI components manager
        anchor_type: The anchor type (N, F, G)
        default_coordinates: List of coordinate dictionaries to populate
    """
    try:
        # Determine which anchor sections to populate based on anchor type
        sections_to_populate = []
        
        if anchor_type == "N":
            sections_to_populate = ["N"]
        elif anchor_type == "F":
            sections_to_populate = ["F1"]
            # F2 would be added by user interaction if needed
        elif anchor_type == "G":
            sections_to_populate = ["G1"]
            # G2 would be added by user interaction if needed
        
        # Populate each section with available coordinate sets
        for section_name in sections_to_populate:
            if section_name in ui_components.anchor_sections:
                anchor_section = ui_components.anchor_sections[section_name]
                
                # Clear existing coordinate sets first
                anchor_section.clear_all_sets()
                
                # Add coordinate sets based on default data
                for i, coord_data in enumerate(default_coordinates):
                    # Add a new coordinate set
                    anchor_section.add_coordinate_set()
                    
                    # Get the last added coordinate set
                    if anchor_section.coordinate_sets:
                        coord_set = anchor_section.coordinate_sets[-1]
                        # Set the default values
                        coord_set.set_values(coord_data)
                
                # Update button states
                anchor_section.update_button_states()
                
                logging.info(f"Populated {len(default_coordinates)} coordinate sets for section {section_name}")
    
    except Exception as e:
        logging.error(f"Error populating unified anchor sections with defaults: {e}")


def add_unified_anchor_handler(ui_components: UnifiedAnchorUIComponents):
    """
    Handle adding a new anchor (F2 or G2) in the unified interface.
    
    Args:
        ui_components: UI components manager
    """
    anchor_type = ui_components.anchor_type_var.get()
    
    if anchor_type == "F":
        if "F2" not in ui_components.anchor_sections:
            section = ui_components.add_anchor_section("F2")
            default_name = _get_default_anchor_name(ui_components.excel_master, anchor_type, "F2", ui_components.selected_stage)
            section.set_anchor_name(default_name)
            # Sync F2 to match F1's coordinate set count
            section.sync_with_paired_anchor("add")
            ui_components.update_anchor_management_buttons()
            
            # Bind mouse wheel events to new widgets
            if hasattr(ui_components, 'bind_mouse_wheel_to_new_widgets'):
                ui_components.bind_mouse_wheel_to_new_widgets(section.frame)
            
            logging.info("Added F2 anchor section to unified interface")
        else:
            messagebox.showwarning("Already Exists", "F2 anchor already exists")
    
    elif anchor_type == "G":
        if "G2" not in ui_components.anchor_sections:
            section = ui_components.add_anchor_section("G2")
            default_name = _get_default_anchor_name(ui_components.excel_master, anchor_type, "G2", ui_components.selected_stage)
            section.set_anchor_name(default_name)
            # Sync G2 to match G1's coordinate set count
            section.sync_with_paired_anchor("add")
            ui_components.update_anchor_management_buttons()
            
            # Bind mouse wheel events to new widgets
            if hasattr(ui_components, 'bind_mouse_wheel_to_new_widgets'):
                ui_components.bind_mouse_wheel_to_new_widgets(section.frame)
            
            logging.info("Added G2 anchor section to unified interface")
        else:
            messagebox.showwarning("Already Exists", "G2 anchor already exists")


def remove_unified_anchor_handler(ui_components: UnifiedAnchorUIComponents):
    """
    Handle removing an anchor (F2 or G2) in the unified interface.
    
    Args:
        ui_components: UI components manager
    """
    anchor_type = ui_components.anchor_type_var.get()
    
    if anchor_type == "F":
        if "F2" in ui_components.anchor_sections:
            ui_components.remove_anchor_section("F2")
            ui_components.update_anchor_management_buttons()
            logging.info("Removed F2 anchor section from unified interface")
        else:
            messagebox.showwarning("Not Found", "F2 anchor does not exist")
    
    elif anchor_type == "G":
        if "G2" in ui_components.anchor_sections:
            ui_components.remove_anchor_section("G2")
            ui_components.update_anchor_management_buttons()
            logging.info("Removed G2 anchor section from unified interface")
        else:
            messagebox.showwarning("Not Found", "G2 anchor does not exist")


def validate_unified_anchor_data(ui_components: UnifiedAnchorUIComponents) -> Dict[str, Any]:
    """
    Validate and collect unified anchor data from the UI.
    
    Args:
        ui_components: UI components manager
        
    Returns:
        Dictionary containing validated unified anchor data
        
    Raises:
        ValueError: If validation fails
    """
    anchor_type = ui_components.anchor_type_var.get()
    
    if not ui_components.anchor_sections:
        raise ValueError("No anchor sections defined")
    
    # Validate each anchor section
    unified_data = {}
    geometry_data = {"anchor_type": anchor_type, "anchors": {}}
    parameter_data = {}
    
    for anchor_name, anchor_section in ui_components.anchor_sections.items():
        try:
            anchor_data = anchor_section.get_all_values()
            
            # Validate geometry data
            if not anchor_data.geometry_data or not anchor_data.geometry_data.get("coordinates"):
                raise ValueError(f"No coordinate sets defined for {anchor_name}")
            
            # Validate parameter data
            if not anchor_data.parameter_data:
                raise ValueError(f"No parameter data defined for {anchor_name}")
            
            # Store unified data
            unified_data[anchor_name] = anchor_data
            
            # Store geometry data in expected format
            geometry_data["anchors"][anchor_name] = anchor_data.geometry_data
            
            # Store parameter data in expected format
            parameter_data[anchor_name] = anchor_data.parameter_data
            
        except ValueError as e:
            raise ValueError(f"Validation error in {anchor_name}: {str(e)}")
    
    return {
        "unified_data": unified_data,
        "geometry_data": geometry_data,
        "parameter_data": parameter_data
    }


def save_unified_anchor_configuration(window: tk.Toplevel, ui_components: UnifiedAnchorUIComponents,
                                     app_instance=None):
    """
    Save the unified anchor configuration and close the window.
    
    Args:
        window: The unified anchor window
        ui_components: UI components manager
        app_instance: The application instance
    """
    try:
        # Validate the unified anchor data
        validated_data = validate_unified_anchor_data(ui_components)
        
        # Store the data in the app instance if provided
        if app_instance:
            # Store geometry configuration
            app_instance.geometry_configuration = validated_data["geometry_data"]
            
            # Store parameter configuration
            app_instance.anchors_by_type = parse_anchor_data_from_geometry(validated_data["geometry_data"])
            app_instance.anchor_ui_components = ui_components.anchor_ui_components
            
            # Update comprehensive configuration using the integrated system
            try:
                from app.gen_design.config_details import update_anchor_geometry_config, update_anchor_parameters_config
                stage = getattr(ui_components, 'selected_stage', None)
                
                # Update geometry configuration
                update_anchor_geometry_config(app_instance, validated_data["geometry_data"], stage)
                
                # Update parameter configuration
                update_anchor_parameters_config(app_instance, validated_data["parameter_data"], stage)
                
            except ImportError:
                logging.warning("Could not import configuration update functions")
            except Exception as e:
                logging.error(f"Error updating comprehensive configuration: {str(e)}")
        
        # Log the saved configuration
        anchor_type = validated_data["geometry_data"]["anchor_type"]
        logging.info(f"Unified anchor configuration saved for anchor type {anchor_type}")
        
        # Display success message with detailed summary
        summary_lines = []
        summary_lines.append("=== UNIFIED ANCHOR CONFIGURATION SAVED ===")
        summary_lines.append(f"Anchor Type: {anchor_type}")
        
        if hasattr(ui_components, 'selected_stage') and ui_components.selected_stage:
            summary_lines.append(f"Design Stage: {ui_components.selected_stage}")
        
        summary_lines.append("")
        summary_lines.append("DETAILED CONFIGURATION:")
        summary_lines.append("-" * 50)
        
        total_iterations = 1
        for anchor_name, anchor_data in validated_data["unified_data"].items():
            coordinate_sets = anchor_data.geometry_data.get('coordinates', [])
            actual_anchor_name = anchor_data.name
            set_count = len(coordinate_sets)
            
            summary_lines.append(f"\n📍 {anchor_name.upper()} ANCHOR:")
            summary_lines.append(f"   • Anchor Name: {actual_anchor_name}")
            summary_lines.append(f"   • Number of coordinate sets: {set_count}")
            
            # Show detailed coordinate information
            for i, coord_set in enumerate(coordinate_sets, 1):
                coord_details = []
                for key, value in coord_set.items():
                    if isinstance(value, (int, float)):
                        coord_details.append(f"{key}: {value:.2f}")
                    else:
                        coord_details.append(f"{key}: {value}")
                coord_str = ", ".join(coord_details)
                summary_lines.append(f"   • Coordinate Set {i}: {coord_str}")
            
            # Show detailed parameter information
            param_data = anchor_data.parameter_data
            summary_lines.append(f"   • PARAMETERS:")
            
            if 'sections' in param_data and param_data['sections']:
                summary_lines.append(f"     - Steel Sections:")
                for section in param_data['sections']:
                    section_name = section.get('section', 'Unknown')
                    unit_count = section.get('unit', 'Unknown')
                    summary_lines.append(f"       * {section_name} (Units: {unit_count})")
            
            if 'spacing' in param_data and param_data['spacing']:
                spacing_values = [str(s) for s in param_data['spacing']]
                spacing_str = ", ".join(spacing_values)
                summary_lines.append(f"     - Spacing Values: {spacing_str}")
            
            if 'length' in param_data:
                summary_lines.append(f"     - Length: {param_data['length']}")
            
            if 'angle' in param_data:
                summary_lines.append(f"     - Angle: {param_data['angle']}")
            
            if 'prestress' in param_data:
                summary_lines.append(f"     - Prestress: {param_data['prestress']}")
            
            if 'grout_body' in param_data:
                summary_lines.append(f"     - Grout Body: {param_data['grout_body']}")
            
            # Show any additional custom parameters
            excluded_keys = {'sections', 'spacing', 'length', 'angle', 'prestress', 'grout_body'}
            additional_params = {k: v for k, v in param_data.items() if k not in excluded_keys and v}
            if additional_params:
                summary_lines.append(f"     - Additional Parameters:")
                for key, value in additional_params.items():
                    summary_lines.append(f"       * {key}: {value}")
            
            total_iterations *= set_count
        
        # Calculate and show totals
        summary_lines.append("")
        summary_lines.append("SUMMARY TOTALS:")
        summary_lines.append("-" * 30)
        summary_lines.append(f"• Total anchors configured: {len(validated_data['unified_data'])}")
        summary_lines.append(f"• Total possible design iterations: {total_iterations}")
        
        # Show coordinate set distribution
        coord_distribution = {}
        for anchor_name, anchor_data in validated_data["unified_data"].items():
            coord_count = len(anchor_data.geometry_data.get('coordinates', []))
            coord_distribution[anchor_name] = coord_count
        
        summary_lines.append(f"• Coordinate sets per anchor: {dict(coord_distribution)}")
        
        summary_text = "\n".join(summary_lines)
        
        logging.info(f"Total anchors configured: {len(validated_data['unified_data'])}")
        logging.info(f"Total possible iterations: {total_iterations}")
        
        messagebox.showinfo("Configuration Saved",
                           f"Unified anchor configuration saved successfully!\n\n{summary_text}")
        
        # Close the window
        window.destroy()
        
    except ValueError as e:
        handle_validation_error(str(e), "Validation Error")
    except Exception as e:
        handle_error_with_logging(e, "Error saving unified anchor configuration",
                                 "Failed to save configuration")


def load_anchor_parameters_into_ui(anchor_section, saved_params: Dict[str, Any]):
    """
    Load saved parameter values into the actual UI components for an anchor section.
    
    Args:
        anchor_section: The UnifiedAnchorSection instance
        saved_params: Dictionary containing saved parameter data
    """
    try:
        logging.info(f"Loading parameters into UI for anchor {anchor_section.anchor_name}")
        
        # Load section/unit combinations if available
        if 'sections' in saved_params and 'combo_vars' in anchor_section.parameter_ui_components:
            saved_sections = saved_params['sections']
            combo_vars = anchor_section.parameter_ui_components['combo_vars']
            combo_entries = anchor_section.parameter_ui_components['combo_entries']
            combo_frame = anchor_section.parameter_ui_components['combo_frame']
            logging.info(f"Loading {len(saved_sections)} section/unit combinations")
            
            # Clear excess combinations to match saved count exactly
            target_count = len(saved_sections)
            logging.info(f"Clearing combinations to exactly {target_count} items")
            
            # Remove excess combinations (keep minimum of target_count)
            while len(combo_vars) > target_count:
                # Remove the last combo
                last_combo_vars = combo_vars.pop()
                logging.info(f"Removing excess combo, now have {len(combo_vars)} combos")
                
                # Remove the last two entries (section and unit dropdowns)
                for _ in range(2):
                    if combo_entries:
                        last_entry = combo_entries.pop()
                        last_entry.destroy()
            
            # Add combinations if we need more (should rarely happen but just in case)
            while len(combo_vars) < target_count:
                # Load steel sections for this anchor type 
                from .anchor_param_def import load_steel_sections
                section_options = load_steel_sections(anchor_section.anchor_type)
                from .common import UNIT_OPTIONS
                
                row_num = len(combo_vars) + 1
                logging.info(f"Adding combo at row {row_num}, now will have {len(combo_vars)+1} combos")
                
                # Create new combo boxes using the same logic as add_combo_to_component
                section_var = tk.StringVar(value=section_options[0] if section_options else "")
                section_dropdown = ttk.Combobox(combo_frame, textvariable=section_var,
                                               values=section_options, width=COMBO_WIDTH, state="readonly")
                section_dropdown.grid(row=row_num, column=0, padx=5, pady=2)
                
                unit_var = tk.StringVar(value="1")
                unit_dropdown = ttk.Combobox(combo_frame, textvariable=unit_var,
                                            values=UNIT_OPTIONS, width=5, state="readonly")
                unit_dropdown.grid(row=row_num, column=1, padx=5, pady=2)
                
                # Update component data
                combo_vars.append({'section': section_var, 'unit': unit_var})
                combo_entries.extend([section_dropdown, unit_dropdown])
            
            # Verify we have exactly the right count
            logging.info(f"Final combo count: {len(combo_vars)}, target was: {target_count}")
            
            # Set the saved values
            for i, section_data in enumerate(saved_sections):
                if i < len(combo_vars):
                    combo_dict = combo_vars[i]
                    combo_dict['section'].set(section_data.get('section', ''))
                    combo_dict['unit'].set(section_data.get('unit', ''))
                    logging.info(f"Set combo {i+1}: section={section_data.get('section')}, unit={section_data.get('unit')}")
        
        # Load spacing values if available
        if 'spacing' in saved_params and 'spacing_vars' in anchor_section.parameter_ui_components:
            saved_spacing = saved_params['spacing']
            spacing_vars = anchor_section.parameter_ui_components['spacing_vars']
            logging.info(f"Loading {len(saved_spacing)} spacing values")
            
            # Ensure we have the right number of spacing entries
            while len(spacing_vars) < len(saved_spacing):
                from .anchor_param_def import add_entry_to_component
                from .anchor_param_class import UIComponentData
                spacing_component = UIComponentData(
                    anchor_section.parameter_ui_components['spacing_vars'],
                    anchor_section.parameter_ui_components['spacing_entries'],
                    anchor_section.parameter_ui_components['spacing_frame']
                )
                add_entry_to_component(spacing_component)
                spacing_vars = anchor_section.parameter_ui_components['spacing_vars']
            
            # Set the saved values
            for i, spacing_value in enumerate(saved_spacing):
                if i < len(spacing_vars):
                    spacing_vars[i].set(str(spacing_value))
                    logging.info(f"Set spacing {i+1}: {spacing_value}")
        
        # Load prestress values if available
        if 'prestress' in saved_params and 'prestress_vars' in anchor_section.parameter_ui_components:
            saved_prestress = saved_params['prestress']
            prestress_vars = anchor_section.parameter_ui_components['prestress_vars']
            logging.info(f"Loading {len(saved_prestress)} prestress values")
            
            # Ensure we have the right number of prestress entries
            while len(prestress_vars) < len(saved_prestress):
                from .anchor_param_def import add_entry_to_component
                from .anchor_param_class import UIComponentData
                prestress_component = UIComponentData(
                    anchor_section.parameter_ui_components['prestress_vars'],
                    anchor_section.parameter_ui_components['prestress_entries'],
                    anchor_section.parameter_ui_components['prestress_frame']
                )
                add_entry_to_component(prestress_component)
                prestress_vars = anchor_section.parameter_ui_components['prestress_vars']
            
            # Set the saved values
            for i, prestress_value in enumerate(saved_prestress):
                if i < len(prestress_vars):
                    prestress_vars[i].set(str(prestress_value))
                    logging.info(f"Set prestress {i+1}: {prestress_value}")
        
        # Load settlement criteria if available
        if 'settlement' in saved_params and 'settlement_vars' in anchor_section.parameter_ui_components:
            saved_settlement = saved_params['settlement']
            settlement_vars = anchor_section.parameter_ui_components['settlement_vars']
            logging.info(f"Loading {len(saved_settlement)} settlement criteria")
            
            # Ensure we have the right number of settlement rows
            while len(settlement_vars) < len(saved_settlement):
                anchor_section.add_settlement_row()
                settlement_vars = anchor_section.parameter_ui_components['settlement_vars']
            
            # Set the saved values
            for i, settlement_data in enumerate(saved_settlement):
                if i < len(settlement_vars):
                    settlement_row_vars = settlement_vars[i]
                    if isinstance(settlement_row_vars, dict):
                        # settlement_vars are stored as dictionaries with column names as keys
                        settlement_row_vars.get('Line_x1', tk.StringVar()).set(settlement_data.get('Line_x1', ''))
                        settlement_row_vars.get('Line_y1', tk.StringVar()).set(settlement_data.get('Line_y1', ''))
                        settlement_row_vars.get('Line_x2', tk.StringVar()).set(settlement_data.get('Line_x2', ''))
                        settlement_row_vars.get('Line_y2', tk.StringVar()).set(settlement_data.get('Line_y2', ''))
                        logging.info(f"Set settlement {i+1}: {settlement_data}")
        
        # Load G-type specific parameters: theta_s
        if 'theta_s' in saved_params and 'theta_s_vars' in anchor_section.parameter_ui_components:
            saved_theta_s = saved_params['theta_s']
            theta_s_vars = anchor_section.parameter_ui_components['theta_s_vars']
            logging.info(f"Loading {len(saved_theta_s)} theta_s values")
            
            # Ensure we have the right number of theta_s entries
            while len(theta_s_vars) < len(saved_theta_s):
                from .anchor_param_def import add_entry_to_component
                from .anchor_param_class import UIComponentData
                theta_s_component = UIComponentData(
                    anchor_section.parameter_ui_components['theta_s_vars'],
                    anchor_section.parameter_ui_components['theta_s_entries'],
                    anchor_section.parameter_ui_components['theta_s_frame']
                )
                add_entry_to_component(theta_s_component)
                theta_s_vars = anchor_section.parameter_ui_components['theta_s_vars']
            
            # Set the saved values
            for i, theta_s_value in enumerate(saved_theta_s):
                if i < len(theta_s_vars):
                    theta_s_vars[i].set(str(theta_s_value))
                    logging.info(f"Set theta_s {i+1}: {theta_s_value}")
        
        # Load G-type specific parameters: theta_g
        if 'theta_g' in saved_params and 'theta_g_vars' in anchor_section.parameter_ui_components:
            saved_theta_g = saved_params['theta_g']
            theta_g_vars = anchor_section.parameter_ui_components['theta_g_vars']
            logging.info(f"Loading {len(saved_theta_g)} theta_g values")
            
            # Ensure we have the right number of theta_g entries
            while len(theta_g_vars) < len(saved_theta_g):
                from .anchor_param_def import add_entry_to_component
                from .anchor_param_class import UIComponentData
                theta_g_component = UIComponentData(
                    anchor_section.parameter_ui_components['theta_g_vars'],
                    anchor_section.parameter_ui_components['theta_g_entries'],
                    anchor_section.parameter_ui_components['theta_g_frame']
                )
                add_entry_to_component(theta_g_component)
                theta_g_vars = anchor_section.parameter_ui_components['theta_g_vars']
            
            # Set the saved values
            for i, theta_g_value in enumerate(saved_theta_g):
                if i < len(theta_g_vars):
                    theta_g_vars[i].set(str(theta_g_value))
                    logging.info(f"Set theta_g {i+1}: {theta_g_value}")
        
        # Load G-type specific parameters: anchor_length
        if 'anchor_length' in saved_params and 'anchor_length_vars' in anchor_section.parameter_ui_components:
            saved_anchor_length = saved_params['anchor_length']
            anchor_length_vars = anchor_section.parameter_ui_components['anchor_length_vars']
            logging.info(f"Loading {len(saved_anchor_length)} anchor_length values")
            
            # Ensure we have the right number of anchor_length entries
            while len(anchor_length_vars) < len(saved_anchor_length):
                from .anchor_param_def import add_entry_to_component
                from .anchor_param_class import UIComponentData
                anchor_length_component = UIComponentData(
                    anchor_section.parameter_ui_components['anchor_length_vars'],
                    anchor_section.parameter_ui_components['anchor_length_entries'],
                    anchor_section.parameter_ui_components['anchor_length_frame']
                )
                add_entry_to_component(anchor_length_component)
                anchor_length_vars = anchor_section.parameter_ui_components['anchor_length_vars']
            
            # Set the saved values
            for i, anchor_length_value in enumerate(saved_anchor_length):
                if i < len(anchor_length_vars):
                    anchor_length_vars[i].set(str(anchor_length_value))
                    logging.info(f"Set anchor_length {i+1}: {anchor_length_value}")
        
        logging.info(f"Successfully loaded all parameters for anchor {anchor_section.anchor_name}")
        
    except Exception as e:
        logging.error(f"Error loading parameters into UI for anchor {anchor_section.anchor_name}: {str(e)}")
        import traceback
        logging.error(f"Full traceback: {traceback.format_exc()}")


def load_saved_unified_anchor_data(ui_components: UnifiedAnchorUIComponents, app_instance,
                                   selected_stage: Optional[str] = None) -> bool:
    """
    Load previously saved unified anchor configuration data into UI components.
    
    This function checks for existing anchor geometry and parameter configurations
    in the app instance and populates the UI components with the saved values.
    
    Args:
        ui_components: UI components manager
        app_instance: The application instance containing saved configurations
        selected_stage: The current design stage to match against saved data
        
    Returns:
        bool: True if data was successfully loaded, False otherwise
    """
    logging.info(f"=== DEBUG: Starting load_saved_unified_anchor_data for stage {selected_stage} ===")
    try:
        if not app_instance:
            logging.info("DEBUG: No app instance provided")
            return False
            
        if not hasattr(app_instance, 'comprehensive_config'):
            logging.info("DEBUG: No comprehensive configuration found in app instance, initializing...")
            # Initialize comprehensive config if it doesn't exist
            try:
                from .common import initialize_comprehensive_config
                app_instance.comprehensive_config = initialize_comprehensive_config()
            except ImportError:
                logging.error("Could not import initialize_comprehensive_config")
                return False
        
        config = app_instance.comprehensive_config
        logging.info(f"DEBUG: comprehensive_config keys: {list(config.keys())}")
        
        # Check for saved anchor geometry configuration
        geometry_config = config.get('anchor_geometry', {})
        parameter_config = config.get('anchor_parameters', {})
        
        logging.info(f"DEBUG: geometry_config: {geometry_config}")
        logging.info(f"DEBUG: parameter_config: {parameter_config}")
        
        geometry_configured = geometry_config.get('configured', False)
        parameters_configured = parameter_config.get('configured', False)
        
        if not geometry_configured and not parameters_configured:
            logging.info("DEBUG: No saved anchor configuration found to load")
            return False
        
        # Validate stage matching if stage is specified
        geometry_stage = geometry_config.get('stage')
        parameter_stage = parameter_config.get('stage')
        
        if selected_stage:
            if (geometry_configured and geometry_stage != selected_stage) or \
               (parameters_configured and parameter_stage != selected_stage):
                logging.info(f"DEBUG: Saved configuration stage mismatch. Selected: {selected_stage}, "
                           f"Geometry: {geometry_stage}, Parameters: {parameter_stage}")
                return False
        
        data_loaded = False
        
        # Load geometry data if available
        if geometry_configured and 'anchors' in geometry_config:
            try:
                anchors_data = geometry_config['anchors']
                anchor_type = geometry_config.get('anchor_type', 'N')
                
                logging.info(f"Loading saved geometry data for anchor type {anchor_type} with {len(anchors_data)} anchors")
                
                # Set the anchor type in the UI
                if hasattr(ui_components, 'anchor_type_var') and ui_components.anchor_type_var:
                    ui_components.anchor_type_var.set(anchor_type)
                
                # Populate anchor sections with saved geometry data
                for anchor_name, anchor_data in anchors_data.items():
                    if 'coordinates' in anchor_data:
                        logging.info(f"Loading coordinates for anchor {anchor_name}")
                        
                        # Add anchor section if it doesn't exist
                        if anchor_name not in ui_components.anchor_sections:
                            ui_components.add_anchor_section(anchor_name)
                        
                        anchor_section = ui_components.anchor_sections[anchor_name]
                        
                        # Set anchor name
                        if hasattr(anchor_section, 'set_anchor_name'):
                            actual_anchor_name = anchor_data.get('anchor_name', anchor_name)
                            anchor_section.set_anchor_name(actual_anchor_name)
                            logging.info(f"Set anchor name to: {actual_anchor_name}")
                        
                        # Clear existing coordinate sets first
                        if hasattr(anchor_section, 'clear_all_sets'):
                            anchor_section.clear_all_sets()
                        
                        # Load coordinate sets
                        coordinates = anchor_data['coordinates']
                        logging.info(f"Loading {len(coordinates)} coordinate sets for {anchor_name}")
                        
                        for i, coord_set in enumerate(coordinates):
                            # Add coordinate set
                            if i == 0:
                                # First coordinate set - add one if none exist
                                if len(anchor_section.coordinate_sets) == 0:
                                    anchor_section._add_coordinate_set_internal()
                            else:
                                # Additional coordinate sets
                                anchor_section.add_coordinate_set()
                            
                            # Set coordinate values
                            if anchor_section.coordinate_sets:
                                latest_set = anchor_section.coordinate_sets[-1]
                                if hasattr(latest_set, 'set_values'):
                                    latest_set.set_values(coord_set)
                                    logging.info(f"Set coordinate values for set {i+1}: {coord_set}")
                
                logging.info(f"Successfully loaded geometry data for {len(anchors_data)} anchors")
                data_loaded = True
                
            except Exception as e:
                logging.error(f"Error loading geometry data: {str(e)}")
                import traceback
                logging.error(f"Full traceback: {traceback.format_exc()}")
        
        # Load parameter data if available
        if parameters_configured and 'parameters' in parameter_config:
            try:
                parameters_data = parameter_config['parameters']
                
                # Load parameter data into actual UI components
                if hasattr(ui_components, 'anchor_ui_components'):
                    ui_components.anchor_ui_components = parameters_data
                
                # Populate the actual UI widgets with saved parameter values
                for anchor_name, anchor_section in ui_components.anchor_sections.items():
                    if anchor_name in parameters_data:
                        saved_params = parameters_data[anchor_name]
                        logging.info(f"Loading saved parameters for anchor {anchor_name}: {list(saved_params.keys())}")
                        load_anchor_parameters_into_ui(anchor_section, saved_params)
                
                logging.info(f"Successfully loaded parameter data into UI for {len(parameters_data)} anchors")
                data_loaded = True
                
            except Exception as e:
                logging.error(f"Error loading parameter data: {str(e)}")
                import traceback
                logging.error(f"Full traceback: {traceback.format_exc()}")
        
        if data_loaded:
            logging.info(f"Successfully loaded saved unified anchor configuration for stage {selected_stage}")
            
            # Bind mouse wheel events to newly loaded sections
            if hasattr(ui_components, 'bind_mouse_wheel_to_new_widgets'):
                for section_name, section in ui_components.anchor_sections.items():
                    if hasattr(section, 'frame') and section.frame:
                        ui_components.bind_mouse_wheel_to_new_widgets(section.frame)
                        logging.info(f"Bound mouse wheel events to loaded anchor section {section_name}")
            
            # Update status if available
            if hasattr(ui_components, 'status_label') and ui_components.status_label:
                ui_components.status_label.configure(
                    text="✓ Previously saved configuration loaded successfully",
                    fg="#4CAF50"
                )
        
        logging.info(f"=== DEBUG: Returning data_loaded = {data_loaded} ===")
        return data_loaded
        
    except Exception as e:
        logging.error(f"ERROR loading saved unified anchor data: {str(e)}")
        import traceback
        logging.error(f"Full traceback: {traceback.format_exc()}")
        return False

def edit_unified_anchor(self, anchor_table: ttk.Treeview, progress_window: tk.Widget,
                       selected_stage: Optional[str] = None) -> None:
    """
    Open a unified window to edit both geometry and parameters for anchor types.
    
    This function creates a modal window for configuring both anchor geometry
    coordinates and parameters for different anchor types (N, F, G) with their
    specific requirements and constraints in a single interface.
    
    Args:
        self: The application instance
        anchor_table: The anchor table widget containing anchor data
        progress_window: The parent window
        selected_stage: The stage value selected in the parent window
        
    Returns:
        None
        
    Raises:
        Exception: If there are errors in UI creation or data processing
    """
    try:
        # Create unified anchor window
        unified_window = create_unified_anchor_window(progress_window, selected_stage)
        
        # Initialize unified UI components manager
        ui_components = UnifiedAnchorUIComponents()
        
        # Create UI sections
        create_anchor_type_selection_section(unified_window, ui_components, selected_stage)
        create_unified_content_area(unified_window, ui_components)
        create_anchor_management_buttons(unified_window, ui_components)
        create_unified_action_buttons(unified_window, ui_components, self)
        
        # Set up anchor type change handler
        def on_anchor_type_change(*args):
            update_unified_anchor_fields(ui_components, self.excel_master, selected_stage)
        
        ui_components.anchor_type_var.trace("w", on_anchor_type_change)
        
        # Try to load previously saved configuration first
        saved_data_loaded = load_saved_unified_anchor_data(ui_components, self, selected_stage)
        
        if not saved_data_loaded:
            # If no saved data found, initialize with default anchor type and populate with Excel data
            update_unified_anchor_fields(ui_components, self.excel_master, selected_stage)
        else:
            # If saved data was loaded, just update the anchor management buttons and set up the UI
            # without calling update_unified_anchor_fields which would overwrite the loaded data
            ui_components._saved_data_loaded = True
            ui_components.excel_master = self.excel_master
            ui_components.selected_stage = selected_stage
            ui_components.update_anchor_management_buttons()
            if hasattr(ui_components, 'anchor_type_var'):
                ui_components.current_anchor_type = ui_components.anchor_type_var.get()
            
            logging.info("Loaded data preserved, skipping Excel default population")
        
        logging.info("Unified anchor configuration window created successfully")
        
    except Exception as e:
        handle_error_with_logging(e, "Error creating unified anchor window",
                                 "Failed to create unified anchor window")


# Legacy compatibility functions for existing code
def edit_anchor_geometry(self, anchor_table: ttk.Treeview, progress_window: tk.Widget,
                        selected_stage: Optional[str] = None) -> None:
    """
    Legacy compatibility function that redirects to unified anchor editing.
    
    This function maintains backward compatibility while directing users to the
    unified anchor editing interface.
    """
    edit_unified_anchor(self, anchor_table, progress_window, selected_stage)


def edit_anchor_parameters(self, anchor_table: ttk.Treeview, progress_window: tk.Widget,
                          selected_stage: Optional[str] = None) -> None:
    """
    Legacy compatibility function that redirects to unified anchor editing.
    
    This function maintains backward compatibility while directing users to the
    unified anchor editing interface.
    """
    edit_unified_anchor(self, anchor_table, progress_window, selected_stage)