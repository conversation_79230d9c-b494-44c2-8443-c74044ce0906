import math


def cal_plate_prop(pile_section, df_steel_section, num_strut_layer, spacing, is_grout):
    # separate the pile_section from 1 string to 2 string, it contains '+' as separator
    i_grout, a_grout = 0, 0
    ei_grout, ea_grout = 0, 0
    e_s = 205000  # kN/m2
    e_g = 22200  # kN/m2
    v = 0.2

    if '+' in pile_section:
        pile_section_0 = pile_section.split('+')[0]
        pile_section_1 = pile_section.split('+')[1]
        material_type = 'E'
        condition = df_steel_section['Steel Section'] == pile_section_0
        row = df_steel_section.loc[condition]
        mass_0 = row['M (kg/m)'].values[0]
        uw_0 = mass_0 * 9.81 / 1000 / (spacing / 1000)
        i_0 = row['Ix (cm4)'].values[0]
        a_0 = row['A (cm2)'].values[0]
        if 'FSP' in pile_section_0:
            if num_strut_layer > 1:
                beta_d = 0.75
            elif num_strut_layer == 1:
                beta_d = 0.6
            else:
                beta_d = 0.5
        else:
            beta_d = 1
        # EA (kN/m)
        ea_0 = (e_s * 10 ** 6) * a_0 * 10 ** (-4) * (1000 / spacing) / 1000
        # EI (kNm2/m)
        ei_0 = e_s * 10 ** 6 * i_0 / 100 ** 4 / 1000 * beta_d * (1000 / spacing)

        condition = df_steel_section['Steel Section'] == pile_section_1
        row = df_steel_section.loc[condition]
        mass_1 = row['M (kg/m)'].values[0]
        uw_1 = mass_1 * 9.81 / 1000 / (spacing / 1000)
        i_1 = row['Ix (cm4)'].values[0]
        a_1 = row['A (cm2)'].values[0]
        # EA (kN/m)
        ea_1 = (e_s * 10 ** 6) * a_1 * 10 ** (-4) * (1000 / spacing) / 1000
        # EI (kNm2/m)
        ei_1 = e_s * 10 ** 6 * i_1 / 100 ** 4 / 1000 * beta_d * (1000 / spacing)

        # calculate grout case
        if is_grout:
            if 'CHS' in pile_section_0:
                condition = df_steel_section['Steel Section'] == pile_section_0
                row = df_steel_section.loc[condition]
                d = row['D (mm)'].values[0]
                t = row['t (mm)'].values[0]

                i_pp = row['Ix (cm4)'].values[0]
                a_pp = row['A (cm2)'].values[0]

                condition = df_steel_section['Steel Section'] == pile_section_1
                row = df_steel_section.loc[condition]
                i_h = row['Ix (cm4)'].values[0]
                a_h = row['A (cm2)'].values[0]
            else:
                condition = df_steel_section['Steel Section'] == pile_section_1
                row = df_steel_section.loc[condition]
                d = row['D (mm)'].values[0]
                t = row['t (mm)'].values[0]

                i_pp = row['Ix (cm4)'].values[0]
                a_pp = row['A (cm2)'].values[0]

                condition = df_steel_section['Steel Section'] == pile_section_0
                row = df_steel_section.loc[condition]
                i_h = row['Ix (cm4)'].values[0]
                a_h = row['A (cm2)'].values[0]

            i_grout = d ** 4 * math.pi / 64 / 10000 - i_pp - i_h  # cm4
            a_grout = d ** 2 * math.pi / 4 / 100 - a_pp - a_h  # cm2

            ei_grout = e_g * 1000 * i_grout / 100000000 * (1000 / spacing)  # kN/m2
            ea_grout = e_g * 1000 * a_grout / 10000 * (1000 / spacing)  # kNm2/m

        mass = mass_0 + mass_1
        uw = uw_0 + uw_1
        i = i_0 + i_1 + i_grout
        a = a_0 + a_1 + a_grout
        ea = ea_0 + ea_1 + ea_grout
        ei = ei_0 + ei_1 + ei_grout
    else:
        material_type = 'E'

        condition = df_steel_section['Steel Section'] == pile_section
        row = df_steel_section.loc[condition]
        mass = row['M (kg/m)'].values[0]
        uw = mass * 9.81 / 1000 / (spacing / 1000)
        i = row['Ix (cm4)'].values[0]
        a = row['A (cm2)'].values[0]
        if 'FSP' in pile_section:
            if num_strut_layer > 1:
                beta_d = 0.75
            elif num_strut_layer == 1:
                beta_d = 0.6
            else:
                beta_d = 0.5
        else:
            beta_d = 1

        # calculate grout case
        if is_grout:
            if 'CHS' in pile_section:
                condition = df_steel_section['Steel Section'] == pile_section
                row = df_steel_section.loc[condition]
                d = row['D (mm)'].values[0]
                t = row['t (mm)'].values[0]

                i_pp = row['Ix (cm4)'].values[0]
                a_pp = row['A (cm2)'].values[0]

                i_grout = d ** 4 * math.pi / 64 / 10000 - i_pp  # cm4
                a_grout = d ** 2 * math.pi / 4 / 100 - a_pp  # cm2

                ei_grout = e_g * 1000 * i_grout / 100000000 * (1000 / spacing)  # kN/m2
                ea_grout = e_g * 1000 * a_grout / 10000 * (1000 / spacing)  # kNm2/m

        # EA (kN/m)
        ea_0 = (e_s * 10 ** 6) * a * 10 ** (-4) * (1000 / spacing) / 1000
        # EI (kNm2/m)
        ei_0 = e_s * 10 ** 6 * i / 100 ** 4 / 1000 * beta_d * (1000 / spacing)

        ea = ea_0 + ea_grout
        ei = ei_0 + ei_grout

    return pile_section, material_type, mass, uw, e_s, i, a, num_strut_layer, beta_d, spacing, ea, ei, v


def get_soil_polygon_points(soil_polygon):
    """
    Extracts unique points from the segments of a soil polygon.

    Args:
        soil_polygon: The soil polygon object containing geometric segment data.

    Returns:
        list: A list of unique (x, y) coordinate tuples representing the points of the soil polygon.
    """
    # Extract the starting points (x1, y1) of each segment in the soil polygon
    points = [(segment.x1.value, segment.y1.value) for segment in soil_polygon.Geometry.Segments]

    # Add the ending points (x2, y2) of each segment in the soil polygon
    points += [(segment.x2.value, segment.y2.value) for segment in soil_polygon.Geometry.Segments]

    # Remove duplicate points while preserving order and return the result
    return list(dict.fromkeys(points))


def check_point_above_polyline(polyline_df, point_x, point_y):
    """
    Determines if a given point (point_x, point_y) is above a polyline defined by a DataFrame.

    Args:
        polyline_df (pd.DataFrame): DataFrame containing the polyline coordinates with columns 'X (m)' and 'Y (m)'.
        point_x (float): The x-coordinate of the point to check.
        point_y (float): The y-coordinate of the point to check.

    Returns:
        bool: True if the point is above the polyline, False otherwise.
    """
    # Sort the polyline DataFrame by x-coordinate
    sorted_polyline = polyline_df.sort_values(by='X (m)')

    # Iterate through consecutive polyline segments
    for i in range(len(sorted_polyline) - 1):
        x1, y1 = sorted_polyline.iloc[i][['X (m)', 'Y (m)']]
        x2, y2 = sorted_polyline.iloc[i + 1][['X (m)', 'Y (m)']]

        # Check if the point lies between the segment's x-coordinates
        if min(x1, x2) <= point_x <= max(x1, x2):
            # Handle vertical line segments (avoid division by zero)
            if abs(x2 - x1) < 1e-10:  # x2 is almost equal to x1 (vertical line)
                # For vertical segments, if point_x equals the line's x-coordinate,
                # check if point_y is above the minimum y of the segment
                if point_y >= min(y1, y2):
                    return True
            else:
                # For non-vertical segments, interpolate y-coordinate
                interpolated_y = round(y1 + (point_x - x1) * (y2 - y1) / (x2 - x1), 2)

                # Return True if the point is above the interpolated y-coordinate
                if point_y >= interpolated_y:
                    return True
    return False
