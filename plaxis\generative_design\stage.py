from plaxis.generative_design.utils import interpolate_y
from plaxis.geometry.get import get_NodeToNodeAnchor, get_fixedEndAnchor, get_EmbeddedBeam
from plaxis.builder.utils import get_input_phase_by_number
from plaxis.geometry.material import add_anchor_prop, add_eb_prop


def add_autorun_stage(g_i, phase_m, anchors, df_ss, df_autorunlog, df_ground_profile):
    """
    Add autorun stages for anchor installation and excavation.
    
    This function processes multiple anchor configurations from df_autorunlog,
    creating corresponding phases and anchor installations for each configuration.
    
    Args:
        g_i: PLAXIS server instance
        phase_m: Main phase object
        anchors: List of anchor objects to process
        df_ss: DataFrame containing steel section properties
        df_autorunlog: DataFrame containing autorun configurations
        df_ground_profile: DataFrame containing ground profile data
        
    Returns:
        tuple: (updated g_i, updated df_autorunlog with phase numbers)
    """
    # Find target excavation phase
    phase_t = next(phase for phase in g_i.Phases[1:] if phase.PreviousPhase.Number.value == phase_m.Number.value)
    # Get the previous phase
    phase_ex = get_input_phase_by_number(g_i, phase_m.Number.value - 1)

    # Process each autorun configuration
    for index, row in df_autorunlog.iterrows():
        _process_autorun_configuration(
            g_i, phase_m, phase_t, phase_ex, row, anchors, df_ss, df_ground_profile, df_autorunlog, index
        )

    return g_i, df_autorunlog

def _process_autorun_configuration(g_i, phase_m, phase_t, phase_ex, row, anchors, df_ss, df_ground_profile, df_autorunlog, index):
    """
    Process a single autorun configuration row.
    
    This function handles the complete processing of one configuration from df_autorunlog,
    including phase creation and processing all anchors for this configuration.
    
    Args:
        g_i: PLAXIS server instance
        phase_m: Main phase object
        phase_t: Target excavation phase
        phase_ex: Previous excavation phase
        row: DataFrame row containing the configuration
        anchors: List of anchor objects to process
        df_ss: DataFrame containing steel section properties
        df_ground_profile: DataFrame containing ground profile data
        df_autorunlog: DataFrame containing autorun configurations
        index: Index of the current row being processed
    """
    # Create phases for this autorun configuration
    phase_s, phase_ss = _create_phases_for_autorun(
        g_i, phase_m, phase_t, phase_ex, row['Identification']
    )

    # Process each anchor in this configuration
    for i, anchor in enumerate(anchors):
        _process_single_anchor_in_configuration(
            g_i, anchor, i, phase_s, phase_ss, row, df_ss, df_ground_profile
        )

    # Log excavation details
    df_autorunlog.loc[index, 'Phase Number'] = phase_ss.Number.value


def _process_single_anchor_in_configuration(g_i, anchor, anchor_index, phase_s, phase_ss, row, df_ss, df_ground_profile):
    """
    Process a single anchor within an autorun configuration.
    
    This function handles the complete processing of one anchor, including
    parameter extraction, material calculations, and type-specific processing.
    
    Args:
        g_i: PLAXIS server instance
        anchor: Anchor object to process
        anchor_index: Index of the anchor (0-based)
        phase_s: Spacing phase
        phase_ss: Excavation phase
        row: DataFrame row containing the configuration
        df_ss: DataFrame containing steel section properties
        df_ground_profile: DataFrame containing ground profile data
    """
    # Extract anchor parameters from the configuration row
    anchor_params = _extract_anchor_parameters(row, anchor_index, df_ss)
    
    # Calculate basic material properties
    anchormat, area, e = _calculate_anchor_material_properties(
        g_i, anchor, anchor_params, df_ss
    )

    # Process anchor based on its type using dispatch pattern
    processor = _get_anchor_processor(anchor.type)
    processor(g_i, anchor, phase_s, phase_ss, anchormat, anchor_params, df_ss, df_ground_profile)


def _get_anchor_processor(anchor_type: str):
    """
    Get the appropriate processor function for the given anchor type.
    
    This function implements a dispatch pattern for anchor type processing,
    making it easy to add new anchor types without modifying existing code.
    
    Args:
        anchor_type: Type of anchor ('N', 'F', 'G', etc.)
        
    Returns:
        function: Appropriate processor function for the anchor type
        
    Raises:
        ValueError: If anchor type is not supported
    """
    anchor_processors = {
        'N': _process_prestressed_anchor,
        'F': _process_prestressed_anchor,
        'G': _process_grouted_anchor
    }
    
    if anchor_type not in anchor_processors:
        raise ValueError(f"Unsupported anchor type: {anchor_type}")
    
    return anchor_processors[anchor_type]





def _create_phases_for_autorun(g_i, phase_m, phase_t, phase_ex, identification: str):
    """
    Create spacing and excavation phases for an autorun configuration.
    
    Args:
        g_i: PLAXIS server instance
        phase_m: Main phase object
        phase_t: Target excavation phase
        phase_ex: Previous excavation phase
        identification: Identification string for this configuration
        
    Returns:
        tuple: (spacing_phase, excavation_phase)
    """
    # Create and configure phase for spacing
    phase_s = g_i.phase(phase_m)
    g_i.setcurrentphase(phase_s)
    phase_s.setproperties("Identification", f"{phase_m.Identification}_{identification}")
    phase_s.Deform.ResetDisplacementsToZero.set(False)
    phase_s.setproperties("PreviousPhase", phase_ex)

    # Create and configure phase for excavation
    phase_ss = g_i.phase(phase_t)
    g_i.setcurrentphase(phase_ss)
    phase_ss.setproperties("PreviousPhase", phase_s)
    phase_ss.setproperties("Identification", f"{phase_t.Identification}_{identification}")
    phase_ss.Deform.ResetDisplacementsToZero.set(False)
    
    return phase_s, phase_ss


def _extract_anchor_parameters(row, anchor_index: int, df_ss):
    """
    Extract anchor parameters from a DataFrame row for a specific anchor.
    
    Args:
        row: DataFrame row containing anchor configuration
        anchor_index: Index of the anchor (0-based)
        df_ss: DataFrame containing steel section properties
        
    Returns:
        dict: Dictionary containing extracted anchor parameters
    """
    anchor_prefix = f"Anchor{anchor_index + 1}_"
    
    params = {
        'name': row[f"{anchor_prefix}Name"],
        'section': row[f"{anchor_prefix}Section"],
        'unit': row[f"{anchor_prefix}Unit"],
        'spacing': row[f"{anchor_prefix}Spacing (m)"]
    }
    
    # Add prestress for N and F type anchors
    if f"{anchor_prefix}Prestress (kN/m)" in row:
        params['prestress'] = row[f"{anchor_prefix}Prestress (kN/m)"]
    
    # Add grouted anchor specific parameters
    if f"{anchor_prefix}Theta_s (deg)" in row:
        params.update({
            'theta_s': row[f"{anchor_prefix}Theta_s (deg)"],
            'theta_g': row[f"{anchor_prefix}Theta_g (deg)"],
            'length': row[f"{anchor_prefix}Length (m)"]
        })
    
    return params


def _calculate_anchor_material_properties(g_i, anchor, anchor_params: dict, df_ss):
    """
    Calculate anchor material properties and create anchor material.
    
    Args:
        g_i: PLAXIS server instance
        anchor: Anchor object containing geometric properties
        anchor_params: Dictionary containing anchor parameters
        df_ss: DataFrame containing steel section properties
        
    Returns:
        tuple: (anchor_material, area, young_modulus)
    """
    section = anchor_params['section']
    unit = anchor_params['unit']
    spacing = anchor_params['spacing']
    
    area = df_ss.loc[df_ss['Section'] == section, 'A (cm2)'].values[0] * unit
    e = df_ss.loc[df_ss['Section'] == section, 'E (MPa)'].values[0]
    
    alpha, theta = float(anchor.alpha), float(anchor.theta)
    anchor_name = f"({anchor.name})_{unit}x{section}_S{spacing}m"
    
    anchormat = add_anchor_prop(g_i, anchor_name, alpha, spacing, area, e)
    
    return anchormat, area, e


def _process_prestressed_anchor(g_i, anchor, phase_s, phase_ss, anchormat, anchor_params: dict, df_ss, df_ground_profile):
    """
    Process prestressed anchors (Node-to-Node or Fixed-End).
    
    Args:
        g_i: PLAXIS server instance
        anchor: Anchor object
        phase_s: Spacing phase
        phase_ss: Excavation phase
        anchormat: Anchor material
        anchor_params: Dictionary containing anchor parameters
        df_ss: DataFrame containing steel section properties (unused for prestressed anchors)
        df_ground_profile: DataFrame containing ground profile data (unused for prestressed anchors)
    """
    prestress = anchor_params['prestress']
    
    g_i.setcurrentphase(phase_s)
    
    if anchor.type == 'N':
        anchor_element = get_NodeToNodeAnchor(
            g_i, anchor.x1, anchor.y1, anchor.x2, anchor.y2,
            tab_current='Staged construction'
        )
    else:  # anchor.type == 'F'
        anchor_element = get_fixedEndAnchor(
            g_i, anchor.x1, anchor.y1, tab_current='Staged construction'
        )
    
    # Configure anchor in spacing phase
    anchor_element.Material.set(phase_s, anchormat)
    anchor_element.AdjustPrestress.set(phase_s, True)
    anchor_element.PrestressForce.set(phase_s, -prestress)
    phase_s.ShouldCalculate = True

    # Configure anchor in excavation phase
    g_i.setcurrentphase(phase_ss)
    anchor_element.Material.set(phase_ss, anchormat)
    phase_ss.ShouldCalculate = True


def _calculate_grouted_anchor_stress(anchor, df_ground_profile, theta_s: float, theta_g: float, length: float):
    """
    Calculate average stress for grouted anchor sections.
    
    Args:
        anchor: Anchor object containing geometric properties
        df_ground_profile: DataFrame containing ground profile data
        theta_s: Theta_s angle in degrees
        theta_g: Theta_g angle in degrees  
        length: Anchor length in meters
        
    Returns:
        float: Average stress value in kN/m
    """
    # Find the index of length in anchor.list_length
    index_length = anchor.list_length.index(length)
    
    list_anchor_g_pts = next(
        (item[2] for item in anchor.list_anchor_g_pts if item[0] == theta_s and item[1] == theta_g), 
        None
    )
    
    x2 = list_anchor_g_pts[0][0][0]
    y2 = list_anchor_g_pts[0][0][1]
    x3 = list_anchor_g_pts[index_length][1][0]
    y3 = list_anchor_g_pts[index_length][1][1]
    
    x_values = df_ground_profile.loc['X (m)']
    y_values = df_ground_profile.loc['Top']
    
    h2 = interpolate_y(x2, x_values, y_values) - y2
    h3 = interpolate_y(x3, x_values, y_values) - y3
    
    stress2 = (19 - 9.81) * h2
    stress3 = (19 - 9.81) * h3
    average_stress = round((stress2 + stress3) / 2, 2)
    
    return average_stress


def _process_grouted_anchor(g_i, anchor, phase_s, phase_ss, anchormat, anchor_params: dict, df_ss, df_ground_profile):
    """
    Process grouted anchors with embedded beam elements.
    
    Args:
        g_i: PLAXIS server instance
        anchor: Anchor object
        phase_s: Spacing phase
        phase_ss: Excavation phase
        anchormat: Anchor material
        anchor_params: Dictionary containing anchor parameters
        df_ss: DataFrame containing steel section properties
        df_ground_profile: DataFrame containing ground profile data
    """
    theta_s = anchor_params['theta_s']
    theta_g = anchor_params['theta_g']
    length = anchor_params['length']
    section = anchor_params['section']
    unit = anchor_params['unit']
    spacing = anchor_params['spacing']
    
    # Find the index of length in anchor.list_length
    index_length = anchor.list_length.index(length)
    
    # Get anchor point lists for this configuration
    list_anchor_n_pts = next(
        (item[2] for item in anchor.list_anchor_n_pts if item[0] == theta_s and item[1] == theta_g), 
        None
    )
    list_anchor_g_pts = next(
        (item[2] for item in anchor.list_anchor_g_pts if item[0] == theta_s and item[1] == theta_g), 
        None
    )
    
    # Calculate material properties for embedded beam
    unit_weight = df_ss.loc[df_ss['Section'] == section, 'Unit Weights (kN/m3)'].values[0]
    diameter = df_ss.loc[df_ss['Section'] == section, 'D (mm)'].values[0] / 1000
    e = df_ss.loc[df_ss['Section'] == section, 'E (MPa)'].values[0]
    
    # Calculate average stress
    average_stress = _calculate_grouted_anchor_stress(
        anchor, df_ground_profile, theta_s, theta_g, length
    )
    
    # Create embedded beam material
    eb_name = f"({anchor.name})_{unit}x{section}_S{spacing}m_Stress{average_stress}kN/m"
    ebmat = add_eb_prop(g_i, eb_name, spacing, e, unit_weight, diameter, average_stress, average_stress)
    
    # Create and activate node-to-node anchors
    for anchor_n_pts in list_anchor_n_pts:
        anchor_n = get_NodeToNodeAnchor(
            g_i, anchor_n_pts[0][0], anchor_n_pts[0][1],
            anchor_n_pts[1][0], anchor_n_pts[1][1],
            tab_current='Staged construction'
        )
        
        g_i.activate(anchor_n, phase_s)
        anchor_n.Material.set(phase_s, anchormat)
        
        g_i.setcurrentphase(phase_ss)
        g_i.activate(anchor_n, phase_ss)
        anchor_n.Material.set(phase_ss, anchormat)
    
    # Create and activate embedded beams up to specified length
    for anchor_g_pts in list_anchor_g_pts[:index_length + 1]:
        if anchor_g_pts[0] != anchor_g_pts[1]:
            anchor_g = get_EmbeddedBeam(
                g_i, anchor_g_pts[0][0], anchor_g_pts[0][1],
                anchor_g_pts[1][0], anchor_g_pts[1][1],
                tab_current='Staged construction'
            )
            
            g_i.activate(anchor_g, phase_s)
            anchor_g.Material.set(phase_s, ebmat)
            
            g_i.setcurrentphase(phase_ss)
            g_i.activate(anchor_g, phase_ss)
            anchor_g.Material.set(phase_ss, ebmat)
    
    phase_s.ShouldCalculate = True
    phase_ss.ShouldCalculate = True
