import logging
import os
from datetime import datetime

import plaxis.builder
from plaxis.builder.run import run_plaxis
from plaxis.generative_design.combination import create_anchor_combinations_log
from plaxis.generative_design.stage import add_autorun_stage
from plaxis.generative_design.utils import define_anchors, cal_anchor_g_end_pts, \
    ensure_directory_exists
from plaxis.geometry.get import get_line, get_point
from plaxis.builder.utils import get_input_phase_by_number
from plaxis.result.read import extract_fea_result

logger = logging.getLogger(__name__)


def run_generative_design(self):
    """
    Main orchestrator function for generative design workflow.
    
    This function coordinates the entire generative design process using
    a modular, function-based approach with strategy pattern for extensibility.
    
    Args:
        self: The main application instance containing file paths, data, and configuration
    """
    # Create workflow configuration
    config = _create_workflow_config(self)
    
    # Execute the modular workflow
    _execute_generative_design_workflow(self, config)


def _create_workflow_config(app_instance):
    """
    Create configuration dictionary for the generative design workflow.
    
    Args:
        app_instance: The main application instance
        
    Returns:
        dict: Configuration dictionary containing workflow parameters
    """
    return {
        'enable_diagnostics': True,
        'create_autorun_folder': True,
        'validate_anchor_types': True,
        'save_intermediate_results': True,
        'log_level': 'INFO'
    }


def _execute_generative_design_workflow(app_instance, config):
    """
    Execute the complete generative design workflow using modular functions.
    
    Args:
        app_instance: The main application instance
        config: Workflow configuration dictionary
    """
    # Step 1: Validate and prepare inputs
    workflow_data = _validate_and_prepare_inputs(app_instance, config)
    
    # Step 2: Setup autorun environment
    _setup_autorun_environment(app_instance, workflow_data, config)
    
    # Step 3: Process anchor configuration
    anchor_config = _process_anchor_configuration(app_instance, workflow_data, config)
    
    # Step 4: Build PLAXIS model
    plaxis_model = _build_plaxis_model(app_instance, anchor_config, config)
    
    # Step 5: Execute anchor strategy based on type
    _execute_anchor_strategy(app_instance, plaxis_model, anchor_config, config)
    
    # Step 6: Run design workflow
    _run_design_workflow(app_instance, plaxis_model, anchor_config, config)
    
    # Step 7: Finalize and save results
    _finalize_and_save_results(app_instance, plaxis_model, anchor_config, config)


def _validate_and_prepare_inputs(app_instance, config):
    """
    Validate inputs and prepare data for the generative design workflow.
    
    Args:
        app_instance: The main application instance
        config: Workflow configuration dictionary
        
    Returns:
        dict: Validated and prepared workflow data
        
    Raises:
        ValueError: If input validation fails
    """
    
    # Copy anchor location data from the master Excel file
    df_anchor_loc = app_instance.excel_master.AnchorLoc.copy()
    df_ground_profile = app_instance.excel_master.GroundProfile.copy()
    
    # Copy steel section data from Excel inputs
    df_ss = app_instance.excel_inputs.ELSSteelSection.copy()
    
    # Get the directory of the Excel file
    directory = os.path.dirname(app_instance.file_paths.ExcelPlaxis)
    logger.info(f"Excel file location: {app_instance.file_paths.ExcelPlaxis}")
    logger.info(f"Directory for AutoRun folder: {directory}")
    
    # Calculate y_fel (final excavation level)
    df_fel = app_instance.excel_master.EL.loc[app_instance.excel_master.EL['Stage'] == 'Final']
    if df_fel.empty:
        logger.error("No excavation data found in FEL for the final stage.")
        raise ValueError("No excavation data found in FEL for the final stage.")
    y_fel = df_fel['Y (m)'].min()
    
    return {
        'df_anchor_loc': df_anchor_loc,
        'df_ground_profile': df_ground_profile,
        'df_ss': df_ss,
        'directory': directory,
        'y_fel': y_fel
    }


def _setup_autorun_environment(app_instance, workflow_data, config):
    """
    Setup the autorun environment including folder creation and path configuration.
    
    Args:
        app_instance: The main application instance
        workflow_data: Validated workflow data
        config: Workflow configuration dictionary
    """
    if not config.get('create_autorun_folder', True):
        return
        
    directory = workflow_data['directory']
    
    # Create a new folder for autorun results if it doesn't already exist
    folder_autorun = os.path.join(directory, 'Plaxis_AutoRun')
    logger.info(f"AutoRun folder path: {folder_autorun}")
    
    try:
        if not os.path.exists(folder_autorun):
            os.makedirs(folder_autorun)
            logger.info(f"Successfully created AutoRun folder: {folder_autorun}")
        else:
            logger.info(f"AutoRun folder already exists: {folder_autorun}")
    except OSError as e:
        logger.error(f"Failed to create AutoRun folder {folder_autorun}: {e}")
        raise
    
    # Verify the folder was created and log its absolute path
    abs_folder_path = os.path.abspath(folder_autorun)
    logger.info(f"AutoRun folder absolute path: {abs_folder_path}")
    logger.info(f"AutoRun folder exists: {os.path.exists(abs_folder_path)}")
    
    # Store folder path in workflow data for later use
    workflow_data['folder_autorun'] = folder_autorun
    workflow_data['abs_folder_path'] = abs_folder_path


def _process_anchor_configuration(app_instance, workflow_data, config):
    """
    Process and validate anchor configuration data.
    
    Args:
        app_instance: The main application instance
        workflow_data: Validated workflow data
        config: Workflow configuration dictionary
        
    Returns:
        dict: Processed anchor configuration
        
    Raises:
        ValueError: If anchor configuration is invalid
    """
    # Define anchors and cross sections
    anchors, cross_sections = define_anchors(app_instance.autorun_params, workflow_data['df_anchor_loc'])
    
    # Set up autorun log file path
    app_instance.file_paths.AutoRunLog = f"{workflow_data['folder_autorun']}/Plaxis_AutoRun_{str(anchors[0].layer)}.xlsx"
    logger.info(f"AutoRun log file will be saved as: {app_instance.file_paths.AutoRunLog}")
    logger.info('AutoRunning PLAXIS...')
    
    # Validate anchor types
    anchor_type = _validate_anchor_types(anchors, config)
    
    # Validate anchor stages
    anchor_stage = _validate_anchor_stages(anchors, config)
    
    # Create anchor combinations log
    df_autorunlog = create_anchor_combinations_log(anchors, anchor_type)
    
    return {
        'anchors': anchors,
        'cross_sections': cross_sections,
        'anchor_type': anchor_type,
        'anchor_stage': anchor_stage,
        'df_autorunlog': df_autorunlog
    }


def _validate_anchor_types(anchors, config):
    """
    Validate that all anchors have the same type.
    
    Args:
        anchors: List of anchor objects
        config: Workflow configuration dictionary
        
    Returns:
        str: Validated anchor type ('N', 'F', or 'G')
        
    Raises:
        ValueError: If anchor types are mixed or invalid
    """
    if not config.get('validate_anchor_types', True):
        return anchors[0].type if anchors else 'N'
    
    anchor_types = [anchor.type for anchor in anchors]
    if all(anchor_type == 'N' for anchor_type in anchor_types):
        anchor_type = 'N'
    elif all(anchor_type == 'F' for anchor_type in anchor_types):
        anchor_type = 'F'
    elif all(anchor_type == 'G' for anchor_type in anchor_types):
        anchor_type = 'G'
    else:
        logger.error("Mixed anchor types detected. Only one type of anchor is allowed.")
        raise ValueError("Mixed anchor types detected. Only one type of anchor is allowed.")
    
    return anchor_type


def _validate_anchor_stages(anchors, config):
    """
    Validate that all anchors have the same stage.
    
    Args:
        anchors: List of anchor objects
        config: Workflow configuration dictionary
        
    Returns:
        int: Validated anchor stage
        
    Raises:
        ValueError: If anchor stages are not consistent
    """
    # Create anchor_stage if all values in anchors.stage are the same
    if len(set(anchor.stage for anchor in anchors)) == 1:
        anchor_stage = int(anchors[0].stage)
    else:
        logger.error("Anchor stages are not the same. Please check the input data.")
        raise ValueError("Anchor stages are not the same. Please check the input data.")
    
    return anchor_stage


def _build_plaxis_model(app_instance, anchor_config, config):
    """
    Build the PLAXIS model using the processed configuration.
    
    Args:
        app_instance: The main application instance
        anchor_config: Processed anchor configuration
        config: Workflow configuration dictionary
        
    Returns:
        PLAXIS model instance
    """
    # Build the PLAXIS model
    g_i = plaxis.builder.build.build_plaxis(
        app_instance.excel_master, 
        app_instance.plaxis_password, 
        app_instance.PORT_i, 
        app_instance.PORT_o,
        end_stage=anchor_config['anchor_stage']
    )
    
    return g_i


def _execute_anchor_strategy(app_instance, plaxis_model, anchor_config, config):
    """
    Execute the appropriate anchor strategy based on anchor type.
    
    Args:
        app_instance: The main application instance
        plaxis_model: PLAXIS model instance
        anchor_config: Processed anchor configuration
        config: Workflow configuration dictionary
    """
    anchor_type = anchor_config['anchor_type']
    
    # Get strategy function based on anchor type
    strategy_registry = _get_anchor_strategy_registry()
    strategy_func = strategy_registry.get(anchor_type)
    
    if strategy_func is None:
        logger.warning(f"No specific strategy found for anchor type '{anchor_type}'. Using default processing.")
        return
    
    # Execute the appropriate strategy
    strategy_func(app_instance, plaxis_model, anchor_config, config)


def _get_anchor_strategy_registry():
    """
    Get the registry of anchor processing strategies.
    
    Returns:
        dict: Registry mapping anchor types to strategy functions
    """
    # Built-in strategies
    built_in_strategies = {
        'N': _process_normal_anchors,
        'F': _process_fixed_anchors,
        'G': _process_grouted_anchors
    }
    
    # Add custom strategies if any are registered
    custom_strategies = globals().get('_CUSTOM_ANCHOR_STRATEGIES', {})
    
    # Merge strategies (custom strategies can override built-in ones)
    all_strategies = built_in_strategies.copy()
    all_strategies.update(custom_strategies)
    
    return all_strategies


def _process_normal_anchors(app_instance, plaxis_model, anchor_config, config):
    """
    Process normal anchor type ('N') using default behavior.
    
    Args:
        app_instance: The main application instance
        plaxis_model: PLAXIS model instance
        anchor_config: Processed anchor configuration
        config: Workflow configuration dictionary
    """
    # Normal anchors use default processing - no special geometry handling needed
    logger.info("Processing normal anchors with default behavior")


def _process_fixed_anchors(app_instance, plaxis_model, anchor_config, config):
    """
    Process fixed anchor type ('F') using default behavior.
    
    Args:
        app_instance: The main application instance
        plaxis_model: PLAXIS model instance
        anchor_config: Processed anchor configuration
        config: Workflow configuration dictionary
    """
    # Fixed anchors use default processing - no special geometry handling needed
    logger.info("Processing fixed anchors with default behavior")


def _process_grouted_anchors(app_instance, plaxis_model, anchor_config, config):
    """
    Process grouted anchor type ('G') with complex geometry calculations.
    
    Args:
        app_instance: The main application instance
        plaxis_model: PLAXIS model instance
        anchor_config: Processed anchor configuration
        config: Workflow configuration dictionary
    """
    logger.info("Processing grouted anchors with complex geometry calculations")
    
    g_i = plaxis_model
    anchors = anchor_config['anchors']
    
    # Get workflow data from app_instance for y_fel
    df_fel = app_instance.excel_master.EL.loc[app_instance.excel_master.EL['Stage'] == 'Final']
    y_fel = df_fel['Y (m)'].min()
    
    # Delete existing anchor elements
    _delete_existing_anchor_elements(g_i, anchors)
    
    # Generate anchor geometry combinations
    _generate_anchor_geometry_combinations(g_i, anchors, y_fel)


def _delete_existing_anchor_elements(g_i, anchors):
    """
    Delete existing anchor elements from the PLAXIS model.
    
    Args:
        g_i: PLAXIS model instance
        anchors: List of anchor objects
    """
    for anchor in anchors:
        # Delete anchor elements
        anchor_n = get_line(g_i, anchor.x1, anchor.y1, anchor.x2, anchor.y2, 
                           tab_search='Structures', tab_current='Structures')
        anchor_g = get_line(g_i, anchor.x2, anchor.y2, anchor.x3, anchor.y3, 
                           tab_search='Structures', tab_current='Structures')
        
        g_i.delete(anchor_g)
        g_i.delete(anchor_n)
        
        # Delete associated points
        for x, y in [(anchor.x2, anchor.y2), (anchor.x2, anchor.y2), (anchor.x3, anchor.y3)]:
            point = get_point(g_i, x, y, tab_search='Structures', tab_current='Structures')
            g_i.delete(point)


def _generate_anchor_geometry_combinations(g_i, anchors, y_fel):
    """
    Generate all anchor geometry combinations for grouted anchors.
    
    Args:
        g_i: PLAXIS model instance
        anchors: List of anchor objects
        y_fel: Final excavation level
    """
    for i, anchor in enumerate(anchors):
        anchor.list_anchor_n_pts = []
        anchor.list_anchor_g_pts = []
        x1, y1 = anchor.x1, anchor.y1
        side = 'left' if anchor.x2 < x1 else 'right'
        
        for theta_s in anchor.list_theta_s:
            for theta_g in anchor.list_theta_g:
                list_anchor_n_pts = []
                list_anchor_g_pts = []
                g_i.gotostages()
                g_i.gotostructures()
                
                for i, length in enumerate(anchor.list_length):
                    (x2, y2), (x3, y3) = cal_anchor_g_end_pts(x1, y1, y_fel, theta_s, theta_g, length, side=side)
                    
                    if i == 0:
                        # Create first anchor segment
                        line = g_i.line((x1, y1), (x2, y2))[-1]
                        anchor_n = g_i.n2nanchor(line)
                        list_anchor_n_pts.append(((x1, y1), (x2, y2)))
                        
                        # Create grouted section
                        pt_start, pt_end = (x2, y2), (x3, y3)
                        list_anchor_g_pts.append(((x2, y2), (x3, y3)))
                        if pt_start != pt_end:
                            line = g_i.line(pt_start, pt_end)[-1]
                            anchor_g = g_i.embeddedbeam(line)
                    else:
                        # Create subsequent grouted sections
                        pt_start, pt_end = (x_current, y_current), (x3, y3)
                        list_anchor_g_pts.append(((x_current, y_current), (x3, y3)))
                        if pt_start != pt_end:
                            line = g_i.line(pt_start, pt_end)[-1]
                            anchor_g = g_i.embeddedbeam(line)
                    
                    x_current, y_current = x3, y3
                
                # Store geometry data in anchor
                anchor.list_anchor_n_pts.append((theta_s, theta_g, list_anchor_n_pts))
                anchor.list_anchor_g_pts.append((theta_s, theta_g, list_anchor_g_pts))


def _run_design_workflow(app_instance, plaxis_model, anchor_config, config):
    """
    Run the main design workflow including stage addition and PLAXIS execution.
    
    Args:
        app_instance: The main application instance
        plaxis_model: PLAXIS model instance
        anchor_config: Processed anchor configuration
        config: Workflow configuration dictionary
    """
    g_i = plaxis_model
    
    g_i.gotostages()
    
    # Determine the phase number for the current layer
    phase_num = (anchor_config['anchor_stage'] - 1) * 2 + 4 - 1
    phase_m = get_input_phase_by_number(g_i, phase_num)
    
    # Get workflow data
    df_ground_profile = app_instance.excel_master.GroundProfile.copy()
    
    # Add autorun stage
    g_i, app_instance.excel_master.AutoRunLog = add_autorun_stage(
        g_i, phase_m, anchor_config['anchors'], 
        app_instance.excel_inputs.ELSSteelSection.copy(), 
        anchor_config['df_autorunlog'], df_ground_profile
    )
    
    # Save intermediate results if configured
    if config.get('save_intermediate_results', True):
        ensure_directory_exists(app_instance.file_paths.AutoRunLog)
        app_instance.excel_master.AutoRunLog.to_excel(app_instance.file_paths.AutoRunLog)
    
    # Run the PLAXIS calculation for all phases
    run_plaxis(g_i)


def _finalize_and_save_results(app_instance, plaxis_model, anchor_config, config):
    """
    Finalize the workflow and save all results.
    
    Args:
        app_instance: The main application instance
        plaxis_model: PLAXIS model instance
        anchor_config: Processed anchor configuration
        config: Workflow configuration dictionary
    """
    # Extract finite element analysis results and update the master data
    app_instance.excel_master = extract_fea_result(
        app_instance.file_paths, 
        app_instance.excel_inputs, 
        app_instance.excel_master, 
        app_instance.plaxis_password,
        anchor_config['cross_sections']
    )
    
    # Save final autorun log
    ensure_directory_exists(app_instance.file_paths.AutoRunLog)
    app_instance.excel_master.AutoRunLog.to_excel(app_instance.file_paths.AutoRunLog)
    
    logger.info("Generative design workflow completed successfully")


# =============================================================================
# EXTENSION AND CONFIGURATION UTILITIES
# =============================================================================

def register_anchor_strategy(anchor_type, strategy_func):
    """
    Register a new anchor processing strategy.
    
    This function allows extending the system with new anchor types
    without modifying existing code.
    
    Args:
        anchor_type (str): The anchor type identifier (e.g., 'X', 'Y')
        strategy_func (callable): Function that processes this anchor type
        
    Example:
        def process_custom_anchors(app_instance, plaxis_model, anchor_config, config):
            # Custom processing logic
            pass
            
        register_anchor_strategy('X', process_custom_anchors)
    """
    global _CUSTOM_ANCHOR_STRATEGIES
    if '_CUSTOM_ANCHOR_STRATEGIES' not in globals():
        globals()['_CUSTOM_ANCHOR_STRATEGIES'] = {}
    _CUSTOM_ANCHOR_STRATEGIES[anchor_type] = strategy_func
    logger.info(f"Registered custom anchor strategy for type '{anchor_type}'")


def get_available_anchor_strategies():
    """
    Get a list of all available anchor strategies.
    
    Returns:
        dict: Dictionary mapping anchor types to strategy function names
    """
    built_in_strategies = _get_anchor_strategy_registry()
    custom_strategies = globals().get('_CUSTOM_ANCHOR_STRATEGIES', {})
    
    all_strategies = {}
    for anchor_type, func in built_in_strategies.items():
        all_strategies[anchor_type] = func.__name__
    for anchor_type, func in custom_strategies.items():
        all_strategies[anchor_type] = func.__name__
        
    return all_strategies


def create_workflow_config_template():
    """
    Create a template configuration dictionary with all available options.
    
    Returns:
        dict: Template configuration with default values and documentation
    """
    return {
        'enable_diagnostics': True,           # Run path diagnostics
        'create_autorun_folder': True,        # Create autorun folder
        'validate_anchor_types': True,        # Validate anchor type consistency
        'save_intermediate_results': True,    # Save intermediate files
        'log_level': 'INFO',                  # Logging level
        'custom_strategies': {},              # Custom strategy configuration
        'validation_rules': {                 # Custom validation rules
            'allow_mixed_anchor_types': False,
            'require_consistent_stages': True,
            'min_anchor_count': 1
        },
        'output_options': {                   # Output configuration
            'save_geometry_data': True,
            'export_summary_report': False,
            'create_backup': True
        }
    }


def validate_workflow_config(config):
    """
    Validate the workflow configuration dictionary.
    
    Args:
        config (dict): Configuration dictionary to validate
        
    Returns:
        bool: True if configuration is valid
        
    Raises:
        ValueError: If configuration is invalid
    """
    required_keys = ['enable_diagnostics', 'create_autorun_folder', 'validate_anchor_types']
    for key in required_keys:
        if key not in config:
            raise ValueError(f"Missing required configuration key: {key}")
    
    valid_log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
    if config.get('log_level') not in valid_log_levels:
        raise ValueError(f"Invalid log level: {config.get('log_level')}. Must be one of {valid_log_levels}")
    
    return True


def execute_generative_design_with_config(app_instance, config=None):
    """
    Execute generative design with custom configuration.
    
    This function provides an alternative entry point that allows
    full customization of the workflow configuration.
    
    Args:
        app_instance: The main application instance
        config (dict, optional): Custom workflow configuration
        
    Returns:
        dict: Workflow execution results and metadata
    """
    if config is None:
        config = _create_workflow_config(app_instance)
    
    # Validate configuration
    validate_workflow_config(config)
    
    # Execute workflow
    start_time = datetime.now()
    logger.info("Starting generative design workflow with custom configuration")
    
    try:
        _execute_generative_design_workflow(app_instance, config)
        
        result = {
            'status': 'success',
            'message': 'Generative design workflow completed successfully',
            'config_used': config,
            'autorun_log_path': getattr(app_instance.file_paths, 'AutoRunLog', None)
        }
        
        logger.info("Generative design workflow completed successfully")
        return result
        
    except Exception as e:
        logger.error(f"Generative design workflow failed: {str(e)}")
        result = {
            'status': 'error',
            'message': f'Workflow failed: {str(e)}',
            'config_used': config,
            'error_details': str(e)
        }
        raise


def get_workflow_step_functions():
    """
    Get a dictionary of all workflow step functions for advanced customization.
    
    Returns:
        dict: Mapping of step names to function objects
    """
    return {
        'validate_and_prepare_inputs': _validate_and_prepare_inputs,
        'setup_autorun_environment': _setup_autorun_environment,
        'process_anchor_configuration': _process_anchor_configuration,
        'build_plaxis_model': _build_plaxis_model,
        'execute_anchor_strategy': _execute_anchor_strategy,
        'run_design_workflow': _run_design_workflow,
        'finalize_and_save_results': _finalize_and_save_results
    }


def create_custom_workflow(step_overrides=None):
    """
    Create a custom workflow by overriding specific steps.
    
    Args:
        step_overrides (dict): Dictionary mapping step names to custom functions
        
    Returns:
        callable: Custom workflow function
        
    Example:
        def custom_validation(app_instance, config):
            # Custom validation logic
            return _validate_and_prepare_inputs(app_instance, config)
            
        custom_workflow = create_custom_workflow({
            'validate_and_prepare_inputs': custom_validation
        })
        custom_workflow(app_instance, config)
    """
    if step_overrides is None:
        step_overrides = {}
    
    def custom_workflow_func(app_instance, config):
        """Custom workflow with overridden steps."""
        # Get default steps
        steps = get_workflow_step_functions()
        
        # Override with custom steps
        steps.update(step_overrides)
        
        # Execute workflow with custom steps
        workflow_data = steps['validate_and_prepare_inputs'](app_instance, config)
        steps['setup_autorun_environment'](app_instance, workflow_data, config)
        anchor_config = steps['process_anchor_configuration'](app_instance, workflow_data, config)
        plaxis_model = steps['build_plaxis_model'](app_instance, anchor_config, config)
        steps['execute_anchor_strategy'](app_instance, plaxis_model, anchor_config, config)
        steps['run_design_workflow'](app_instance, plaxis_model, anchor_config, config)
        steps['finalize_and_save_results'](app_instance, plaxis_model, anchor_config, config)
    
    return custom_workflow_func
