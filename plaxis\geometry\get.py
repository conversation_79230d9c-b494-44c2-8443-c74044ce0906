def go_back_tab(g_i, tab_current='Structures'):
    """
    Navigate back to the specified tab in PLAXIS.
    
    Args:
        g_i: PLAXIS input object
        tab_current (str): Tab to navigate to ('Structures', 'Soil', 'Mesh', 'Flow conditions', 'Staged construction')
    """
    if tab_current == 'Soil':
        g_i.gotosoil()
    elif tab_current == 'Structures':
        g_i.gotostructures()
    elif tab_current == 'Mesh':
        g_i.gotomesh()
    elif tab_current == 'Flow conditions':
        g_i.gotoflow()
    elif tab_current == 'Staged construction':
        g_i.gotostages()


def get_line(g_i, x1, y1, x2, y2, tab_search='Structures', tab_current='Structures'):
    """
    Finds and returns a PLAXIS line object by its coordinates.

    Parameters:
        g_i: PLAXIS input object
        x1, y1, x2, y2 (float): Coordinates of the two points defining the line.
        tab_search (str): Tab to search in ('Structures' or other).

    Returns:
        Line object matching the given coordinates.

    Raises:
        ValueError: If no line with the specified coordinates is found.
    """
    if tab_search == 'Structures':
        g_i.gotostructures()
        for line in g_i.Lines:
            condition1 = (line.First.x.value == x1 and line.First.y.value == y1 and
                          line.Second.x.value == x2 and line.Second.y.value == y2)
            condition2 = (line.First.x.value == x2 and line.First.y.value == y2 and
                          line.Second.x.value == x1 and line.Second.y.value == y1)
            if condition1 | condition2:
                go_back_tab(g_i, tab_current)
                return line

    else:
        g_i.gotostages()
        for line in g_i.Lines:
            condition = (line.Parent.BoundingBox.xMin.value == min(x1, x2) and
                         line.Parent.BoundingBox.yMin.value == min(y1, y2) and
                         line.Parent.BoundingBox.xMax.value == max(x1, x2) and
                         line.Parent.BoundingBox.yMax.value == max(y1, y2))
            if condition:
                go_back_tab(g_i, tab_current)
                return line

    raise ValueError(f"Line with coordinates ({x1}, {y1}) to ({x2}, {y2}) not found")


def get_point(g_i, x, y, tab_search='Structures', tab_current='Structures'):
    """
    Finds and returns a PLAXIS point object by its coordinates.

    Parameters:
        g_i: PLAXIS input object.
        x (float): X-coordinate of the point to search for.
        y (float): Y-coordinate of the point to search for.
        tab_search (str): Tab to search in ('Structures' or other). Defaults to 'Structures'.

    Returns:
        Point object: The PLAXIS point object matching the given coordinates.

    Raises:
        ValueError: If no point with the specified coordinates is found.
    """
    if tab_search == 'Structures':
        g_i.gotostructures()
        for point in g_i.Points:
            if round(point.x.value, 2) == round(x, 2) and round(point.y.value, 2) == round(y, 2):
                go_back_tab(g_i, tab_current)
                return point
    else:
        g_i.gotostages()
        for point in g_i.Points:
            if round(point.Geometry.x.value, 2) == round(x, 2) and round(point.Geometry.y.value, 2) == round(y, 2):
                go_back_tab(g_i, tab_current)
                return point

    raise ValueError(f"Point with coordinates ({round(x, 2)}, {round(y, 2)}) not found")


def get_NodeToNodeAnchor(g_i, x1, y1, x2, y2, tab_current='Structures'):
    if tab_current != 'Staged construction':
        g_i.gotostages()

    for anchor_n in g_i.NodeToNodeAnchors:
        condition = (round(anchor_n.Parent.BoundingBox.xMin.value, 2) == round(min(x1, x2), 2) and
                     round(anchor_n.Parent.BoundingBox.yMin.value, 2) == round(min(y1, y2), 2) and
                     round(anchor_n.Parent.BoundingBox.xMax.value, 2) == round(max(x1, x2), 2) and
                     round(anchor_n.Parent.BoundingBox.yMax.value, 2) == round(max(y1, y2), 2))

        if condition:
            if tab_current != 'Staged construction':
                go_back_tab(g_i, tab_current)
            return anchor_n

    raise ValueError(
        f"Line with coordinates ({round(x1, 2)}, {round(y1, 2)}) to ({round(x2, 2)}, {round(y2, 2)}) not found")


def get_fixedEndAnchor(g_i, x, y, tab_current='Structures'):
    if tab_current != 'Staged construction':
        g_i.gotostages()

    for anchor_f in g_i.fixedEndAnchors:
        if (round(anchor_f.Parent.Geometry.x.value, 2) == round(x, 2) and
                round(anchor_f.Parent.Geometry.y.value, 2) == round(y, 2)):
            if tab_current != 'Staged construction':
                go_back_tab(g_i, tab_current)
            return anchor_f

    raise ValueError(f"Point with coordinates ({x}, {y}) not found")


def get_EmbeddedBeam(g_i, x1, y1, x2, y2, tab_current='Structures'):
    if tab_current != 'Staged construction':
        g_i.gotostages()

    for embedded_beam in g_i.EmbeddedBeams:
        condition = (round(embedded_beam.Parent.BoundingBox.xMin.value, 2) == round(min(x1, x2), 2) and
                     round(embedded_beam.Parent.BoundingBox.yMin.value, 2) == round(min(y1, y2), 2) and
                     round(embedded_beam.Parent.BoundingBox.xMax.value, 2) == round(max(x1, x2), 2) and
                     round(embedded_beam.Parent.BoundingBox.yMax.value, 2) == round(max(y1, y2), 2))
        if condition:
            if tab_current != 'Staged construction':
                go_back_tab(g_i, tab_current)
            return embedded_beam

    raise ValueError(
        f"Line with coordinates ({round(x1, 2)}, {round(y1, 2)}) to ({round(x2, 2)}, {round(y2, 2)}) not found")


