"""
Login and Authentication Module for PLAXIS Automation Tool

This module handles user authentication, password generation, and session management
for the PLAXIS Automation Tool application.
"""
# Standard library imports
import hashlib
import logging
import os
import random
import re
import secrets
import smtplib
import ssl
import string
import sys
import threading
import time
import urllib.parse
import urllib.request
import warnings
from datetime import datetime
from email.message import EmailMessage
from functools import wraps
from typing import Optional, Dict, Any, Union, Callable

# Third-party library imports
import pandas as pd
import tkinter as tk
from tkinter import messagebox, ttk, filedialog

# Configure logging and warnings
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
warnings.simplefilter(action='ignore', category=UserWarning)
warnings.simplefilter(action='ignore', category=FutureWarning)
pd.options.mode.chained_assignment = None  # default='warn'

# Constants for security settings
MAX_LOGIN_ATTEMPTS = 5
LOGIN_TIMEOUT_MINUTES = 15
SESSION_TOKEN_BYTES = 32
LICENSE_FILE_ID = "1idRdClrn_lRARklDa_UgNJNdJ_bxHfJz"
URL_TIMEOUT = 10  # Timeout for network requests in seconds
EMAIL_SENDER = '<EMAIL>'
EMAIL_PASSWORD = 'nunwcsgerkfetpii'  # Consider using environment variables for production
from version_config import APP_VERSION_V_PREFIX as APP_VERSION

# UI Constants
FONT_TITLE = ("Arial", 16, "bold")
FONT_SUBTITLE = ("Arial", 12)
FONT_NORMAL = ("Arial", 10)
FONT_SMALL = ("Arial", 8, "italic")
BUTTON_WIDTH_LARGE = 20
BUTTON_HEIGHT_LARGE = 2
BUTTON_WIDTH_MEDIUM = 15
BUTTON_HEIGHT_MEDIUM = 2
ENTRY_WIDTH = 40

# ------ Simplified Asynchronous Email System ------

def _send_email_in_background(func: Callable, *args, **kwargs) -> None:
    """
    Execute an email sending function in the background to prevent UI blocking.
    
    Args:
        func: The email function to execute
        *args: Arguments to pass to the function
        **kwargs: Keyword arguments to pass to the function
    """
    def email_wrapper():
        try:
            success = func(*args, **kwargs)
            if success:
                logging.info(f"Background email sent successfully")
            else:
                logging.warning(f"Background email sending failed - likely due to network restrictions")
                logging.info("Email service unavailable: This may be due to firewall/SMTP port blocking")
        except Exception as e:
            logging.error(f"Error in background email sending: {e}")
            logging.info("Email service error: Network connectivity or configuration issue")
    
    # Create and start background thread
    email_thread = threading.Thread(
        target=email_wrapper,
        name="EmailSender",
        daemon=True
    )
    email_thread.start()


def send_email_log_sync(user_name: str, case: str, version_type: str = "Base", 
                        user_email: Optional[str] = None) -> bool:
    """
    Synchronous email sending function with multiple fallback methods.
    
    This function tries multiple SMTP configurations to handle network restrictions.
    
    Args:
        user_name: Username performing the action
        case: Description of the action
        version_type: Software version type
        user_email: Optional email address (<NAME_EMAIL>)

    Returns:
        Boolean indicating success
    """
    email_receiver = EMAIL_SENDER  # Send logs to the same monitoring address
    subject = 'ELS RPA Usage Log'

    # Get current timestamp
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Use provided email or construct from username
    if user_email is None:
        user_email = f"{user_name}@asiainfrasolutions.com"

    body = f"""
    Timestamp: {timestamp}
    Username: {user_name}
    Email: {user_email}
    Version: {APP_VERSION} ({version_type})
    Action: {case}
    """

    # Create email message
    em = EmailMessage()
    em['From'] = EMAIL_SENDER
    em['To'] = email_receiver
    em['Subject'] = subject
    em.set_content(body)

    # Try multiple SMTP methods in order of preference
    methods = [
        ("SMTP SSL (Port 465)", _send_via_ssl_465),
        ("SMTP STARTTLS (Port 587)", _send_via_starttls_587),
    ]
    
    for method_name, send_method in methods:
        try:
            logging.debug(f"Attempting email via {method_name}...")
            send_method(em)
            logging.info(f"Email log sent successfully for user: {user_name}, action: {case} via {method_name}")
            return True
            
        except (smtplib.SMTPAuthenticationError, smtplib.SMTPException) as smtp_error:
            logging.warning(f"{method_name} failed with SMTP error: {smtp_error}")
            continue
            
        except OSError as network_error:
            # Network connectivity issues (timeouts, connection refused, etc.)
            if "10060" in str(network_error) or "timeout" in str(network_error).lower():
                logging.warning(f"{method_name} failed due to network timeout/blocking")
            else:
                logging.warning(f"{method_name} failed with network error: {network_error}")
            continue
            
        except Exception as e:
            logging.warning(f"{method_name} failed with unexpected error: {e}")
            continue
    
    # All methods failed
    logging.error(f"All email sending methods failed for user: {user_name}, action: {case}")
    logging.info("Email sending failed - this may be due to network restrictions (firewall/SMTP blocking)")
    return False


def _send_via_ssl_465(email_message: EmailMessage) -> None:
    """Send email using SSL on port 465 (original method)."""
    context = ssl.create_default_context()
    with smtplib.SMTP_SSL('smtp.gmail.com', 465, context=context, timeout=20) as smtp:
        smtp.login(EMAIL_SENDER, EMAIL_PASSWORD)
        smtp.sendmail(EMAIL_SENDER, EMAIL_SENDER, email_message.as_string())


def _send_via_starttls_587(email_message: EmailMessage) -> None:
    """Send email using STARTTLS on port 587 (alternative method)."""
    with smtplib.SMTP('smtp.gmail.com', 587, timeout=20) as smtp:
        smtp.starttls()
        smtp.login(EMAIL_SENDER, EMAIL_PASSWORD)
        smtp.sendmail(EMAIL_SENDER, EMAIL_SENDER, email_message.as_string())


# ------ Notification Functions (Merged from _Notification.Notification) ------

def generate_password_key(length: int = 30) -> str:
    """
    Generate a secure random password of specified length.

    Args:
        length: Length of password to generate (default: 30)

    Returns:
        String containing the secure password
    """
    # Define character sets for password
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    special_chars = "!@#$%^&*()-_=+"
    
    # Ensure at least one of each type is included
    password = [
        random.choice(lowercase),
        random.choice(uppercase),
        random.choice(digits),
        random.choice(special_chars)
    ]
    
    # Fill the rest randomly
    all_chars = lowercase + uppercase + digits + special_chars
    remaining_length = length - len(password)
    password.extend(random.choice(all_chars) for _ in range(remaining_length))
    
    # Shuffle the password to make it more secure
    random.shuffle(password)
    return ''.join(password)


def send_password_email(user_name: str, password_key: str) -> bool:
    """
    Send login password to user's email.

    Args:
        user_name: Username (email prefix)
        password_key: Generated password to send

    Returns:
        Boolean indicating success
    """
    try:
        email_receiver = f"{user_name}@asiainfrasolutions.com"
        subject = f"ELS RPA {APP_VERSION} Login Password"

        body = f"""
        Dear {user_name},

        Your Login Password for ELS RPA: 
        {password_key}

        This password will expire after your current session.
        
        Regards,
        Alex Sze
        """

        # Create email message
        em = EmailMessage()
        em['From'] = EMAIL_SENDER
        em['To'] = email_receiver
        em['Subject'] = subject
        em.set_content(body)

        # Send email securely
        context = ssl.create_default_context()
        with smtplib.SMTP_SSL('smtp.gmail.com', 465, context=context) as smtp:
            smtp.login(EMAIL_SENDER, EMAIL_PASSWORD)
            smtp.sendmail(EMAIL_SENDER, email_receiver, em.as_string())

        logging.info(f"Password sent successfully to {email_receiver}")
        return True

    except smtplib.SMTPAuthenticationError as auth_error:
        logging.error(f"SMTP authentication error: {auth_error}")
        return False
    except smtplib.SMTPException as smtp_error:
        logging.error(f"SMTP error sending password email: {smtp_error}")
        return False
    except Exception as e:
        logging.error(f"Unexpected error sending password email: {e}")
        return False


def send_email_log(user_name: str, case: str, version_type: str = "Base", 
                  user_email: Optional[str] = None) -> bool:
    """
    Log user actions via email for monitoring (Enhanced Asynchronous version).

    This function sends the email in the background using simple threading to prevent UI blocking.
    Provides better error handling and graceful degradation when email services are unavailable.

    Args:
        user_name: Username performing the action
        case: Description of the action
        version_type: Software version type
        user_email: Optional email address (<NAME_EMAIL>)

    Returns:
        Boolean indicating success (True if queued successfully, even if actual sending might fail)
    """
    try:
        # Send email in background using simple threading
        _send_email_in_background(
            send_email_log_sync, 
            user_name, 
            case, 
            version_type, 
            user_email
        )
        logging.debug(f"Email log queued for background sending: user={user_name}, action={case}")
        return True
        
    except Exception as e:
        logging.error(f"Failed to queue email log: {e}")
        # Don't fallback to synchronous in async function - that would block UI
        # Just log the failure and return False
        logging.warning("Email logging is currently unavailable due to network restrictions")
        return False

# ------ Helper Functions ------

def log_security_event(user_name: str, case: str, user_email: Optional[str] = None) -> bool:
    """
    Log security events with error handling.
    
    Args:
        user_name: Username related to the security event
        case: Description of the security event
        user_email: Optional email address
        
    Returns:
        Boolean indicating success
    """
    try:
        email = user_email or f"{user_name}@asiainfrasolutions.com"
        return send_email_log(
            user_name=user_name,
            case=case,
            version_type="Base",
            user_email=email
        )
    except Exception as e:
        logging.error(f"Failed to log security event: {str(e)}")
        return False


def requires_login(func: Callable) -> Callable:
    """
    Decorator to ensure user is logged in before accessing a function.
    
    Args:
        func: Function to protect with login requirement
        
    Returns:
        Wrapped function that checks login status
    """
    @wraps(func)
    def wrapper(self, *args: Any, **kwargs: Any) -> Any:
        if not getattr(self, 'login_status', False):
            messagebox.showerror("Authentication Required", "You must be logged in to access this feature")
            self.show_login_frame()
            return None
        return func(self, *args, **kwargs)
    return wrapper


# ------ Login UI and Authentication Functions ------

def show_login_frame(self) -> None:
    """
    Display the login screen with username and password fields.
    
    Args:
        self: The application instance
    """
    # Clear any existing frames
    for widget in self.root.winfo_children():
        widget.destroy()

    # Deactivate timer updates
    self.timer_active = False

    # Recreate the menu bar
    self.create_menu_bar()

    # Try to set window icon if available
    try:
        self.root.iconbitmap('AIS.ico')
    except Exception as e:
        logging.debug(f"Icon not loaded: {str(e)}")

    # Create header
    header_frame = tk.Frame(self.root)
    header_frame.pack(fill="x", padx=20, pady=10)

    title_label = tk.Label(header_frame, text="PLAXIS Automation Tool", font=FONT_TITLE)
    title_label.pack(anchor="w")

    separator = ttk.Separator(self.root, orient="horizontal")
    separator.pack(fill="x", padx=20, pady=5)

    # Create login form
    login_frame = tk.Frame(self.root)
    login_frame.pack(expand=True, fill="both", padx=40, pady=20)

    # Username entry
    tk.Label(login_frame, text="User Name (e.g. john.doe):", font=FONT_NORMAL).pack(anchor="w", pady=(10, 0))
    tk.Label(login_frame, text="User Name = <EMAIL>", font=FONT_SMALL).pack(anchor="w")

    self.username_entry = tk.Entry(login_frame, width=ENTRY_WIDTH)
    self.username_entry.pack(pady=(5, 20), fill="x")
    self.username_entry.focus()  # Set focus on username field

    # Get password button
    get_password_btn = tk.Button(login_frame, text="Send Password to Email",
                               command=self.send_password, 
                               width=BUTTON_WIDTH_LARGE, 
                               height=BUTTON_HEIGHT_LARGE)
    get_password_btn.pack(pady=10)

    # Password entry
    tk.Label(login_frame, text="Enter the 30-unit password:", font=FONT_NORMAL).pack(anchor="w", pady=(10, 0))

    self.password_entry = tk.Entry(login_frame, width=ENTRY_WIDTH, show="*")
    self.password_entry.pack(pady=(5, 20), fill="x")

    # Bind Enter key to login
    self.password_entry.bind('<Return>', lambda event: self.login())

    # Login button
    login_btn = tk.Button(login_frame, text="Login", 
                        command=self.login, 
                        width=BUTTON_WIDTH_LARGE, 
                        height=BUTTON_HEIGHT_LARGE)
    login_btn.pack(pady=10)

    # Development login button - should be removed in production
    if _is_development_mode():
        dev_btn = tk.Button(login_frame, text="Development Login (Skip)", 
                          command=self.dev_login, 
                          width=BUTTON_WIDTH_LARGE)
        dev_btn.pack(pady=10)


def _is_development_mode() -> bool:
    """
    Check if the application is running in development mode.
    
    This can be determined by an environment variable or a debug flag.
    
    Returns:
        Boolean indicating if in development mode
    """
    # Always return True for development environment
    # IMPORTANT: Change this back to False before deployment
    return True
    # Original implementation:
    # return os.environ.get('PLAXIS_DEV_MODE', 'False').lower() == 'true'


def check_license(self, user_name: str) -> bool:
    """
    Check if the user is in the approved license list.
    
    Args:
        self: The application instance
        user_name: Username to check against the license list
        
    Returns:
        Boolean indicating if user is licensed
    """
    try:
        # Construct the direct download URL using the constant file ID
        direct_url = f"https://drive.google.com/uc?export=download&id={LICENSE_FILE_ID}"

        # Open the direct download URL and read the content with a timeout
        with urllib.request.urlopen(direct_url, timeout=URL_TIMEOUT) as response:
            # Read the content in bytes
            file_content = response.read()

            # Decode bytes to string (assuming UTF-8 encoding)
            list_user = file_content.decode('utf-8')
            list_user = re.findall(r"'(.*?)'", list_user)

            # Check if user is in the approved list
            return user_name in list_user

    except urllib.error.URLError as e:
        logging.error(f"URL Error checking license: {str(e)}")
        messagebox.showerror("License Check Failed",
                           "Could not connect to the license server. Please check your internet connection.")
        return False
    except Exception as e:
        logging.error(f"Error checking license: {str(e)}")
        messagebox.showerror("License Check Failed",
                           f"Could not verify license eligibility: {str(e)}")
        return False


def send_password(self) -> None:
    """
    Generate and email a secure password to the user.
    
    Args:
        self: The application instance
    """
    # Validate username
    self.user_name = self.username_entry.get().strip()
    if not self.user_name:
        messagebox.showerror("Error", "Please enter your username")
        return

    # Store the full email address
    self.user_email = f"{self.user_name}@asiainfrasolutions.com"

    # First check if the user is in the approved license list
    if not self.check_license(self.user_name):
        messagebox.showerror("Unauthorized",
                           "Sorry, your username is not in the approved license list. Please contact support.")
        # Log the unauthorized attempt
        log_security_event(
            user_name=self.user_name,
            case="Unauthorized Login Attempt",
            user_email=self.user_email
        )
        return

    # Reset login attempts when requesting new password
    self.login_attempts = 0
    
    # Generate password, salt, and compute hash
    try:
        # Generate a secure password and send it
        _generate_and_send_password(self)
    except Exception as e:
        logging.error(f"Password generation error: {str(e)}")
        messagebox.showerror("Error", f"Failed to send password: {str(e)}")


def _generate_and_send_password(self) -> None:
    """
    Generate a secure password, create salt and hash, and send it to the user's email.
    
    Args:
        self: The application instance
    """
    # Generate the password
    plain_password = generate_password_key()
    self.password_salt = secrets.token_hex(16)  # Generate random salt

    # Hash password with salt
    password_with_salt = plain_password + self.password_salt
    self.password_hash = hashlib.sha256(password_with_salt.encode()).hexdigest()

    # Send the plain password via email
    success = send_password_email(self.user_name, plain_password)
    if success:
        log_security_event(
            user_name=self.user_name,
            case="0. UI_Login",
            user_email=self.user_email
        )
        messagebox.showinfo("Success", f"Password sent to {self.user_email}")
    else:
        messagebox.showerror("Error", "Failed to send password email. Please check your internet connection.")


def login(self) -> None:
    """
    Authenticate user with provided password.
    
    Args:
        self: The application instance
    """
    # Get the password input
    input_password = self.password_entry.get().strip()

    # Verify password hash exists
    if not hasattr(self, 'password_hash') or not self.password_hash:
        messagebox.showerror("Error", "Please request a password first")
        return

    # Validate the password
    is_valid = _validate_password(self, input_password)

    if is_valid:
        _complete_successful_login(self)
    else:
        _handle_failed_login(self)


def _validate_password(self, password: str) -> bool:
    """
    Validate the password by comparing its hash with the stored hash.
    
    Args:
        self: The application instance
        password: The password to validate
        
    Returns:
        Boolean indicating if password is valid
    """
    # Hash input password with stored salt
    input_password_with_salt = password + self.password_salt
    input_password_hash = hashlib.sha256(input_password_with_salt.encode()).hexdigest()

    # Compare hashes
    return input_password_hash == self.password_hash


def _complete_successful_login(self) -> None:
    """
    Complete the login process after successful authentication.
    
    Args:
        self: The application instance
    """
    # Reset login attempts counter on successful login
    self.login_attempts = 0
    self.login_status = True
    
    # Generate a session token
    self.session_token = secrets.token_hex(SESSION_TOKEN_BYTES)
    
    # Reset the start time when logging in
    self.start_time = time.time()

    # Securely clear password data from memory
    self.password_hash = ""
    self.password_salt = ""
    self.password_entry.delete(0, 'end')

    # Go directly to main frame without showing PLAXIS password dialog
    messagebox.showinfo("Success", f"Welcome, {self.user_name}")
    self.show_main_frame()


def _handle_failed_login(self) -> None:
    """
    Handle failed login attempts and enforce security policies.
    
    Args:
        self: The application instance
    """
    # Increment login attempts counter on failed login
    self.login_attempts = getattr(self, 'login_attempts', 0) + 1

    # Check if maximum attempts reached
    if self.login_attempts >= MAX_LOGIN_ATTEMPTS:
        # Log the security event
        log_security_event(
            user_name=getattr(self, 'user_name', "Unknown"),
            case="Security Alert: Maximum Login Attempts Exceeded",
            user_email=getattr(self, 'user_email', "<EMAIL>")
        )

        # Show error message
        messagebox.showerror("Security Alert",
                          f"Maximum login attempts ({MAX_LOGIN_ATTEMPTS}) exceeded.\n"
                          "Application will exit for security reasons.")

        # Force exit the application
        self.root.after(200, self.force_security_exit)
    else:
        # Show regular error with attempts remaining
        remaining = MAX_LOGIN_ATTEMPTS - self.login_attempts
        messagebox.showerror("Error", f"Wrong Password! Please try again.\n"
                                    f"Attempts remaining: {remaining}")


def force_security_exit(self) -> None:
    """
    Force exit the application for security reasons.
    
    Args:
        self: The application instance
    """
    logging.warning(f"Security exit triggered after {MAX_LOGIN_ATTEMPTS} failed login attempts")
    try:
        self.root.destroy()
        os._exit(1)  # Use exit code 1 for abnormal exit
    except Exception as e:
        logging.error(f"Error during security exit: {e}")
        sys.exit(1)  # Fallback exit method


def update_timer(self) -> None:
    """
    Update the session timer display.
    
    Args:
        self: The application instance
    """
    # Only update if timer is active and root window still exists
    if self.timer_active and self.root.winfo_exists():
        try:
            elapsed = time.time() - self.start_time
            remaining = max(0, self.duration - elapsed)
            hours = int(remaining // 3600)
            minutes = int((remaining % 3600) // 60)
            seconds = int(remaining % 60)

            # Check if the time_label still exists before updating
            if self.time_label and self.time_label.winfo_exists():
                self.time_label.config(text=f"Time remaining: {hours}:{minutes:02d}:{seconds:02d}")
                self.root.after(1000, self.update_timer)  # Schedule next update
            else:
                # Label doesn't exist anymore, stop updates
                self.timer_active = False
        except tk.TclError:
            # Handle the case where the widget was destroyed
            self.timer_active = False
    # If timer is not active, just return without scheduling another update


def monitor_elapsed_time(self) -> None:
    """
    Monitor session time and trigger logout when time expires.
    
    Args:
        self: The application instance
    """
    while True:
        current_time = time.time()
        # Check if the duration has elapsed
        if current_time - self.start_time >= self.duration:
            # Use after method to close program from main thread
            self.root.after(0, self.close_program)
            break
        # Sleep before checking again
        time.sleep(1)


def close_program(self) -> None:
    """
    Close the application after session timeout.
    
    Args:
        self: The application instance
    """
    logging.info("Session timeout reached. Forcefully closing the application.")

    # Deactivate timer to prevent further updates
    self.timer_active = False

    messagebox.showinfo("Time Expired", "Your session has expired.")

    # Schedule the actual exit after a short delay to ensure the messagebox is closed
    self.root.after(200, self.perform_exit)


def logout(self) -> None:
    """
    Securely log out the user.
    
    Args:
        self: The application instance
    """
    if messagebox.askyesno("Logout", "Are you sure you want to log out?"):
        # Clear sensitive data
        self.login_status = False
        self.session_token = ""
        self.password_hash = ""
        self.password_salt = ""
        self.plaxis_password = ""

        # Log the logout event
        log_security_event(
            user_name=getattr(self, 'user_name', "Unknown"),
            case="User Logout",
            user_email=getattr(self, 'user_email', None)
        )

        # Deactivate timer updates
        self.timer_active = False

        # Return to login screen
        self.show_login_frame()


def dev_login(self) -> None:
    """
    Development-only login bypass (should be removed in production).
    
    Args:
        self: The application instance
    """
    if not _is_development_mode():
        logging.warning("Attempted to use dev_login when not in development mode")
        messagebox.showerror("Error", "Development login is disabled in production mode")
        return
        
    self.user_name = "Developer"
    self.login_status = True
    # Go directly to main frame without showing PLAXIS password dialog
    messagebox.showinfo("Success", "Welcome, Developer")
    self.show_main_frame()

# ------ Module Initialization ------

# The simplified email system is ready for use immediately
# No initialization required - emails use simple threading
logging.info("Simplified email system ready")


