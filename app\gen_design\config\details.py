"""
Configuration Details Module

This module provides a centralized system for displaying and managing
configuration details across all generative design components.
It integrates functionality from anchor geometry, excavation level,
and anchor parameters to provide a unified configuration view.
"""

import logging
import tkinter as tk
from tkinter import messagebox
from typing import Dict, Any, Optional, List
import datetime

try:
    from ..common import (
        FONT_REGULAR, FONT_BOLD, FONT_HEADER, BG_COLOR, ACCENT_COLOR, SUCCESS_COLOR,
        create_modal_window, create_scrollable_content_area, create_standard_frame,
        create_info_label, create_header_label, create_section_header, create_primary_button,
        create_secondary_button, DEFAULT_WINDOW_SIZES, bind_mouse_wheel_globally,
        update_anchor_geometry_config, update_anchor_parameters_config, update_excavation_level_config
    )
except ImportError:
    # Fall back to basic definitions if common module dependencies fail
    FONT_REGULAR = ("Arial", 10)
    FONT_BOLD = ("Arial", 10, "bold")
    FONT_HEADER = ("Arial", 14, "bold")
    BG_COLOR = "#ffffff"
    ACCENT_COLOR = "#0066cc"
    SUCCESS_COLOR = "#4CAF50"
    DEFAULT_WINDOW_SIZES = {"config": "1300x900"}
    
    # Define minimal fallback functions
    def create_modal_window(parent, title, size, **kwargs):
        window = tk.Toplevel(parent)
        window.title(title)
        window.geometry(size)
        return window
    
    def create_scrollable_content_area(parent):
        frame = tk.Frame(parent)
        return frame, None, frame
    
    def create_standard_frame(parent, **kwargs):
        return tk.Frame(parent, **kwargs)
    
    def create_info_label(parent, text, **kwargs):
        return tk.Label(parent, text=text, **kwargs)
    
    def create_header_label(parent, text, **kwargs):
        return tk.Label(parent, text=text, font=FONT_HEADER, **kwargs)
    
    def create_section_header(parent, text, **kwargs):
        return tk.Label(parent, text=text, font=FONT_BOLD, **kwargs)
    
    def create_primary_button(parent, text, command, **kwargs):
        return tk.Button(parent, text=text, command=command, **kwargs)
    
    def create_secondary_button(parent, text, command, **kwargs):
        return tk.Button(parent, text=text, command=command, **kwargs)
    
    def bind_mouse_wheel_globally(window, canvas):
        pass
    
    def update_anchor_geometry_config(app_instance, data, stage):
        pass
    
    def update_anchor_parameters_config(app_instance, data, stage):
        pass
    
    def update_excavation_level_config(app_instance, data, stage):
        pass

from .manager import ConfigurationManager


def show_configuration_details(app_instance):
    """
    Display comprehensive configuration details in an enhanced modal window.
    
    Args:
        app_instance: The application instance containing configuration data
    """
    try:
        # Create configuration manager
        config_manager = ConfigurationManager(app_instance)
        
        # Update configuration data from app instance
        if hasattr(app_instance, 'comprehensive_config'):
            _update_config_manager_from_comprehensive(config_manager, app_instance.comprehensive_config)
        
        # Create the configuration details window
        window = create_modal_window(
            app_instance.root,
            "Configuration Details",
            DEFAULT_WINDOW_SIZES.get("config", "1300x900"),
            resizable=True,
            center=True
        )
        
        # Create scrollable content area
        main_frame, canvas, content_frame = create_scrollable_content_area(window)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Create header
        header_frame = create_standard_frame(content_frame, bg=BG_COLOR)
        header_frame.pack(fill="x", padx=20, pady=10)
        
        create_header_label(header_frame, "Generative Design Configuration Details", bg=BG_COLOR).pack()
        
        # Create timestamp
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        create_info_label(header_frame, f"Generated: {timestamp}", bg=BG_COLOR).pack()
        
        # Create configuration sections
        _create_anchor_geometry_section(content_frame, config_manager)
        _create_anchor_parameters_section(content_frame, config_manager)
        _create_excavation_level_section(content_frame, config_manager)
        _create_summary_section(content_frame, config_manager)
        
        # Create action buttons
        action_frame = create_standard_frame(content_frame, bg=BG_COLOR)
        action_frame.pack(fill="x", padx=20, pady=20)
        
        def close_window():
            window.destroy()
        
        def refresh_details():
            window.destroy()
            show_configuration_details(app_instance)
        
        create_primary_button(action_frame, "Refresh", refresh_details).pack(side=tk.LEFT, padx=5)
        create_secondary_button(action_frame, "Close", close_window).pack(side=tk.LEFT, padx=5)
        
        # Bind mouse wheel for scrolling
        if canvas:
            bind_mouse_wheel_globally(window, canvas)
        
        # Focus the window
        window.focus_set()
        
    except Exception as e:
        error_msg = f"Error showing configuration details: {str(e)}"
        logging.error(error_msg)
        logging.exception("Detailed traceback:")
        messagebox.showerror("Error", "Unable to display configuration details.")


def _update_config_manager_from_comprehensive(config_manager: ConfigurationManager, comprehensive_config: Dict[str, Any]):
    """Update configuration manager from comprehensive config data."""
    try:
        # Update anchor geometry
        if 'anchor_geometry' in comprehensive_config:
            geometry_config = comprehensive_config['anchor_geometry']
            if geometry_config.get('configured', False):
                config_manager.update_geometry_config(
                    geometry_config.get('anchors', {}),
                    geometry_config.get('stage')
                )
        
        # Update anchor parameters
        if 'anchor_parameters' in comprehensive_config:
            params_config = comprehensive_config['anchor_parameters']
            if params_config.get('configured', False):
                config_manager.update_parameters_config(
                    params_config.get('parameters', {}),
                    params_config.get('stage')
                )
        
        # Update excavation level
        if 'excavation_level' in comprehensive_config:
            excavation_config = comprehensive_config['excavation_level']
            if excavation_config.get('configured', False):
                config_manager.update_excavation_config(
                    {'stage_data': excavation_config.get('stage_data', {})},
                    excavation_config.get('stage')
                )
        
    except Exception as e:
        logging.error(f"Error updating config manager from comprehensive config: {e}")


def _create_anchor_geometry_section(parent_frame: tk.Frame, config_manager: ConfigurationManager):
    """Create anchor geometry configuration section."""
    section_frame = create_standard_frame(parent_frame, bg=BG_COLOR)
    section_frame.pack(fill="x", padx=20, pady=10)
    
    create_section_header(section_frame, "Anchor Geometry Configuration", bg=BG_COLOR).pack(anchor="w")
    
    geometry_config = config_manager.config_data['anchor_geometry']
    status = "✓ Configured" if geometry_config['configured'] else "✗ Not Configured"
    status_color = SUCCESS_COLOR if geometry_config['configured'] else "#FF6B6B"
    
    status_label = create_info_label(section_frame, f"Status: {status}", bg=BG_COLOR, fg=status_color)
    status_label.pack(anchor="w")
    
    if geometry_config['configured']:
        stage = geometry_config.get('stage', 'Not specified')
        combinations = geometry_config.get('combinations', 1)
        create_info_label(section_frame, f"Stage: {stage}", bg=BG_COLOR).pack(anchor="w")
        create_info_label(section_frame, f"Combinations: {combinations}", bg=BG_COLOR).pack(anchor="w")


def _create_anchor_parameters_section(parent_frame: tk.Frame, config_manager: ConfigurationManager):
    """Create anchor parameters configuration section."""
    section_frame = create_standard_frame(parent_frame, bg=BG_COLOR)
    section_frame.pack(fill="x", padx=20, pady=10)
    
    create_section_header(section_frame, "Anchor Parameters Configuration", bg=BG_COLOR).pack(anchor="w")
    
    params_config = config_manager.config_data['anchor_parameters']
    status = "✓ Configured" if params_config['configured'] else "✗ Not Configured"
    status_color = SUCCESS_COLOR if params_config['configured'] else "#FF6B6B"
    
    status_label = create_info_label(section_frame, f"Status: {status}", bg=BG_COLOR, fg=status_color)
    status_label.pack(anchor="w")
    
    if params_config['configured']:
        stage = params_config.get('stage', 'Not specified')
        combinations = params_config.get('combinations', 1)
        create_info_label(section_frame, f"Stage: {stage}", bg=BG_COLOR).pack(anchor="w")
        create_info_label(section_frame, f"Combinations: {combinations}", bg=BG_COLOR).pack(anchor="w")


def _create_excavation_level_section(parent_frame: tk.Frame, config_manager: ConfigurationManager):
    """Create excavation level configuration section."""
    section_frame = create_standard_frame(parent_frame, bg=BG_COLOR)
    section_frame.pack(fill="x", padx=20, pady=10)
    
    create_section_header(section_frame, "Excavation Level Configuration", bg=BG_COLOR).pack(anchor="w")
    
    excavation_config = config_manager.config_data['excavation_level']
    status = "✓ Configured" if excavation_config['configured'] else "✗ Not Configured"
    status_color = SUCCESS_COLOR if excavation_config['configured'] else "#FF6B6B"
    
    status_label = create_info_label(section_frame, f"Status: {status}", bg=BG_COLOR, fg=status_color)
    status_label.pack(anchor="w")
    
    if excavation_config['configured']:
        stage = excavation_config.get('stage', 'Not specified')
        combinations = excavation_config.get('combinations', 1)
        create_info_label(section_frame, f"Stage: {stage}", bg=BG_COLOR).pack(anchor="w")
        create_info_label(section_frame, f"Combinations: {combinations}", bg=BG_COLOR).pack(anchor="w")


def _create_summary_section(parent_frame: tk.Frame, config_manager: ConfigurationManager):
    """Create configuration summary section."""
    section_frame = create_standard_frame(parent_frame, bg=BG_COLOR)
    section_frame.pack(fill="x", padx=20, pady=10)
    
    create_section_header(section_frame, "Configuration Summary", bg=BG_COLOR).pack(anchor="w")
    
    total_combinations = config_manager.get_total_combinations()
    ready_status = "✓ Ready for Generation" if config_manager.is_ready_for_generation() else "✗ Configuration Incomplete"
    ready_color = SUCCESS_COLOR if config_manager.is_ready_for_generation() else "#FF6B6B"
    
    create_info_label(section_frame, f"Total Combinations: {total_combinations}", bg=BG_COLOR).pack(anchor="w")
    status_label = create_info_label(section_frame, f"Status: {ready_status}", bg=BG_COLOR, fg=ready_color)
    status_label.pack(anchor="w")


# Export the main functions for backward compatibility
__all__ = [
    'show_configuration_details',
    'update_anchor_geometry_config',
    'update_anchor_parameters_config',
    'update_excavation_level_config'
]
