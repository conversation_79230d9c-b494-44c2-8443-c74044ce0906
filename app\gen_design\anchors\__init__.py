"""
Anchor Management Package

This package provides comprehensive anchor management functionality including:
- Geometry configuration for different anchor types (N, F, G)
- Parameter management (sections, units, spacing, prestress, etc.)
- Unified UI components for anchor configuration
- Data models and validation

The package maintains backward compatibility with the original module structure.
"""

# Import all classes and functions for backward compatibility
try:
    from .models import (
        UnifiedAnchorData,
        UnifiedAnchorSection,
        UnifiedAnchorUIComponents
    )
except ImportError:
    pass

try:
    from .geometry import (
        CoordinateSet,
        GTypeCoordinateSet,
        AnchorSection,
        GeometryUIComponents
    )
except ImportError:
    pass

try:
    from .parameters import (
        AnchorConfig,
        UIComponentData,
        SettlementData,
        DEFAULT_ANCHOR_VALUES,
        STEEL_SECTION_SHEETS,
        FALLBACK_SECTIONS
    )
except ImportError:
    pass

try:
    from .definitions import (
        edit_unified_anchor,
        get_default_coordinates_from_excel,
        _get_default_anchor_name,
        _populate_anchor_sections_with_defaults
    )
except ImportError:
    pass

__all__ = [
    # Models
    'UnifiedAnchorData',
    'UnifiedAnchorSection', 
    'UnifiedAnchorUIComponents',
    
    # Geometry
    'CoordinateSet',
    'GTypeCoordinateSet',
    'AnchorSection',
    'GeometryUIComponents',
    
    # Parameters
    'AnchorConfig',
    'UIComponentData',
    'SettlementData',
    'DEFAULT_ANCHOR_VALUES',
    'STEEL_SECTION_SHEETS',
    'FALLBACK_SECTIONS',
    
    # Definitions
    'edit_unified_anchor',
    'get_default_coordinates_from_excel',
    '_get_default_anchor_name',
    '_populate_anchor_sections_with_defaults'
]
