"""
PLAXIS Configuration Module

This module provides functionality for PLAXIS password configuration
and related UI components.
"""
# Standard library imports
import logging
from typing import Optional, Callable, Union
import os
import base64
import json
from pathlib import Path

# Third-party library imports
import tkinter as tk
from tkinter import messagebox, ttk, filedialog

# UI Constants
FONT_TITLE = ("Arial", 16, "bold")
FONT_SUBTITLE = ("Arial", 12)
FONT_NORMAL = ("Arial", 10)
FONT_SMALL = ("Arial", 8, "italic")
BUTTON_WIDTH_LARGE = 20
BUTTON_HEIGHT_LARGE = 2
BUTTON_WIDTH_MEDIUM = 15
BUTTON_HEIGHT_MEDIUM = 2
ENTRY_WIDTH = 40

# Minimum recommended password length
MIN_PASSWORD_LENGTH = 8

# Password storage settings
DATA_DIR = "C:\\ELS_RPA"
PASSWORD_FILE = os.path.join(DATA_DIR, "plaxis_config.dat")
# Simple encryption key (in production, use a more secure approach)
ENCRYPTION_KEY = b"PLAXISAUTOMATION"


def obfuscate_password(password: str) -> str:
    """
    Simple obfuscation for the password (not secure encryption).
    
    Args:
        password: Password to obfuscate
        
    Returns:
        Obfuscated password string
    """
    if not password:
        return ""
        
    # Convert password to bytes
    pwd_bytes = password.encode('utf-8')
    
    # XOR each byte with the key (cycling through key bytes)
    key_len = len(ENCRYPTION_KEY)
    xor_bytes = bytes([pwd_bytes[i] ^ ENCRYPTION_KEY[i % key_len] for i in range(len(pwd_bytes))])
    
    # Base64 encode the result
    return base64.b64encode(xor_bytes).decode('utf-8')


def deobfuscate_password(obfuscated: str) -> str:
    """
    Reverse the obfuscation to get the original password.
    
    Args:
        obfuscated: Obfuscated password string
        
    Returns:
        Original password string
    """
    if not obfuscated:
        return ""
        
    try:
        # Base64 decode
        xor_bytes = base64.b64decode(obfuscated)
        
        # XOR each byte with the key (cycling through key bytes)
        key_len = len(ENCRYPTION_KEY)
        pwd_bytes = bytes([xor_bytes[i] ^ ENCRYPTION_KEY[i % key_len] for i in range(len(xor_bytes))])
        
        # Convert back to string
        return pwd_bytes.decode('utf-8')
    except Exception as e:
        logging.error(f"Error deobfuscating password: {e}")
        return ""


def save_plaxis_password(password: str) -> bool:
    """
    Save PLAXIS password to file.
    
    Args:
        password: Password to save
        
    Returns:
        True if saved successfully, False otherwise
    """
    try:
        # Create directory if it doesn't exist
        os.makedirs(DATA_DIR, exist_ok=True)
        
        # Obfuscate password
        obfuscated = obfuscate_password(password)
        
        # Create data structure
        data = {
            "password": obfuscated,
            "timestamp": str(Path(PASSWORD_FILE).stat().st_mtime) if os.path.exists(PASSWORD_FILE) else None
        }
        
        # Write to file
        with open(PASSWORD_FILE, 'w') as f:
            json.dump(data, f)
            
        logging.info(f"PLAXIS password saved to {PASSWORD_FILE}")
        return True
        
    except Exception as e:
        logging.error(f"Error saving PLAXIS password: {e}")
        return False


def load_plaxis_password() -> Optional[str]:
    """
    Load PLAXIS password from file.
    
    Returns:
        Loaded password or None if not available
    """
    try:
        if not os.path.exists(PASSWORD_FILE):
            return None
            
        with open(PASSWORD_FILE, 'r') as f:
            data = json.load(f)
            
        # Get obfuscated password
        obfuscated = data.get("password", "")
        if not obfuscated:
            return None
            
        # Deobfuscate and return
        return deobfuscate_password(obfuscated)
        
    except Exception as e:
        logging.error(f"Error loading PLAXIS password: {e}")
        return None


def center_window(root: tk.Tk, window: tk.Toplevel) -> None:
    """
    Center a window on its parent.

    Args:
        root: The parent window
        window: The window to center
    """
    try:
        window.update_idletasks()
        
        # Get window dimensions
        width = window.winfo_width()
        height = window.winfo_height()
        
        # Get parent dimensions
        parent_width = root.winfo_width()
        parent_height = root.winfo_height()
        parent_x = root.winfo_x()
        parent_y = root.winfo_y()

        # Calculate position
        x = max(0, parent_x + (parent_width - width) // 2)
        y = max(0, parent_y + (parent_height - height) // 2)

        # Set position
        window.geometry(f"{width}x{height}+{x}+{y}")
        
    except tk.TclError as e:
        logging.warning(f"Error centering window: {e}")
    except Exception as e:
        logging.error(f"Unexpected error centering window: {e}")


def create_password_dialog(parent: tk.Tk, title: str, 
                          callback: Callable[[str], None]) -> tk.Toplevel:
    """
    Create a generic password input dialog.

    Args:
        parent: Parent window
        title: Dialog title
        callback: Function to call with password value

    Returns:
        The dialog window
    """
    dialog = tk.Toplevel(parent)
    dialog.title(title)
    dialog.geometry("400x200")  # Reduced height since we removed elements
    dialog.transient(parent)
    dialog.grab_set()
    dialog.resizable(False, False)

    # Try to set window icon
    try:
        dialog.iconbitmap('AIS.ico')
    except Exception as e:
        logging.debug(f"Icon not loaded: {str(e)}")

    # Add a frame for content
    content_frame = tk.Frame(dialog, padx=20, pady=15)
    content_frame.pack(fill="both", expand=True)

    # Center the dialog on parent
    center_window(parent, dialog)
    
    return dialog


def show_plaxis_password_dialog(self) -> None:
    """
    Show dialog to get PLAXIS configuration password.

    Args:
        self: The application instance
    """
    # Create the dialog window
    dialog = create_password_dialog(
        self.root, 
        "PLAXIS Configuration",
        lambda pwd: None  # Will be defined in on_submit
    )
    
    # Create content with improved layout
    content_frame = dialog.winfo_children()[0]
    
    # Heading
    tk.Label(
        content_frame, 
        text="Enter the PLAXIS Configure Password:",
        font=FONT_SUBTITLE
    ).pack(anchor="w", pady=(0, 10))
    
    # Password input
    pwd_var = tk.StringVar()
    
    # Try to load saved password
    saved_password = load_plaxis_password()
    if saved_password:
        pwd_var.set(saved_password)
    
    # Password entry
    plaxis_pwd_entry = tk.Entry(
        content_frame, 
        width=ENTRY_WIDTH, 
        show="*", 
        textvariable=pwd_var
    )
    plaxis_pwd_entry.pack(fill="x", pady=10)
    plaxis_pwd_entry.focus()
    
    # Remember password checkbox
    remember_var = tk.BooleanVar(value=True if saved_password else False)
    remember_cb = tk.Checkbutton(
        content_frame, 
        text="Remember password", 
        variable=remember_var,
        font=FONT_NORMAL
    )
    remember_cb.pack(anchor="w", pady=5)
    
    # Button frame
    button_frame = tk.Frame(content_frame)
    button_frame.pack(fill="x", pady=(15, 5))
    
    def on_submit() -> None:
        """Handle password submission."""
        password = pwd_var.get().strip()
        
        # Validate password
        if not password:
            messagebox.showerror("Error", "Password is required")
            return
            
        if len(password) < MIN_PASSWORD_LENGTH:
            result = messagebox.askokcancel(
                "Weak Password", 
                f"The password is shorter than the recommended minimum ({MIN_PASSWORD_LENGTH} characters).\n\n"
                "Do you still want to use this password?"
            )
            if not result:
                return
        
        # Store the password
        self.plaxis_password = password
        
        # Save password if remember is checked
        if remember_var.get():
            save_plaxis_password(password)
        else:
            # Delete saved password file if exists
            if os.path.exists(PASSWORD_FILE):
                try:
                    os.remove(PASSWORD_FILE)
                    logging.info(f"Deleted saved password file: {PASSWORD_FILE}")
                except Exception as e:
                    logging.error(f"Error deleting password file: {e}")
        
        dialog.destroy()

        # Update password status label using the app's method
        if hasattr(self, 'update_password_status_display'):
            self.update_password_status_display()

        messagebox.showinfo("Success", "PLAXIS password configured successfully")

    def on_cancel() -> None:
        """Handle dialog cancellation."""
        dialog.destroy()
    
    # Cancel button
    tk.Button(
        button_frame, 
        text="Cancel",
        command=on_cancel,
        width=BUTTON_WIDTH_MEDIUM
    ).pack(side="left", padx=(0, 10))
    
    # Submit button
    submit_btn = tk.Button(
        button_frame, 
        text="Submit",
        command=on_submit,
        width=BUTTON_WIDTH_MEDIUM,
        bg="#e6e6e6",  # Light gray background
        relief=tk.RAISED,
        bd=2
    )
    submit_btn.pack(side="right")

    # Bind Enter key to submit
    dialog.bind('<Return>', lambda event: on_submit())
    dialog.bind('<Escape>', lambda event: on_cancel())
    
    # Set focus on password entry
    plaxis_pwd_entry.focus_set()
