from plaxis.builder.utils import set_structure_material, deactivate_structure_void, deactivate_soil
from plaxis.geometry.draw import draw_water_level
from plaxis.geometry.get import get_NodeToNodeAnchor, get_EmbeddedBeam, get_fixedEndAnchor


def define_stages(g_i, df_structure_polygon, df_structure_geometry, df_structure_material, df_el, df_plate_loc,
                  df_anchor_loc, df_iwl, end_stage):
    """
    Defines the construction stages in the PLAXIS server using a modular approach.

    This function orchestrates the creation of all construction stages by delegating
    to specialized helper functions for each stage type. This modular design allows
    for easy extension and maintenance of stage definitions.

    Args:
        g_i: The PLAXIS server instance.
        df_structure_polygon: DataFrame containing structure polygon data.
        df_structure_geometry: DataFrame containing structure geometry data.
        df_structure_material: DataFrame containing structure material data.
        df_el (pd.DataFrame): DataFrame containing the excavation level coordinates with columns 'X (m)' and 'Y (m)'.
        df_plate_loc (pd.DataFrame): DataFrame containing the plate location coordinates with columns 'X (m)'.
        df_anchor_loc (pd.DataFrame): DataFrame containing the anchor location coordinates with columns 'X (m)', 'Y (m)', and 'Stage'.
        df_iwl (pd.DataFrame): DataFrame containing the initial water level coordinates with columns 'X (m)' and 'Y (m)'.
        end_stage: The final stage to process ('Final' for all stages, or specific stage number).

    Returns:
        None
    """
    # Initialize phase tracking
    phase_num = 0
    
    # Stage 0: Initial stage with water level
    initial_phase = _setup_initial_stage(g_i, df_iwl, phase_num)
    
    # Stage 1: Pile installation
    phase_num += 1
    pile_phase = _setup_pile_installation_stage(
        g_i, initial_phase, phase_num, 
        df_structure_polygon, df_structure_geometry, df_structure_material
    )
    
    # Extract plate materials for stage processing
    plate_materials = _extract_plate_materials(g_i)
    
    # Initialize stage configuration
    stage_config = _get_stage_config(df_anchor_loc, end_stage)
    plate_tracking = _initialize_plate_tracking(df_plate_loc)
    
    # Process construction stages (excavation + strut installation)
    final_phase = _process_construction_stages(
        g_i, pile_phase, phase_num, stage_config, plate_materials, 
        plate_tracking, df_el, df_plate_loc, df_anchor_loc, df_iwl
    )
    
    # Final excavation stage if needed
    if stage_config['final_stage']:
        _process_final_excavation_stage(
            g_i, final_phase, df_el, df_iwl, df_plate_loc
        )
    
    # Ensure we're in stages mode at the end
    g_i.gotostages()


def _create_phase(g_i, parent_phase, phase_num: int, identification: str, reset_displacements: bool = False):
    """
    Helper function to create and configure a new phase.
    
    Args:
        g_i: The PLAXIS server instance.
        parent_phase: The parent phase to derive from.
        phase_num: The phase number for identification.
        identification: The identification string for the phase.
        reset_displacements: Whether to reset displacements to zero.
        
    Returns:
        The newly created phase object.
    """
    new_phase = g_i.phase(parent_phase)
    g_i.setcurrentphase(new_phase)
    new_phase.setproperties("Identification", identification)
    if reset_displacements:
        new_phase.Deform.ResetDisplacementsToZero.set(True)
    return new_phase


def _setup_initial_stage(g_i, df_iwl, phase_num: int):
    """
    Sets up the initial stage with water level configuration.
    
    Args:
        g_i: The PLAXIS server instance.
        df_iwl: DataFrame containing initial water level coordinates.
        phase_num: The current phase number.
        
    Returns:
        The initial phase object.
    """
    phase0_s = g_i.Phases[0]
    g_i.setcurrentphase(phase0_s)
    phase0_s.setproperties("Identification", '0: Initial Stage')
    
    # Setup water level
    iwl_list = [(row['X (m)'], row['Y (m)']) for _, row in df_iwl.iterrows()]
    g_i.gotoflow()
    waterlevel_s = g_i.waterlevel(*iwl_list)
    g_i.setglobalwaterlevel(waterlevel_s, phase0_s)
    g_i.gotostages()
    
    # Activate interfaces
    for interface in g_i.Interfaces:
        g_i.activate(interface, phase0_s)
    
    return phase0_s


def _setup_pile_installation_stage(g_i, parent_phase, phase_num: int, df_structure_polygon, 
                                   df_structure_geometry, df_structure_material):
    """
    Sets up the pile installation stage with structure activation and material assignment.
    
    Args:
        g_i: The PLAXIS server instance.
        parent_phase: The parent phase to derive from.
        phase_num: The current phase number.
        df_structure_polygon: DataFrame containing structure polygon data.
        df_structure_geometry: DataFrame containing structure geometry data.
        df_structure_material: DataFrame containing structure material data.
        
    Returns:
        The pile installation phase object.
    """
    pile_phase = _create_phase(
        g_i, parent_phase, phase_num, 
        f'{phase_num}: Stage 0 Pile Installation', 
        reset_displacements=True
    )
    
    # Activate structural elements
    _activate_structural_elements(g_i, pile_phase)
    
    # Set structure materials and deactivate voids
    set_structure_material(g_i, pile_phase, df_structure_polygon, df_structure_geometry, df_structure_material)
    deactivate_structure_void(g_i, pile_phase, df_structure_polygon, df_structure_geometry)
    
    # Set initial plate materials
    _set_initial_plate_materials(g_i, pile_phase)
    
    return pile_phase


def _activate_structural_elements(g_i, phase):
    """
    Activates all structural elements (plates, line loads, point loads) for a phase.
    
    Args:
        g_i: The PLAXIS server instance.
        phase: The phase to activate elements in.
    """
    if len(g_i.Plates) != 0:
        lines = [b.Parent for b in g_i.Plates]
        g_i.activate(lines, phase)

    if len(g_i.LineLoads) != 0:
        lines = [b.Parent for b in g_i.LineLoads]
        g_i.activate(lines, phase)

    if len(g_i.PointLoads) != 0:
        points = [b.Parent for b in g_i.PointLoads]
        g_i.activate(points, phase)


def _extract_plate_materials(g_i):
    """
    Extracts and organizes plate materials by name for easy access.
    
    Args:
        g_i: The PLAXIS server instance.
        
    Returns:
        dict: Dictionary mapping material names to material objects.
    """
    plate_materials = {}
    list_platemat = [mat for mat in g_i.Materials[:] if mat.TypeName.value == 'PlateMat']
    
    material_mapping = {
        'Plate_1_Initial': 'platemat_1_0',
        'Plate_1_1st': 'platemat_1_1',
        'Plate_1_2nd': 'platemat_1_2',
        'Plate_2_Initial': 'platemat_2_0',
        'Plate_2_1st': 'platemat_2_1',
        'Plate_2_2nd': 'platemat_2_2'
    }
    
    for platemat in list_platemat:
        # Convert PLAXIS proxy object to string for comparison
        material_name = str(platemat.Name.value) if hasattr(platemat.Name, 'value') else str(platemat.Name)
        if material_name in material_mapping:
            plate_materials[material_mapping[material_name]] = platemat
    
    return plate_materials


def _set_initial_plate_materials(g_i, phase):
    """
    Sets initial materials for plates in the pile installation stage.
    
    Args:
        g_i: The PLAXIS server instance.
        phase: The phase to set materials in.
    """
    plate_materials = _extract_plate_materials(g_i)
    
    for b in g_i.Plates:
        if 'Plate_1' in str(b.Name) and 'platemat_1_0' in plate_materials:
            b.Material.set(phase, plate_materials['platemat_1_0'])
        elif 'Plate_2' in str(b.Name) and 'platemat_2_0' in plate_materials:
            b.Material.set(phase, plate_materials['platemat_2_0'])


def _get_stage_config(df_anchor_loc, end_stage):
    """
    Creates configuration object for stage processing.
    
    Args:
        df_anchor_loc: DataFrame containing anchor location data.
        end_stage: The final stage to process.
        
    Returns:
        dict: Configuration dictionary with stage parameters.
    """
    stage_list = df_anchor_loc['Stage'].unique()
    
    if end_stage == max(stage_list):
        end_stage = 'Final'
    
    end_stage_num = float('inf') if end_stage in ['', 'Final'] else int(end_stage)
    
    return {
        'stages': stage_list,
        'end_stage_num': end_stage_num,
        'final_stage': end_stage == 'Final'
    }


def _initialize_plate_tracking(df_plate_loc):
    """
    Initializes tracking data structure for plate anchor counts.
    
    Args:
        df_plate_loc: DataFrame containing plate location data.
        
    Returns:
        dict: Tracking dictionary with plate positions and anchor counts.
    """
    plate1_x = df_plate_loc.loc[df_plate_loc['Plate Name'] == 'Plate_1', 'X (m)'].values[0]
    plate2_x = df_plate_loc.loc[df_plate_loc['Plate Name'] == 'Plate_2', 'X (m)'].values[0]
    
    return {
        'plate1_x': plate1_x,
        'plate2_x': plate2_x,
        'plate1_anchor_count': 0,
        'plate2_anchor_count': 0
    }


def _process_construction_stages(g_i, pile_phase, phase_num: int, stage_config: dict, 
                               plate_materials: dict, plate_tracking: dict, df_el, 
                               df_plate_loc, df_anchor_loc, df_iwl):
    """
    Processes all construction stages (excavation + strut installation pairs).
    
    Args:
        g_i: The PLAXIS server instance.
        pile_phase: The pile installation phase.
        phase_num: Starting phase number.
        stage_config: Stage configuration dictionary.
        plate_materials: Dictionary of plate materials.
        plate_tracking: Plate tracking dictionary.
        df_el: DataFrame containing excavation level data.
        df_plate_loc: DataFrame containing plate location data.
        df_anchor_loc: DataFrame containing anchor location data.
        df_iwl: DataFrame containing initial water level data.
        
    Returns:
        The last created phase object.
    """
    current_phase = pile_phase
    current_phase_num = phase_num
    stage = 1
    
    for stage_num in stage_config['stages']:
        if stage <= stage_config['end_stage_num']:
            # Excavation stage
            current_phase_num += 1
            excavation_phase = _process_excavation_stage(
                g_i, current_phase, current_phase_num, stage, 
                df_el, df_iwl, df_plate_loc
            )
            
            # Strut installation stage
            current_phase_num += 1
            strut_phase = _process_strut_installation_stage(
                g_i, excavation_phase, current_phase_num, stage_num, 
                df_anchor_loc, plate_materials, plate_tracking, df_plate_loc
            )
            
            current_phase = strut_phase
        
        stage += 1
        
        # Handle end stage excavation if needed
        if stage - 1 == stage_config['end_stage_num'] and not stage_config['final_stage']:
            current_phase_num += 1
            final_excavation_phase = _process_excavation_stage(
                g_i, current_phase, current_phase_num, stage, 
                df_el, df_iwl, df_plate_loc
            )
            current_phase = final_excavation_phase
    
    return current_phase


def _process_excavation_stage(g_i, parent_phase, phase_num: int, stage: int, 
                             df_el, df_iwl, df_plate_loc):
    """
    Processes a single excavation stage.
    
    Args:
        g_i: The PLAXIS server instance.
        parent_phase: The parent phase to derive from.
        phase_num: The current phase number.
        stage: The stage number.
        df_el: DataFrame containing excavation level data.
        df_iwl: DataFrame containing initial water level data.
        df_plate_loc: DataFrame containing plate location data.
        
    Returns:
        The excavation phase object.
    """
    excavation_phase = _create_phase(
        g_i, parent_phase, phase_num,
        f'{phase_num}: Stage {stage} Excavation',
        reset_displacements=(stage == 1)
    )
    
    # Deactivate soil polygons above the excavation level
    deactivate_soil(g_i, excavation_phase, df_el, stage)
    
    # Draw water level for the current stage
    draw_water_level(g_i, excavation_phase, df_iwl, df_el, df_plate_loc, stage)
    
    g_i.gotostages()
    
    return excavation_phase


def _process_strut_installation_stage(g_i, parent_phase, phase_num: int, stage_num, 
                                    df_anchor_loc, plate_materials: dict, 
                                    plate_tracking: dict, df_plate_loc):
    """
    Processes a single strut installation stage.
    
    Args:
        g_i: The PLAXIS server instance.
        parent_phase: The parent phase to derive from.
        phase_num: The current phase number.
        stage_num: The stage number for anchor filtering.
        df_anchor_loc: DataFrame containing anchor location data.
        plate_materials: Dictionary of plate materials.
        plate_tracking: Plate tracking dictionary.
        df_plate_loc: DataFrame containing plate location data.
        
    Returns:
        The strut installation phase object.
    """
    strut_phase = _create_phase(
        g_i, parent_phase, phase_num,
        f'{phase_num}: Stage {stage_num} Strut Installation'
    )
    
    # Install anchors for this stage
    _install_stage_anchors(g_i, strut_phase, df_anchor_loc, stage_num, plate_tracking)
    
    # Update plate materials based on anchor counts
    _update_plate_materials(g_i, strut_phase, plate_materials, plate_tracking)
    
    return strut_phase


def _install_stage_anchors(g_i, phase, df_anchor_loc, stage_num, plate_tracking: dict):
    """
    Installs all anchors for a specific stage.
    
    Args:
        g_i: The PLAXIS server instance.
        phase: The phase to install anchors in.
        df_anchor_loc: DataFrame containing anchor location data.
        stage_num: The stage number to filter anchors.
        plate_tracking: Plate tracking dictionary (updated in place).
    """
    col_prestress = df_anchor_loc.columns.get_loc('Prestress (kN/m)')
    condition = df_anchor_loc['Stage'] == stage_num
    
    for j, row in df_anchor_loc[condition].iterrows():
        f_preload = -row[col_prestress]
        anchor_type = row['Anchor Type (F/N/G)']
        
        # Install the anchor based on type
        _install_single_anchor(g_i, phase, row, anchor_type, f_preload)
        
        # Update plate anchor counts
        _update_anchor_counts(row, plate_tracking)


def _install_single_anchor(g_i, phase, anchor_row, anchor_type: str, f_preload: float):
    """
    Installs a single anchor based on its type.
    
    Args:
        g_i: The PLAXIS server instance.
        phase: The phase to install the anchor in.
        anchor_row: Row containing anchor data.
        anchor_type: Type of anchor ('F', 'N', or 'G').
        f_preload: Preload force to apply.
    """
    x1, y1, x2, y2, x3, y3 = anchor_row[['X1 (m)', 'Y1 (m)', 'X2 (m)', 'Y2 (m)', 'X3 (m)', 'Y3 (m)']]
    
    if anchor_type in ['N', 'G']:
        anchor_n = get_NodeToNodeAnchor(g_i, x1, y1, x2, y2, tab_current='Staged construction')
        anchor_n.activate(phase)
        if f_preload != 0:
            anchor_n.AdjustPrestress.set(phase, True)
            anchor_n.PrestressForce.set(phase, f_preload)
        
        if anchor_type == 'G':
            anchor_g = get_EmbeddedBeam(g_i, x2, y2, x3, y3, tab_current='Staged construction')
            anchor_g.activate(phase)
    
    elif anchor_type == 'F':
        anchor_f = get_fixedEndAnchor(g_i, x1, y1, tab_current='Staged construction')
        anchor_f.activate(phase)
        if f_preload != 0:
            anchor_f.AdjustPrestress.set(phase, True)
            anchor_f.PrestressForce.set(phase, f_preload)


def _update_anchor_counts(anchor_row, plate_tracking: dict):
    """
    Updates plate anchor counts based on anchor installation.
    
    Args:
        anchor_row: Row containing anchor data.
        plate_tracking: Plate tracking dictionary (updated in place).
    """
    x1 = anchor_row['X1 (m)']
    
    if x1 == plate_tracking['plate1_x']:
        plate_tracking['plate1_anchor_count'] += 1
    elif x1 == plate_tracking['plate2_x']:
        plate_tracking['plate2_anchor_count'] += 1


def _update_plate_materials(g_i, phase, plate_materials: dict, plate_tracking: dict):
    """
    Updates plate materials based on current anchor counts.
    
    Args:
        g_i: The PLAXIS server instance.
        phase: The phase to update materials in.
        plate_materials: Dictionary of available plate materials.
        plate_tracking: Current plate tracking data.
    """
    plate_mapping = {
        'Plate_1': {
            1: plate_materials.get('platemat_1_1'),
            2: plate_materials.get('platemat_1_2')
        },
        'Plate_2': {
            1: plate_materials.get('platemat_2_1'),
            2: plate_materials.get('platemat_2_2')
        }
    }
    
    for b in g_i.Plates:
        for plate_name, materials in plate_mapping.items():
            if plate_name in str(b.Name):
                if plate_name == 'Plate_1':
                    anchor_count = plate_tracking['plate1_anchor_count']
                elif plate_name == 'Plate_2':
                    anchor_count = plate_tracking['plate2_anchor_count']
                else:
                    continue
                
                if anchor_count == 1 and materials[1]:
                    b.Material.set(phase, materials[1])
                elif anchor_count >= 2 and materials[2]:
                    b.Material.set(phase, materials[2])


def _process_final_excavation_stage(g_i, parent_phase, df_el, df_iwl, df_plate_loc):
    """
    Processes the final excavation stage.
    
    Args:
        g_i: The PLAXIS server instance.
        parent_phase: The parent phase to derive from.
        df_el: DataFrame containing excavation level data.
        df_iwl: DataFrame containing initial water level data.
        df_plate_loc: DataFrame containing plate location data.
    """
    # Get the current phase number from the parent phase identification
    parent_id = str(parent_phase.Identification.value)
    if ':' in parent_id:
        try:
            parent_num = int(parent_id.split(':')[0])
            phase_num = parent_num + 1
        except:
            phase_num = 99  # fallback
    else:
        phase_num = 99  # fallback
    
    final_phase = _create_phase(
        g_i, parent_phase, phase_num,
        f'{phase_num}: Stage Final Final Excavation',
        reset_displacements=False
    )
    
    # Deactivate soil polygons above the final excavation level
    deactivate_soil(g_i, final_phase, df_el, 'Final')
    
    # Draw water level for the final excavation stage
    draw_water_level(g_i, final_phase, df_iwl, df_el, df_plate_loc, 'Final')
