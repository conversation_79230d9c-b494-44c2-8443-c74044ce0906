from tkinter import messagebox, ttk, filedialog
from typing import Dict, List, Tuple, Any, Optional
from tkinter import messagebox
from plaxis.generative_design.run import run_generative_design as run_generative_design
import _Main_Class
import _Main_Read
from app.login_frame import send_email_log
import logging


# Define these as methods of the class to properly access self attributes
# ============================================================================
# UTILITY FUNCTIONS FOR ANCHOR PARAMETER COLLECTION
# ============================================================================

def create_anchor_params_structure(anchor_name: str, anchor_type: str, stage: str) -> Dict[str, Any]:
    """
    Create the basic parameter structure for an anchor.
    
    Args:
        anchor_name: Name of the anchor
        anchor_type: Type of anchor (N, F, or G)
        stage: Stage value from UI
        
    Returns:
        Dict containing the basic anchor parameter structure
    """
    base_params = {
        'Name': anchor_name,
        'Type': anchor_type,
        'Section_Unit': [],
        'Spacing': [],
        'Stage': stage,
        'Settlement': []
    }
    
    # Add type-specific parameters
    if anchor_type in ["N", "F"]:
        base_params['Prestress'] = []
    elif anchor_type == "G":
        base_params.update({
            'Theta_g': [],
            'Theta_s': [],
            'Length': []
        })
    
    return base_params


def validate_and_convert_numeric_value(value: str, data_type: type, field_name: str) -> Optional[float]:
    """
    Validate and convert a string value to numeric type with error handling.
    
    Args:
        value: String value to convert
        data_type: Target data type (float or int)
        field_name: Name of the field for error reporting
        
    Returns:
        Converted numeric value or None if validation fails
        
    Raises:
        Shows warning dialog for invalid values
    """
    if not value or not value.strip():
        return None
        
    try:
        if data_type == int:
            return int(value.strip())
        else:
            return float(value.strip())
    except ValueError:
        messagebox.showwarning("Invalid Value", f"Invalid {field_name} value: {value}")
        logging.warning(f"Invalid {field_name} value: {value}")
        return None


def collect_section_unit_data(ui_components: Dict[str, Any]) -> List[Tuple[str, int]]:
    """
    Collect section-unit combination data from UI components.
    
    Args:
        ui_components: Dictionary containing UI component references
        
    Returns:
        List of tuples containing (section, unit) combinations
    """
    section_unit_combinations = []
    combo_vars = ui_components.get('combo_vars', [])
    
    for combo in combo_vars:
        section = combo['section'].get()
        unit = combo['unit'].get()
        
        if section and unit:
            converted_unit = validate_and_convert_numeric_value(unit, int, "unit")
            if converted_unit is not None:
                section_unit_combinations.append((section, converted_unit))
                
    return section_unit_combinations


def collect_numeric_entries(ui_components: Dict[str, Any], entry_key: str, 
                           field_name: str, data_type: type = float) -> List[float]:
    """
    Collect numeric values from UI entry components.
    
    Args:
        ui_components: Dictionary containing UI component references
        entry_key: Key to access the specific entry list in ui_components
        field_name: Name of the field for error reporting
        data_type: Data type for conversion (default: float)
        
    Returns:
        List of converted numeric values
    """
    numeric_values = []
    entries = ui_components.get(entry_key, [])
    
    for entry in entries:
        value = entry.get().strip()
        if value:
            converted_value = validate_and_convert_numeric_value(value, data_type, field_name)
            if converted_value is not None:
                numeric_values.append(converted_value)
                
    return numeric_values


def collect_settlement_data(ui_components: Dict[str, Any], anchor_name: str) -> List[Tuple[Tuple[float, float], Tuple[float, float]]]:
    """
    Collect settlement criteria data from UI components.
    
    Args:
        ui_components: Dictionary containing UI component references
        anchor_name: Name of the anchor for error reporting
        
    Returns:
        List of settlement line coordinates as ((x1, y1), (x2, y2)) tuples
    """
    settlement_lines = []
    settlement_vars = ui_components.get('settlement_vars', [])
    
    for var_dict in settlement_vars:
        try:
            x1 = float(var_dict['Line_x1'].get().strip() or "0")
            y1 = float(var_dict['Line_y1'].get().strip() or "0")
            x2 = float(var_dict['Line_x2'].get().strip() or "0")
            y2 = float(var_dict['Line_y2'].get().strip() or "0")
            
            # Only add if at least one value is non-zero
            if x1 != 0 or y1 != 0 or x2 != 0 or y2 != 0:
                settlement_lines.append(((x1, y1), (x2, y2)))
        except (ValueError, KeyError) as e:
            messagebox.showwarning("Invalid Value", 
                                   f"Invalid settlement coordinates for {anchor_name}: {str(e)}")
            logging.warning(f"Invalid settlement coordinates for {anchor_name}: {str(e)}")
            
    return settlement_lines


# ============================================================================
# TYPE-SPECIFIC ANCHOR PARAMETER COLLECTORS
# ============================================================================

def collect_nf_anchor_parameters(anchor_name: str, 
                                anchor_type: str, stage: str, 
                                ui_components: Dict[str, Any]) -> Dict[str, Any]:
    """
    Collect parameters for Type N and F anchors.
    
    Args:
        anchor_name: Name of the anchor
        anchor_type: Type of anchor (N or F)
        stage: Stage value from UI
        ui_components: Dictionary containing UI component references
        
    Returns:
        Dictionary containing all collected anchor parameters
    """
    # Create basic parameter structure
    anchor_params = create_anchor_params_structure(anchor_name, anchor_type, stage)
    
    # Collect section-unit combinations
    anchor_params['Section_Unit'] = collect_section_unit_data(ui_components)
    
    # Collect spacing values
    anchor_params['Spacing'] = collect_numeric_entries(ui_components, 'spacing_entries', 'spacing')
    
    # Collect prestress values
    anchor_params['Prestress'] = collect_numeric_entries(ui_components, 'prestress_entries', 'prestress')
    
    # Collect settlement criteria
    anchor_params['Settlement'] = collect_settlement_data(ui_components, anchor_name)
    
    logging.info(f"Collected parameters for {anchor_type} anchor '{anchor_name}': "
                f"{len(anchor_params['Section_Unit'])} section-unit combinations, "
                f"{len(anchor_params['Spacing'])} spacing values, "
                f"{len(anchor_params['Prestress'])} prestress values, "
                f"{len(anchor_params['Settlement'])} settlement lines")
    
    return anchor_params


def collect_g_anchor_parameters(anchor_name: str, 
                               anchor_type: str, stage: str,
                               ui_components: Dict[str, Any]) -> Dict[str, Any]:
    """
    Collect parameters for Type G anchors.
    
    Args:
        anchor_name: Name of the anchor
        anchor_type: Type of anchor (G)
        stage: Stage value from UI
        ui_components: Dictionary containing UI component references
        
    Returns:
        Dictionary containing all collected anchor parameters
    """
    # Create basic parameter structure
    anchor_params = create_anchor_params_structure(anchor_name, anchor_type, stage)
    
    # Collect section-unit combinations
    anchor_params['Section_Unit'] = collect_section_unit_data(ui_components)
    
    # Collect spacing values (Note: error message in original code mentioned "Theta_g" but this seems wrong)
    anchor_params['Spacing'] = collect_numeric_entries(ui_components, 'spacing_entries', 'spacing')
    
    # Collect theta_g values
    anchor_params['Theta_g'] = collect_numeric_entries(ui_components, 'theta_g_entries', 'Theta_g')
    
    # Collect theta_s values
    anchor_params['Theta_s'] = collect_numeric_entries(ui_components, 'theta_s_entries', 'Theta_s')
    
    # Collect anchor length values
    anchor_params['Length'] = collect_numeric_entries(ui_components, 'anchor_length_entries', 'Anchor Length')
    
    # Collect settlement criteria
    anchor_params['Settlement'] = collect_settlement_data(ui_components, anchor_name)
    
    logging.info(f"Collected parameters for G anchor '{anchor_name}': "
                f"{len(anchor_params['Section_Unit'])} section-unit combinations, "
                f"{len(anchor_params['Spacing'])} spacing values, "
                f"{len(anchor_params['Theta_g'])} theta_g values, "
                f"{len(anchor_params['Theta_s'])} theta_s values, "
                f"{len(anchor_params['Length'])} length values, "
                f"{len(anchor_params['Settlement'])} settlement lines")
    
    return anchor_params


# ============================================================================
# DISPLAY AND SUMMARY FUNCTIONS
# ============================================================================

def update_anchor_table_display(anchor_table, anchor_name: str, anchor_params: Dict[str, Any]) -> None:
    """
    Update the anchor table display with new parameter values.
    
    Args:
        anchor_table: Reference to the UI table component
        anchor_name: Name of the anchor to update
        anchor_params: Dictionary containing anchor parameters
    """
    # Find the item in the table
    for item_id in anchor_table.get_children():
        item_values = anchor_table.item(item_id, "values")
        if item_values[0] == anchor_name:
            new_values = list(item_values)
            
            # Update section and unit if available
            if anchor_params['Section_Unit']:
                new_values[2] = anchor_params['Section_Unit'][0][0]
                new_values[3] = str(anchor_params['Section_Unit'][0][1])
            
            # Update spacing if available
            if anchor_params['Spacing']:
                new_values[4] = str(anchor_params['Spacing'][0])
            
            # Update prestress if available (only for N and F types)
            if 'Prestress' in anchor_params and anchor_params['Prestress']:
                new_values[5] = str(anchor_params['Prestress'][0])
            
            anchor_table.item(item_id, values=new_values)
            break


def generate_parameter_summary(anchor_params: Dict[str, Any]) -> str:
    """
    Generate a detailed text summary of anchor parameters.
    
    Args:
        anchor_params: Dictionary containing anchor parameters
        
    Returns:
        Formatted string containing parameter summary
    """
    anchor_name = anchor_params['Name']
    anchor_type = anchor_params['Type']
    
    summary = [f"Anchor {anchor_name} (Type {anchor_type}):\n"]
    
    # Add section-unit combinations
    summary.append("  Section-Unit Combinations:")
    for section, unit in anchor_params['Section_Unit']:
        summary.append(f"    - {section} × {unit}")
    
    # Add spacing values
    spacing_unit = "deg" if anchor_type == "G" else "m"
    summary.append(f"\n  Spacing Values ({spacing_unit}):")
    for spacing in anchor_params['Spacing']:
        summary.append(f"    - {spacing}")
    
    # Add type-specific parameters
    if anchor_type in ["N", "F"]:
        # Add prestress values
        summary.append("\n  Prestress Values (kN/m):")
        for prestress in anchor_params['Prestress']:
            summary.append(f"    - {prestress}")
    
    elif anchor_type == "G":
        # Add theta_g values
        summary.append("\n  Theta_g Values (deg):")
        for theta_g in anchor_params['Theta_g']:
            summary.append(f"    - {theta_g}")
        
        # Add theta_s values
        summary.append("\n  Theta_s Values (deg):")
        for theta_s in anchor_params['Theta_s']:
            summary.append(f"    - {theta_s}")
        
        # Add anchor length values
        summary.append("\n  Anchor Length Values (m):")
        for length in anchor_params['Length']:
            summary.append(f"    - {length}")
    
    # Add settlement criteria
    if anchor_params['Settlement']:
        summary.append("\n  Settlement Lines:")
        for (x1, y1), (x2, y2) in anchor_params['Settlement']:
            summary.append(f"    - Line from ({x1}, {y1}) to ({x2}, {y2})")
    
    return "\n".join(summary)


# ============================================================================
# MAIN PARAMETER COLLECTION ORCHESTRATOR
# ============================================================================

def collect_all_anchor_parameters(self) -> Tuple[Dict[str, Dict[str, Any]], List[str]]:
    """
    Collect parameters from UI for all anchor types.
    
    Args:
        self: Reference to the main class instance containing UI components
        
    Returns:
        Tuple containing:
        - Dictionary of saved parameters (anchor_name -> parameters)
        - List of summary strings for display
    """
    saved_parameters = {}
    saved_values = []
    
    # Get stage value
    stage = self.stage_var.get()
    
    # Process each anchor type
    for anchor_type, anchors in self.anchors_by_type.items():
        if anchor_type not in ["N", "F", "G"]:
            logging.warning(f"Unknown anchor type: {anchor_type}")
            continue
            
        for anchor in anchors:
            anchor_name = anchor.name
            ui_components = self.anchor_ui_components.get(anchor_name, {})
            
            try:
                # Collect parameters based on anchor type
                if anchor_type in ["N", "F"]:
                    anchor_params = collect_nf_anchor_parameters(
                        anchor_name, anchor_type, stage, ui_components
                    )
                elif anchor_type == "G":
                    anchor_params = collect_g_anchor_parameters(
                        anchor_name, anchor_type, stage, ui_components
                    )
                
                # Store parameters and update display
                saved_parameters[anchor_name] = anchor_params
                update_anchor_table_display(self.anchor_table, anchor_name, anchor_params)
                
                # Generate summary
                summary = generate_parameter_summary(anchor_params)
                saved_values.append(summary)
                
            except Exception as e:
                error_msg = f"Error processing anchor {anchor_name}: {str(e)}"
                logging.error(error_msg)
                messagebox.showerror("Error", error_msg)
    
    logging.info(f"Successfully collected parameters for {len(saved_parameters)} anchors")
    return saved_parameters, saved_values

def save_anchor_parameters(self):
    """
    Save anchor parameters and close the parameter window.
    
    This function:
    1. Collects parameters from UI for all anchor types
    2. Stores them for later use
    3. Displays summary of collected parameters
    4. Closes the parameter window
    5. Updates the main window to show saved configuration
    
    Args:
        self: Reference to the main class instance containing UI components
    """
    try:
        logging.info("Starting save anchor parameters process")
        
        # Step 1: Collect all anchor parameters from UI
        saved_parameters, saved_values = collect_all_anchor_parameters(self)
        
        # Step 2: Store parameters for later use
        self.autorun_params = saved_parameters
        
        # Step 3: Show summary to user
        if saved_values:
            summary = "The following parameters were saved:\n\n" + "\n\n".join(saved_values)
            messagebox.showinfo("Parameters Saved", summary)
            logging.info(f"Successfully saved parameters for {len(saved_parameters)} anchors")
        else:
            messagebox.showinfo("No Changes", "No anchor parameters were updated")
            logging.warning("No anchor parameters were collected")
            return
        
        # Step 4: Close the parameter window
        self.param_window.destroy()
        
        # Step 5: Update comprehensive configuration using the integrated system
        try:
            from app.gen_design.config_details import update_anchor_parameters_config
            stage = self.stage_var.get() if hasattr(self, 'stage_var') else None
            parameters_data = {'parameters': saved_parameters}
            update_anchor_parameters_config(self, parameters_data, stage)
            logging.info("Updated anchor parameters configuration using integrated system")
        except ImportError:
            logging.warning("Could not import update_anchor_parameters_config function")
        except Exception as e:
            logging.error(f"Error updating comprehensive configuration: {str(e)}")
        
    except Exception as e:
        error_msg = f"Error saving anchor parameters: {str(e)}"
        logging.error(error_msg, exc_info=True)
        messagebox.showerror("Save Error", error_msg)


def execute_generative_design_process(self):
    """
    Execute the PLAXIS generative design automation process.
    
    This function handles the automation parts of the generative design:
    1. Initializes PLAXIS automation components
    2. Executes the generative design automation
    3. Shows completion status
    
    Args:
        self: Reference to the main class instance containing saved parameters
    """
    try:
        logging.info("Starting generative design automation process")
        
        # Check if parameters are saved
        if not hasattr(self, 'autorun_params') or not self.autorun_params:
            messagebox.showwarning("No Parameters", 
                                 "Please configure and save anchor parameters before running generative design.")
            return
        
        # Step 1: Initialize PLAXIS automation
        if not initialize_plaxis_automation(self):
            logging.error("Failed to initialize PLAXIS automation")
            return
        
        # Step 2: Execute generative design automation
        if not execute_generative_design_automation(self):
            logging.error("Failed to execute generative design automation")
            return
        
        # Step 3: Show completion message
        messagebox.showinfo("Success", "ELS Generative Design completed successfully.")
        logging.info("Generative design automation process completed successfully")
        
    except Exception as e:
        error_msg = f"Unexpected error in generative design automation: {str(e)}"
        logging.error(error_msg, exc_info=True)
        messagebox.showerror("Generative Design Error", error_msg)


# ============================================================================
# PLAXIS AUTOMATION FUNCTIONS
# ============================================================================

def initialize_plaxis_automation(self) -> bool:
    """
    Initialize required objects and paths for PLAXIS automation.
    
    Args:
        self: Reference to the main class instance
        
    Returns:
        True if initialization successful, False otherwise
    """
    try:
        # Initialize required objects
        self.excel_inputs = _Main_Class.ExcelInputs()
        
        # Preserve the ExcelPlaxis path that was set during file selection
        excel_plaxis_path = self.file_paths.ExcelPlaxis
        logging.info(f"Preserving user-selected Excel file path: {excel_plaxis_path}")
        
        # Reinitialize file paths but restore the user-selected Excel path
        self.file_paths = _Main_Class.FilePaths()
        self.file_paths.ExcelPlaxis = excel_plaxis_path
        logging.info(f"Restored ExcelPlaxis path to: {self.file_paths.ExcelPlaxis}")
        
        # Read steel section data
        self.excel_inputs = _Main_Read.read_steel_section(
            self.file_paths.ELSSteelSection, self.excel_inputs
        )
        
        # Set communication ports
        self.PORT_i = 10000
        self.PORT_o = 10001
        
        logging.info("PLAXIS automation initialization completed successfully")
        return True
        
    except Exception as e:
        error_msg = f"Failed to initialize PLAXIS automation: {str(e)}"
        logging.error(error_msg)
        messagebox.showerror("Initialization Error", error_msg)
        return False


def execute_generative_design_automation(self) -> bool:
    """
    Execute the PLAXIS generative design automation.
    
    Args:
        self: Reference to the main class instance
        
    Returns:
        True if execution successful, False otherwise
    """
    try:
        # Run the generative design
        run_generative_design(self)
        logging.info("ELS Generative Design completed successfully")
        return True
        
    except Exception as e:
        error_msg = f"Failed to execute generative design: {str(e)}"
        logging.error(error_msg)
        messagebox.showerror("Execution Error", error_msg)
        return False


# ============================================================================
# MAIN GENERATIVE DESIGN FUNCTION (REFACTORED)
# ============================================================================

def generative_design(self):
    """
    Legacy function redirected to new execution process.
    
    This function is kept for backward compatibility and redirects
    to the new execute_generative_design_process function.
    
    Args:
        self: Reference to the main class instance containing UI components
    """
    execute_generative_design_process(self)
