from shapely import Polygon

from plaxis.geometry.utils import get_soil_polygon_points, check_point_above_polyline


def define_mesh(g_i):
    """
    Defines the mesh settings in the PLAXIS server.

    Args:
        g_i: The PLAXIS server instance.

    Returns:
        None
    """
    # Switch to the mesh mode
    g_i.gotomesh()
    # Set the mesh to medium density
    g_i.mesh(0.06)


def get_input_phase_by_number(g_i, phase_num):
    """
    Finds and returns a PLAXIS input phase by its phase number.

    Parameters:
        g_i: PLAXIS input object
        phase_num (int): The phase number to search for

    Returns:
        Phase object matching the given phase number

    Raises:
        ValueError: If no phase with the specified number is found
    """
    for phase_i in g_i.Phases:
        if phase_i.Number.value == phase_num:
            return phase_i

    raise ValueError(f"Phase with number {phase_num} not found")


def get_output_phase_by_number(g_o, phase_num):
    """
    Finds and returns a PLAXIS output phase by its phase number.

    Parameters:
        g_o: PLAXIS output object
        phase_num (int): The phase number to search for

    Returns:
        Phase object matching the given phase number

    Raises:
        ValueError: If no phase with the specified number is found
    """
    for phase_o in g_o.Phases[:]:
        if phase_o.Number.value == phase_num:
            return phase_o

    raise ValueError(f"Phase with number {phase_num} not found")


def set_structure_material(g_i, phase, df_structure_polygon, df_structure_geometry, df_structure_material):
    list_soilmat = [mat for mat in g_i.Materials[:] if mat.TypeName.value == 'SoilMat']
    for _, row in df_structure_geometry[df_structure_geometry['Type (O/I)'] == 'O'].iterrows():
        polyline_name = row['Polyline']
        structure_points = [(round(r['X (m)'], 2), round(r['Y (m)'], 2)) for _, r in
                            df_structure_polygon[df_structure_polygon['Polyline'] == polyline_name].iterrows()]

        structure = Polygon(structure_points)

        for soil_polygon in g_i.SoilPolygons:
            scaled_soil = Polygon(get_soil_polygon_points(soil_polygon)).buffer(-0.05)

            if scaled_soil.within(structure):
                structure_name = df_structure_geometry.loc[
                    df_structure_geometry['Polyline'] == polyline_name, 'Structure Name'
                ].values[0]
                material_abbv = df_structure_material.loc[
                    df_structure_material['Structure Name'] == structure_name, 'Material Abbv'
                ].values[0]

                for soilmat in list_soilmat:
                    if material_abbv == soilmat.Name:
                        soil_polygon.setmaterial(phase, soilmat)
                        break


def deactivate_structure_void(g_i, phase, df_structure_polygon, df_structure_geometry):
    for _, row in df_structure_geometry[df_structure_geometry['Type (O/I)'] == 'I'].iterrows():
        polyline = row['Polyline']
        structure_points = [(round(r['X (m)'], 2), round(r['Y (m)'], 2)) for _, r in
                            df_structure_polygon[df_structure_polygon['Polyline'] == polyline].iterrows()]

        structure = Polygon(structure_points)

        for soil_polygon in g_i.SoilPolygons:
            if Polygon(get_soil_polygon_points(soil_polygon)).buffer(-0.05).within(structure):
                soil_polygon.deactivate(phase)


def deactivate_soil(g_i, phase, df_el, stage):
    """
    Deactivates soil polygons in the PLAXIS server for a given excavation stage.

    Args:
        g_i: The PLAXIS server instance.
        phase: The phase in which to deactivate the soil polygons.
        df_el (pd.DataFrame): DataFrame containing the excavation level coordinates with columns 'X (m)' and 'Y (m)'.
        stage (int or str): The stage for which to deactivate the soil polygons.

    Returns:
        None
    """
    df_el = df_el[df_el['Stage'] == stage].sort_values('X (m)').reset_index(drop=True)

    # print(df_el)
    # Iterate df_el except the last row
    for soil_polygon in g_i.SoilPolygons:
        points = get_soil_polygon_points(soil_polygon)
        if all(check_point_above_polyline(df_el, round(x, 3), round(y, 3)) for x, y in points):
            soil_polygon.deactivate(phase)
