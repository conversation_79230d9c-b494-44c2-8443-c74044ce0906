"""
Data Utilities for Generative Design Module

This module contains utility functions for data validation, conversion,
and error handling used across the generative design modules.
"""

import logging
from typing import Dict, List, Optional, Any, Union, Tuple
from tkinter import messagebox
import pandas as pd


# =============================================================================
# DATA VALIDATION UTILITIES
# =============================================================================

def validate_dataframe(dataframe, name: str) -> bool:
    """
    Validate that a dataframe exists and is not empty.
    
    Args:
        dataframe: The dataframe to validate
        name: Name of the dataframe for logging
        
    Returns:
        True if dataframe is valid, False otherwise
    """
    if dataframe is None:
        logging.warning(f"{name} dataframe is None")
        return False
    if dataframe.empty:
        logging.warning(f"{name} dataframe is empty")
        return False
    return True


def validate_numeric_range(value: float, min_val: float = None, max_val: float = None, 
                          field_name: str = "Value") -> bool:
    """
    Validate that a numeric value is within the specified range.
    
    Args:
        value: The value to validate
        min_val: Minimum allowed value (optional)
        max_val: Maximum allowed value (optional)
        field_name: Name of the field for error messages
        
    Returns:
        True if value is valid, False otherwise
    """
    if min_val is not None and value < min_val:
        logging.warning(f"{field_name} {value} is below minimum {min_val}")
        return False
    if max_val is not None and value > max_val:
        logging.warning(f"{field_name} {value} is above maximum {max_val}")
        return False
    return True


def validate_coordinate_data(coordinates: List[Dict[str, float]], 
                           required_keys: List[str] = None) -> bool:
    """
    Validate coordinate data structure.
    
    Args:
        coordinates: List of coordinate dictionaries
        required_keys: Required keys in each coordinate dict
        
    Returns:
        True if coordinates are valid, False otherwise
    """
    if not coordinates:
        logging.warning("Empty coordinates list")
        return False
    
    if required_keys is None:
        required_keys = ["X", "Y"]
    
    for i, coord in enumerate(coordinates):
        if not isinstance(coord, dict):
            logging.warning(f"Coordinate {i} is not a dictionary")
            return False
        
        for key in required_keys:
            if key not in coord:
                logging.warning(f"Coordinate {i} missing required key: {key}")
                return False
            
            if not isinstance(coord[key], (int, float)):
                logging.warning(f"Coordinate {i} key {key} is not numeric: {coord[key]}")
                return False
    
    return True


# =============================================================================
# DATA CONVERSION UTILITIES
# =============================================================================

def safe_float_conversion(value: Any, default: float = 0.0) -> float:
    """
    Safely convert a value to float with a default fallback.
    
    Args:
        value: The value to convert
        default: Default value if conversion fails
        
    Returns:
        Converted float value or default
    """
    if value is None or str(value).strip() in ['', 'nan', 'NaN', 'None']:
        return default
    try:
        return float(value)
    except (ValueError, TypeError):
        logging.warning(f"Could not convert value '{value}' to float, using {default}")
        return default


def safe_int_conversion(value: Any, default: int = 0) -> int:
    """
    Safely convert a value to int with a default fallback.
    
    Args:
        value: The value to convert
        default: Default value if conversion fails
        
    Returns:
        Converted int value or default
    """
    if value is None or str(value).strip() in ['', 'nan', 'NaN', 'None']:
        return default
    try:
        return int(float(value))  # Convert via float to handle "1.0" strings
    except (ValueError, TypeError):
        logging.warning(f"Could not convert value '{value}' to int, using {default}")
        return default


def safe_string_conversion(value: Any, default: str = "") -> str:
    """
    Safely convert a value to string with a default fallback.
    
    Args:
        value: The value to convert
        default: Default value if conversion fails
        
    Returns:
        Converted string value or default
    """
    if value is None:
        return default
    try:
        return str(value).strip()
    except (ValueError, TypeError):
        logging.warning(f"Could not convert value '{value}' to string, using '{default}'")
        return default


def normalize_stage_value(stage: Union[str, int, float]) -> str:
    """
    Normalize stage values to consistent string format.
    
    Args:
        stage: Stage value in various formats
        
    Returns:
        Normalized stage as string
    """
    if stage is None:
        return "1"  # Default stage
    
    try:
        # Convert to int first to handle float strings like "1.0"
        stage_int = int(float(stage))
        return str(stage_int)
    except (ValueError, TypeError):
        logging.warning(f"Could not normalize stage value '{stage}', using '1'")
        return "1"


# =============================================================================
# DATA STRUCTURE UTILITIES
# =============================================================================

def find_coordinate_columns(dataframe: pd.DataFrame) -> Tuple[Optional[str], Optional[str]]:
    """
    Find X and Y coordinate columns in a dataframe using common naming patterns.
    
    Args:
        dataframe: The dataframe to search
        
    Returns:
        Tuple of (x_column_name, y_column_name) or (None, None) if not found
    """
    x_col_patterns = ['X', 'x', 'X_coord', 'X_coordinate', 'Easting', 'East']
    y_col_patterns = ['Y', 'y', 'Y_coord', 'Y_coordinate', 'Northing', 'North']
    
    x_cols = []
    y_cols = []
    
    for pattern in x_col_patterns:
        matching_cols = [col for col in dataframe.columns if pattern.upper() in str(col).upper()]
        if matching_cols:
            x_cols.extend(matching_cols)
    
    for pattern in y_col_patterns:
        matching_cols = [col for col in dataframe.columns if pattern.upper() in str(col).upper()]
        if matching_cols:
            y_cols.extend(matching_cols)
    
    # Remove duplicates while preserving order
    x_cols = list(dict.fromkeys(x_cols))
    y_cols = list(dict.fromkeys(y_cols))
    
    return (x_cols[0] if x_cols else None, y_cols[0] if y_cols else None)


def extract_unique_values(dataframe: pd.DataFrame, column: str, 
                         convert_to_string: bool = True) -> List[str]:
    """
    Extract unique values from a dataframe column.
    
    Args:
        dataframe: The dataframe to extract from
        column: Column name to extract values from
        convert_to_string: Whether to convert values to strings
        
    Returns:
        List of unique values (as strings if convert_to_string is True)
    """
    if not validate_dataframe(dataframe, "input"):
        return []
    
    if column not in dataframe.columns:
        logging.warning(f"Column '{column}' not found in dataframe")
        return []
    
    try:
        unique_values = dataframe[column].dropna().unique().tolist()
        if convert_to_string:
            unique_values = [str(val) for val in unique_values]
        return sorted(unique_values)
    except Exception as e:
        logging.error(f"Error extracting unique values from column '{column}': {e}")
        return []


def create_default_stage_data(stage_options: List[str], 
                            default_coordinates: List[Dict[str, float]] = None) -> Dict[str, List[Dict[str, float]]]:
    """
    Create default stage data structure with coordinates.
    
    Args:
        stage_options: List of stage identifiers
        default_coordinates: Default coordinates to use (optional)
        
    Returns:
        Dictionary mapping stages to coordinate lists
    """
    if default_coordinates is None:
        default_coordinates = [{"X": 0.0, "Y": 0.0}]
    
    return {stage: default_coordinates.copy() for stage in stage_options}


# =============================================================================
# ERROR HANDLING UTILITIES
# =============================================================================

def handle_error_with_logging(error: Exception, operation: str, 
                             show_dialog: bool = True) -> str:
    """
    Handle errors with consistent logging and optional user notification.
    
    Args:
        error: The exception that occurred
        operation: Description of the operation that failed
        show_dialog: Whether to show error dialog to user
        
    Returns:
        Formatted error message
    """
    error_msg = f"Error in {operation}: {str(error)}"
    logging.error(error_msg)
    
    if show_dialog:
        messagebox.showerror("Error", error_msg)
    
    return error_msg


def handle_validation_error(field_name: str, value: Any, expected_type: str = None,
                          show_dialog: bool = True) -> str:
    """
    Handle validation errors with consistent messaging.
    
    Args:
        field_name: Name of the field that failed validation
        value: The invalid value
        expected_type: Expected type/format description
        show_dialog: Whether to show error dialog to user
        
    Returns:
        Formatted validation error message
    """
    if expected_type:
        error_msg = f"Invalid {field_name}: '{value}' (expected {expected_type})"
    else:
        error_msg = f"Invalid {field_name}: '{value}'"
    
    logging.warning(error_msg)
    
    if show_dialog:
        messagebox.showwarning("Validation Error", error_msg)
    
    return error_msg


def log_operation_success(operation: str, details: str = None):
    """
    Log successful operations with consistent formatting.
    
    Args:
        operation: Description of the successful operation
        details: Additional details about the operation
    """
    if details:
        logging.info(f"✓ {operation}: {details}")
    else:
        logging.info(f"✓ {operation}")


# =============================================================================
# GEOMETRY DATA UTILITIES  
# =============================================================================

def convert_coordinates_to_polylines(coordinates: List[Dict[str, float]]) -> List[List[Dict[str, float]]]:
    """
    Convert flat coordinate list to polyline sets.
    
    For now, puts all coordinates in a single polyline set.
    Future enhancement could implement intelligent grouping based on coordinate proximity.
    
    Args:
        coordinates: List of coordinate dictionaries
        
    Returns:
        List containing polyline sets (each set is a list of coordinates)
    """
    if not coordinates:
        return [[{"X": 0.0, "Y": 0.0}]]
    
    # For simplicity, put all coordinates in one polyline set
    # Future enhancement: implement intelligent polyline separation
    return [coordinates]


def calculate_polyline_bounds(coordinates: List[Dict[str, float]]) -> Dict[str, float]:
    """
    Calculate bounding box for a list of coordinates.
    
    Args:
        coordinates: List of coordinate dictionaries with X, Y keys
        
    Returns:
        Dictionary with min_x, max_x, min_y, max_y keys
    """
    if not coordinates:
        return {"min_x": 0.0, "max_x": 0.0, "min_y": 0.0, "max_y": 0.0}
    
    x_coords = [coord.get("X", 0.0) for coord in coordinates]
    y_coords = [coord.get("Y", 0.0) for coord in coordinates]
    
    return {
        "min_x": min(x_coords),
        "max_x": max(x_coords),
        "min_y": min(y_coords),
        "max_y": max(y_coords)
    }