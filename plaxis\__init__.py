"""
PLAXIS Automation Package

This package provides Python automation tools for PLAXIS geotechnical software.
It includes modules for building models, generative design, geometry operations,
master data processing, and result analysis.
"""

# Import subpackages to make them available
from . import builder
from . import generative_design
from . import geometry
from . import master_data
from . import result

# Import key functions that are commonly used
from .builder.build import build_plaxis
from .result.read import read_uy_min_max, extract_fea_result
from .result.chart import chart_uy

from version_config import PACKAGE_VERSION

__version__ = PACKAGE_VERSION

__all__ = [
    # Subpackages
    'builder',
    'generative_design', 
    'geometry',
    'master_data',
    'result',
    
    # Key functions
    'build_plaxis',
    'read_uy_min_max',
    'extract_fea_result',
    'chart_uy'
]