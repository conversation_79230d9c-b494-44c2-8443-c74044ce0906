# Project Structure and Architecture

## Top-Level Directory Structure
```
Plaxis-Automation/
├── main.py                          # Main application entry point
├── version_config.py                # Centralized version management (v1.8.1)
├── requirements.txt                 # Python dependencies
├── AIS.ico                         # Application icon
├── nuitka embed.txt                # Build configuration for executable
├── .github/                        # GitHub workflows and instructions
├── .serena/                        # Serena MCP configuration
├── app/                            # UI application modules (ENHANCED)
├── plaxis/                         # PLAXIS automation package
├── calculation/                    # Calculation logic modules
├── Library_Steel/                  # Steel section library data
├── Sample Input/                   # Example input files
├── _Main_Class.py                  # Core data structures (legacy)
├── _Main_Initialize.py             # Application initialization (legacy)
└── _Main_Read.py                   # Excel file reading (legacy)
```

## Enhanced Modular Architecture (v1.8.x)

### Application UI Package (`app/`)
```
app/
├── __init__.py
├── config_server.py               # Configuration management
├── login_frame.py                 # Authentication system
├── main_frame.py                  # Main application window
├── build_fem/                     # FEM model building interface
│   ├── __init__.py
│   └── main_window.py
└── gen_design/                    # ENHANCED Generative design modules
    ├── __init__.py
    ├── main_window.py             # Main generative design interface
    ├── run.py                     # Execution logic
    ├── anchor_geom_class.py       # Anchor geometry classes
    ├── anchor_geom_def.py         # Anchor geometry configuration
    ├── anchor_param_class.py      # Anchor parameter classes
    ├── anchor_param_def.py        # Anchor parameter configuration
    ├── excavation_class.py        # Excavation geometry classes
    ├── excavation_def.py          # Excavation configuration
    └── common/                    # NEW: Shared components package
        ├── __init__.py
        ├── data_utils.py          # Data processing utilities
        ├── ui_components.py       # Reusable UI components
        ├── ui_constants.py        # Styling constants
        └── window_utils.py        # Window management utilities
```

### PLAXIS Automation Package (`plaxis/`)
```
plaxis/
├── __init__.py
├── builder/                        # Model building functionality
│   ├── __init__.py
│   ├── build.py                   # Core building logic
│   ├── run.py                     # Execution coordination
│   ├── stage.py                   # Construction stage management
│   └── utils.py                   # Building utilities
├── generative_design/             # Parametric design and optimization
│   ├── __init__.py
│   ├── combination.py             # Parameter combination generation
│   ├── run.py                     # Generative design execution
│   ├── stage.py                   # Stage-based design management
│   └── utils.py                   # Design utilities
├── geometry/                      # Geometric operations
│   ├── __init__.py
│   ├── draw.py                    # Drawing and construction
│   ├── get.py                     # Geometry extraction
│   ├── material.py               # Material assignment
│   └── utils.py                   # Geometric utilities
├── master_data/                   # Data processing and management
│   ├── __init__.py
│   └── processor.py               # Data processing logic
└── result/                        # Result processing and visualization
    ├── __init__.py
    ├── chart.py                   # Chart generation
    └── read.py                    # Result extraction
```

## Advanced Component Architecture

### UI Component Hierarchy
```
GeometryUIComponents (Manager)
├── AnchorSection (N, F1, F2, G1, G2)
│   ├── CoordinateSet (X1, Y1, X2, Y2)
│   └── GTypeCoordinateSet (X1, Y1 only)
├── ExcavationLevelUIComponents (Manager)
│   └── ExcavationStageSection
│       └── ExcavationPolylineSet
│           └── ExcavationCoordinatePoint
└── Dynamic UI Components
    ├── UIComponentData
    ├── AnchorConfig
    └── SettlementData
```

### Shared Constants and Styling
- **Apple-Inspired Design System**: Consistent color palette, typography, and sizing
- **UI Constants**: Centralized styling constants in `app/gen_design/common/ui_constants.py`
- **Component Reusability**: Standard buttons, frames, and input components
- **Responsive Design**: Dynamic UI generation based on anchor types and data

## Data Flow Architecture

### Modern Input Data Path (v1.8.x)
```
Excel Files → plaxis.master_data → app.gen_design → Component Classes → plaxis.builder → PLAXIS Model
```

### UI Flow Architecture
```
main.py → app.main_frame → app.login_frame → Feature Selection → Specialized Modules
```

### Generative Design Flow (Enhanced)
```
app.gen_design.main_window → Anchor Geometry → Parameter Configuration → Excavation Configuration → plaxis.generative_design → Results
```

### Advanced Anchor Configuration Flow
```
app.gen_design.anchor_geom_def → GeometryUIComponents → AnchorSection → CoordinateSet → Validation → Save
```

## Key Architectural Patterns

### Component-Based Design
- **Reusable UI Components**: `CoordinateSet`, `AnchorSection`, `GeometryUIComponents`
- **Type-Specific Components**: `GTypeCoordinateSet` for G-type anchors
- **Dynamic UI Generation**: Components created based on anchor types and user selections
- **State Management**: Components maintain their own state and validation

### Data Management
- **Excel Integration**: Comprehensive Excel reading with validation and default value loading
- **Configuration Management**: Centralized version and setting management in `version_config.py`
- **Parameter Validation**: Multi-level validation for user inputs
- **Type Safety**: Extensive use of type hints and data classes

### Advanced UI Patterns
- **Apple-Inspired Design**: Consistent styling with modern UI patterns
- **Modal Dialogs**: Proper modal window management with parent-child relationships
- **Scrollable Content**: Canvas-based scrolling for complex forms
- **Dynamic Component Management**: Add/remove functionality for flexible configurations

## File Dependencies

### Input Files Structure
- **A.PLAXISInput_Property.xlsx** - Material and structural properties
- **A.PLAXISInput_Geometry.xlsx** - Geometric definitions and anchor locations
- **A.PLAXISInput_Geology.xlsx** - Soil and geological data
- **B.PLAXIS_Input.xlsx** - Consolidated PLAXIS model data

### Configuration Files
- **version_config.py** - Single source of truth for version information (v1.8.1)
- **AIS.ico** - Application icon file
- **requirements.txt** - Python dependency specification (matplotlib, plxscripting, scipy, numpy, pandas, openpyxl, shapely, nuitka)

## Design Principles

### Component Reusability
- **UI Components**: Reusable coordinate sets, anchor sections, dynamic components
- **Shared Utilities**: Common functions in `app/gen_design/common/`
- **Parameter Management**: Generic parameter collection and validation systems
- **Type-Specific Handling**: Different components for different anchor types

### Separation of Concerns
- **UI Layer**: Pure presentation logic in `app/` modules with sophisticated component hierarchy
- **Business Logic**: Core functionality in `plaxis/` package
- **Data Layer**: Input/output handling and validation with Excel integration
- **Shared Components**: Reusable elements in `app/gen_design/common/`

### Extensibility
- **Plugin Architecture**: Easy addition of new anchor types and components
- **Modular Design**: New features can be added without affecting existing code
- **Configuration-Driven**: Behavior controlled through constants and configuration
- **Component-Based**: New UI components can be easily integrated