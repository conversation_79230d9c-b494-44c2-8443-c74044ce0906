"""
Unified Anchor Management Classes

This module provides backward compatibility for the reorganized anchor management system.
All classes have been moved to the anchors package but are re-exported here for compatibility.
"""

# Import all classes from the new location for backward compatibility
from .anchors.models import (
    UnifiedAnchorData,
    UnifiedAnchorSection,
    UnifiedAnchorUIComponents
)

# Re-export for backward compatibility
__all__ = [
    'UnifiedAnchorData',
    'UnifiedAnchorSection',
    'UnifiedAnchorUIComponents'
]
