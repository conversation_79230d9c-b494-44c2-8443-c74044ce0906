nuitka --mingw64 --standalone --onefile --enable-plugin=tk-inter --windows-disable-console --windows-icon-from-ico=AIS.ico main.py

C:\ProgramData\Seequent\PLAXIS Python Distribution V3\python\python.exe

Set-Alias python "C:\ProgramData\Seequent\PLAXIS Python Distribution V3\python\python.exe"


nuitka --mingw64 --standalone --enable-plugin=tk-inter --windows-console-mode=disable --windows-icon-from-ico=AIS.ico --include-data-dir=C:/Users/<USER>/PycharmProjects/Plaxis-Automation/Library_Steel=Library_Steel --include-data-files=C:/Users/<USER>/PycharmProjects/Plaxis-Automation/AIS.ico=AIS.ico --windows-company-name="<PERSON> Sze" --windows-file-description="ELS Design RPA" --windows-product-version="1.8.1" main.py

nuitka --mingw64 --standalone --enable-plugin=tk-inter --windows-console-mode=disable --windows-icon-from-ico=AIS.ico --include-data-dir=C:/Users/<USER>/VSCodeProjects/Plaxis-Automation/Library_Steel=Library_Steel --include-data-files=C:/Users/<USER>/VSCodeProjects/Plaxis-Automation/AIS.ico=AIS.ico --windows-company-name="Alex Sze" --windows-file-description="ELS Design RPA" --windows-product-version="1.8.1" main.py