"""
Data import module for the PLAXIS Automation Tool.

This module provides functions for reading and processing input data from Excel files.
It handles geometry, property, geology, and PLAXIS-specific data import.
"""

import os
import logging
from typing import Tuple, Optional
import pandas as pd

import _Main_Class

# Configure logger
logger = logging.getLogger(__name__)


def read_input_geometry(file_path: str, excel_inputs: _Main_Class.ExcelInputs) -> _Main_Class.ExcelInputs:
    """
    Read geometry data from Excel file.
    
    Args:
        file_path: Path to the geometry Excel file
        excel_inputs: ExcelInputs object to store the data
        
    Returns:
        Updated ExcelInputs object with geometry data
    
    Raises:
        FileNotFoundError: If the file doesn't exist
        ValueError: If required sheets are missing
    """
    try:
        # Read all required sheets
        required_sheets = ['Section', 'Anchor', 'Plate', 'VBH']
        
        with pd.ExcelFile(file_path) as xlsx:
            sheet_names = xlsx.sheet_names
            
            # Validate required sheets exist
            missing_sheets = [sheet for sheet in required_sheets if sheet not in sheet_names]
            if missing_sheets:
                raise ValueError(f"Missing required sheets in geometry file: {', '.join(missing_sheets)}")
            
            # Read standard sheets
            excel_inputs.Section = xlsx.parse('Section')
            excel_inputs.Anchor = xlsx.parse('Anchor')
            excel_inputs.Plate = xlsx.parse('Plate')
            excel_inputs.VBH = xlsx.parse('VBH')
            
            # Read optional Geological Section sheet
            if 'Geological Section' in sheet_names:
                excel_inputs.GeoSection = xlsx.parse('Geological Section')
        
        # Process coordinate data
        if 'X' in excel_inputs.VBH.columns:
            excel_inputs.VBH['X (mm)'] = excel_inputs.VBH['X']

        # Sort and reset Plate data
        if 'X (mm)' in excel_inputs.Plate.columns:
            excel_inputs.Plate = excel_inputs.Plate.sort_values(by='X (mm)', ascending=True)
            excel_inputs.Plate = excel_inputs.Plate.reset_index(drop=True)
        
        logger.info(f"Successfully read geometry data from {file_path}")
        return excel_inputs
        
    except FileNotFoundError:
        logger.error(f"Geometry file not found: {file_path}")
        raise
    except ValueError as ve:
        logger.error(f"Error reading geometry data: {ve}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error reading geometry data: {e}")
        raise


def read_input_property(file_path: str, excel_inputs: _Main_Class.ExcelInputs) -> _Main_Class.ExcelInputs:
    """
    Read property data from Excel file.
    
    Args:
        file_path: Path to the property Excel file
        excel_inputs: ExcelInputs object to store the data
        
    Returns:
        Updated ExcelInputs object with property data
        
    Raises:
        FileNotFoundError: If the file doesn't exist
        ValueError: If required sheets are missing
    """
    try:
        # Read all required sheets
        required_sheets = ['Steel_Sections_Properties', 'Plate_Properties']
        
        with pd.ExcelFile(file_path) as xlsx:
            sheet_names = xlsx.sheet_names
            
            # Validate required sheets exist
            missing_sheets = [sheet for sheet in required_sheets if sheet not in sheet_names]
            if missing_sheets:
                raise ValueError(f"Missing required sheets in property file: {', '.join(missing_sheets)}")
            
            excel_inputs.SteelSectionProp = xlsx.parse('Steel_Sections_Properties')
            excel_inputs.PlateProp = xlsx.parse('Plate_Properties')
        
        logger.info(f"Successfully read property data from {file_path}")
        return excel_inputs
        
    except FileNotFoundError:
        logger.error(f"Property file not found: {file_path}")
        raise
    except ValueError as ve:
        logger.error(f"Error reading property data: {ve}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error reading property data: {e}")
        raise


def read_input_geology(file_path: str, excel_inputs: _Main_Class.ExcelInputs) -> _Main_Class.ExcelInputs:
    """
    Read geology data from Excel file.
    
    Args:
        file_path: Path to the geology Excel file
        excel_inputs: ExcelInputs object to store the data
        
    Returns:
        Updated ExcelInputs object with geology data
        
    Raises:
        FileNotFoundError: If the file doesn't exist
        ValueError: If required sheets are missing
    """
    try:
        # Read all required sheets
        required_sheets = ['Soil_Properties', 'Borehole']
        
        with pd.ExcelFile(file_path) as xlsx:
            sheet_names = xlsx.sheet_names
            
            # Validate required sheets exist
            missing_sheets = [sheet for sheet in required_sheets if sheet not in sheet_names]
            if missing_sheets:
                raise ValueError(f"Missing required sheets in geology file: {', '.join(missing_sheets)}")
            
            excel_inputs.SoilProperties = xlsx.parse('Soil_Properties')
            excel_inputs.Borehole = xlsx.parse('Borehole')
        
        # Process soil properties data
        if 'Bottom Level (mPD)' in excel_inputs.SoilProperties.columns:
            excel_inputs.SoilProperties['Bottom Level (mPD)'] = excel_inputs.SoilProperties['Bottom Level (mPD)'].fillna(-99999)
            excel_inputs.SoilProperties = (
                excel_inputs.SoilProperties.sort_values(
                    by=['Soil Type', 'Bottom Level (mPD)'],
                    ascending=[True, False]
                ).reset_index(drop=True)
            )
        
        # Process borehole data
        if all(col in excel_inputs.Borehole.columns for col in ['Borehole', 'Top Level (mPD)']):
            excel_inputs.Borehole = excel_inputs.Borehole.sort_values(
                ['Borehole', 'Top Level (mPD)'],
                ascending=[True, False]
            ).reset_index(drop=True)
        
        logger.info(f"Successfully read geology data from {file_path}")
        return excel_inputs
        
    except FileNotFoundError:
        logger.error(f"Geology file not found: {file_path}")
        raise
    except ValueError as ve:
        logger.error(f"Error reading geology data: {ve}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error reading geology data: {e}")
        raise


def read_input_plaxis(file_path: str, excel_master: _Main_Class.ExcelMaster) -> _Main_Class.ExcelMaster:
    """
    Read PLAXIS input data from Excel file.
    
    Args:
        file_path: Path to the PLAXIS input Excel file
        excel_master: ExcelMaster object to store the data
        
    Returns:
        Updated ExcelMaster object with PLAXIS data
        
    Raises:
        FileNotFoundError: If the file doesn't exist
        ValueError: If required sheets are missing
    """
    try:
        # Define required sheets
        required_sheets = [
            'Ground_Profile', 'Soil_Properties', 'Plate_Location', 'Anchor_Location',
            'Excavation_Level', 'Section', 'Initial_Water_Level', 'Line_Load',
            'Point_Load', 'Structure_Polygon', 'Structure_Geometry', 'Structure_Material'
        ]
        
        with pd.ExcelFile(file_path) as xlsx:
            sheet_names = xlsx.sheet_names
            
            # Validate required sheets exist
            missing_sheets = [sheet for sheet in required_sheets if sheet not in sheet_names]
            if missing_sheets:
                logger.warning(f"Missing sheets in PLAXIS file: {', '.join(missing_sheets)}")
            
            # Read standard sheets with error handling for each
            try:
                excel_master.GroundProfile = xlsx.parse('Ground_Profile', index_col=0)
            except Exception as e:
                logger.warning(f"Error reading Ground_Profile sheet: {e}")
                excel_master.GroundProfile = pd.DataFrame()
            
            try:
                excel_master.SoilProperties = xlsx.parse('Soil_Properties')
            except Exception as e:
                logger.warning(f"Error reading Soil_Properties sheet: {e}")
                excel_master.SoilProperties = pd.DataFrame()
                
            try:
                excel_master.PlateLoc = xlsx.parse('Plate_Location')
            except Exception as e:
                logger.warning(f"Error reading Plate_Location sheet: {e}")
                excel_master.PlateLoc = pd.DataFrame()
                
            try:
                excel_master.AnchorLoc = xlsx.parse('Anchor_Location')
            except Exception as e:
                logger.warning(f"Error reading Anchor_Location sheet: {e}")
                excel_master.AnchorLoc = pd.DataFrame()
                
            try:
                excel_master.EL = xlsx.parse('Excavation_Level')
            except Exception as e:
                logger.warning(f"Error reading Excavation_Level sheet: {e}")
                excel_master.EL = pd.DataFrame()
                
            try:
                excel_master.Section = xlsx.parse('Section')
            except Exception as e:
                logger.warning(f"Error reading Section sheet: {e}")
                excel_master.Section = pd.DataFrame()
                
            try:
                excel_master.IWL = xlsx.parse('Initial_Water_Level')
            except Exception as e:
                logger.warning(f"Error reading Initial_Water_Level sheet: {e}")
                excel_master.IWL = pd.DataFrame()
                
            try:
                excel_master.LineLoad = xlsx.parse('Line_Load')
            except Exception as e:
                logger.warning(f"Error reading Line_Load sheet: {e}")
                excel_master.LineLoad = pd.DataFrame()
                
            try:
                excel_master.PointLoad = xlsx.parse('Point_Load')
            except Exception as e:
                logger.warning(f"Error reading Point_Load sheet: {e}")
                excel_master.PointLoad = pd.DataFrame()
                
            try:
                excel_master.StructurePolygon = xlsx.parse('Structure_Polygon')
            except Exception as e:
                logger.warning(f"Error reading Structure_Polygon sheet: {e}")
                excel_master.StructurePolygon = pd.DataFrame()
                
            try:
                excel_master.StructureGeometry = xlsx.parse('Structure_Geometry')
            except Exception as e:
                logger.warning(f"Error reading Structure_Geometry sheet: {e}")
                excel_master.StructureGeometry = pd.DataFrame()
                
            try:
                excel_master.StructureMaterial = xlsx.parse('Structure_Material')
            except Exception as e:
                logger.warning(f"Error reading Structure_Material sheet: {e}")
                excel_master.StructureMaterial = pd.DataFrame()
                
            # Read optional Input_Parameter sheet
            if 'Input_Parameter' in sheet_names:
                try:
                    excel_master.InputParameter = xlsx.parse('Input_Parameter')
                except Exception as e:
                    logger.warning(f"Error reading Input_Parameter sheet: {e}")
        
        logger.info(f"Successfully read PLAXIS data from {file_path}")
        return excel_master
        
    except FileNotFoundError:
        logger.error(f"PLAXIS file not found: {file_path}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error reading PLAXIS data: {e}")
        raise


def read_steel_section(file_path: str, excel_inputs: _Main_Class.ExcelInputs) -> _Main_Class.ExcelInputs:
    """
    Read steel section data from Excel file.
    
    Args:
        file_path: Path to the steel section library Excel file
        excel_inputs: ExcelInputs object to store the data
        
    Returns:
        Updated ExcelInputs object with steel section data
        
    Raises:
        FileNotFoundError: If the file doesn't exist
    """
    try:
        excel_inputs.ELSSteelSection = pd.read_excel(file_path, sheet_name='Steel_Section')
        logger.info(f"Successfully read steel section data from {file_path}")
        return excel_inputs
        
    except FileNotFoundError:
        logger.error(f"Steel section file not found: {file_path}")
        raise
    except Exception as e:
        logger.error(f"Error reading steel section data: {e}")
        raise


def read_input(file_paths: _Main_Class.FilePaths) -> Tuple[_Main_Class.ExcelInputs, _Main_Class.ExcelMaster]:
    """
    Read all input data from the specified file paths.
    
    Args:
        file_paths: FilePaths object containing paths to all input files
        
    Returns:
        Tuple containing (ExcelInputs, ExcelMaster) objects with all loaded data
    """
    excel_inputs = _Main_Class.ExcelInputs()
    excel_master = _Main_Class.ExcelMaster()

    # Read geometry data if file exists
    if os.path.exists(file_paths.ExcelGeometry):
        try:
            excel_inputs = read_input_geometry(file_paths.ExcelGeometry, excel_inputs)
        except Exception as e:
            logger.error(f"Failed to read geometry data: {e}")
    else:
        logger.warning(f"Geometry file not found: {file_paths.ExcelGeometry}")

    # Read property data if file exists
    if os.path.exists(file_paths.ExcelProperty):
        try:
            excel_inputs = read_input_property(file_paths.ExcelProperty, excel_inputs)
        except Exception as e:
            logger.error(f"Failed to read property data: {e}")
    else:
        logger.warning(f"Property file not found: {file_paths.ExcelProperty}")

    # Read geology data if file exists
    if os.path.exists(file_paths.ExcelGeology):
        try:
            excel_inputs = read_input_geology(file_paths.ExcelGeology, excel_inputs)
        except Exception as e:
            logger.error(f"Failed to read geology data: {e}")
    else:
        logger.warning(f"Geology file not found: {file_paths.ExcelGeology}")

    # Read PLAXIS data if file exists
    if os.path.exists(file_paths.ExcelPlaxis):
        try:
            excel_master = read_input_plaxis(file_paths.ExcelPlaxis, excel_master)
        except Exception as e:
            logger.error(f"Failed to read PLAXIS data: {e}")
    else:
        logger.warning(f"PLAXIS file not found: {file_paths.ExcelPlaxis}")

    # Read steel section data
    try:
        excel_inputs = read_steel_section(file_paths.ELSSteelSection, excel_inputs)
    except Exception as e:
        logger.error(f"Failed to read steel section data: {e}")

    logger.info("Input data reading complete")
    return excel_inputs, excel_master
