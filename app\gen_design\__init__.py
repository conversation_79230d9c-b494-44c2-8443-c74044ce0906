"""
PLAXIS Generative Design Package

This package provides functionality for generative design iterations with PLAXIS models.
It includes anchor configuration, excavation level management, and configuration details.

The package is organized into the following subpackages:
- anchors: Anchor geometry and parameter management
- excavation: Excavation level configuration
- config: Configuration management and details
- common: Shared utilities and components
"""

# Import main functions for backward compatibility
from .main_window import autorun_plaxis
from .run import *

# Import configuration functions for backward compatibility
try:
    from .config.details import (
        show_configuration_details,
        update_anchor_geometry_config,
        update_anchor_parameters_config,
        update_excavation_level_config
    )
except ImportError:
    pass

# Import anchor functions for backward compatibility
try:
    from .anchors.definitions import edit_unified_anchor
except ImportError:
    pass

# Import excavation functions for backward compatibility
try:
    from .excavation.definitions import edit_excavation_level
except ImportError:
    pass

__all__ = [
    'autorun_plaxis',
    'edit_unified_anchor',
    'edit_excavation_level',
    'show_configuration_details',
    'update_anchor_geometry_config',
    'update_anchor_parameters_config',
    'update_excavation_level_config'
]