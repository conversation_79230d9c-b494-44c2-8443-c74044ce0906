"""
Shared UI Components for Generative Design Module

This module contains reusable UI component builders and utilities
to reduce code duplication across the generative design modules.
"""

import tkinter as tk
from tkinter import ttk
from typing import Callable, Optional, Dict, Any, List
from .ui_constants import *


# =============================================================================
# BUTTON CREATION UTILITIES
# =============================================================================

def create_styled_button(parent_frame: tk.Widget, text: str, command: Callable, 
                        width: int, height: int, bg_color: str, 
                        fg_color: str = "white", font: tuple = FONT_BOLD, 
                        pack_side: str = tk.LEFT, padx: int = STANDARD_PADDING,
                        pady: int = STANDARD_PADDING, **kwargs) -> tk.Button:
    """
    Create a consistently styled button with Apple-inspired design.
    
    Args:
        parent_frame: Parent widget to contain the button
        text: Button text
        command: Command to execute when clicked
        width: Button width
        height: Button height  
        bg_color: Background color
        fg_color: Text color
        font: Font tuple
        pack_side: Pack side (tk.LEFT, tk.RIGHT, etc.)
        padx: Horizontal padding
        pady: Vertical padding
        **kwargs: Additional button options
        
    Returns:
        Created button widget
    """
    button_style = DEFAULT_BUTTON_STYLE.copy()
    button_style.update(kwargs)
    
    # Remove font from button_style to avoid duplicate parameter error
    button_style.pop('font', None)
    
    button = tk.Button(
        parent_frame,
        text=text,
        command=command,
        width=width,
        height=height,
        bg=bg_color,
        fg=fg_color,
        font=font,
        **button_style
    )
    button.pack(side=pack_side, padx=padx, pady=pady)
    return button


def create_primary_button(parent_frame: tk.Widget, text: str, command: Callable,
                         width: int = 15, height: int = 2, **kwargs) -> tk.Button:
    """Create a primary action button with standard styling."""
    return create_styled_button(
        parent_frame, text, command, width, height, 
        ACCENT_COLOR, "white", **kwargs
    )


def create_success_button(parent_frame: tk.Widget, text: str, command: Callable,
                         width: int = 15, height: int = 2, **kwargs) -> tk.Button:
    """Create a success/save button with green styling."""
    return create_styled_button(
        parent_frame, text, command, width, height,
        SUCCESS_COLOR, "white", **kwargs
    )


def create_danger_button(parent_frame: tk.Widget, text: str, command: Callable,
                        width: int = 15, height: int = 2, **kwargs) -> tk.Button:
    """Create a danger/delete button with red styling."""
    return create_styled_button(
        parent_frame, text, command, width, height,
        ERROR_COLOR, "white", **kwargs
    )


def create_secondary_button(parent_frame: tk.Widget, text: str, command: Callable,
                           width: int = 15, height: int = 2, **kwargs) -> tk.Button:
    """Create a secondary button with gray styling."""
    return create_styled_button(
        parent_frame, text, command, width, height,
        DISABLED_COLOR, "#333333", **kwargs
    )


def create_link_button(parent_frame: tk.Widget, text: str, command: Callable,
                      width: int = 15, height: int = 1, **kwargs) -> tk.Button:
    """Create a link-style button with minimal styling for low-profile actions."""
    link_kwargs = {
        'relief': 'flat',
        'borderwidth': 0,
        'highlightthickness': 0,
        'activebackground': BG_COLOR,
        'activeforeground': '#0066cc',
        'cursor': 'hand2'
    }
    link_kwargs.update(kwargs)
    
    return create_styled_button(
        parent_frame, text, command, width, height,
        BG_COLOR, "#0066cc", font=FONT_REGULAR, **link_kwargs
    )


# =============================================================================
# FRAME CREATION UTILITIES
# =============================================================================

def create_action_buttons_frame(parent_window: tk.Widget, 
                               buttons_config: List[Dict[str, Any]]) -> tk.Frame:
    """
    Create a frame with multiple action buttons.
    
    Args:
        parent_window: Parent widget
        buttons_config: List of button configuration dictionaries
                       Each dict should have: text, command, style (optional)
        
    Returns:
        Frame containing the buttons
    """
    buttons_frame = tk.Frame(parent_window, bg=BG_COLOR)
    buttons_frame.pack(fill="x", padx=20, pady=15)
    
    for config in buttons_config:
        text = config["text"]
        command = config["command"]
        style = config.get("style", "primary")
        width = config.get("width", 15)
        height = config.get("height", 2)
        pack_side = config.get("pack_side", tk.LEFT)
        
        if style == "primary":
            create_primary_button(buttons_frame, text, command, width, height, pack_side=pack_side)
        elif style == "success":
            create_success_button(buttons_frame, text, command, width, height, pack_side=pack_side)
        elif style == "danger":
            create_danger_button(buttons_frame, text, command, width, height, pack_side=pack_side)
        elif style == "secondary":
            create_secondary_button(buttons_frame, text, command, width, height, pack_side=pack_side)
        elif style == "link":
            create_link_button(buttons_frame, text, command, width, height, pack_side=pack_side)
        else:
            create_primary_button(buttons_frame, text, command, width, height, pack_side=pack_side)
    
    return buttons_frame


def create_labeled_frame(parent: tk.Widget, title: str, **kwargs) -> tk.LabelFrame:
    """Create a consistently styled labeled frame."""
    frame_style = {
        "font": FONT_HEADER,
        "bg": BG_COLOR,
        "fg": "#333333",
        "bd": 2,
        "relief": "solid",
        "padx": SECTION_PADDING,
        "pady": LARGE_PADDING
    }
    frame_style.update(kwargs)
    
    return tk.LabelFrame(parent, text=title, **frame_style)


def create_standard_frame(parent: tk.Widget, **kwargs) -> tk.Frame:
    """Create a standard frame with consistent styling."""
    frame_style = DEFAULT_FRAME_STYLE.copy()
    frame_style.update(kwargs)
    return tk.Frame(parent, **frame_style)


# =============================================================================
# SCROLLABLE CONTENT UTILITIES
# =============================================================================

def create_scrollable_content_area(parent_window: tk.Widget) -> tuple:
    """
    Create a scrollable content area with canvas and scrollbars.
    
    Args:
        parent_window: Parent widget for the scrollable area
        
    Returns:
        Tuple of (main_frame, canvas, scrollable_frame)
    """
    # Main container
    main_frame = create_standard_frame(parent_window)
    main_frame.pack(fill="both", expand=True, padx=20, pady=10)
    
    # Canvas and scrollbar for scrollable content
    canvas = tk.Canvas(main_frame, bg=BG_COLOR, highlightthickness=0)
    scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = create_standard_frame(canvas)
    
    # Configure scrolling
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)
    
    # Pack canvas and scrollbar
    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    
    # Configure grid weight for proper expansion
    scrollable_frame.grid_columnconfigure(0, weight=1)
    
    return main_frame, canvas, scrollable_frame


# =============================================================================
# INPUT FIELD UTILITIES
# =============================================================================

def create_labeled_entry(parent: tk.Widget, label_text: str, 
                        entry_width: int = ENTRY_WIDTH, 
                        label_width: int = LABEL_WIDTH,
                        initial_value: str = "") -> tuple:
    """
    Create a labeled entry field with consistent styling.
    
    Args:
        parent: Parent widget
        label_text: Text for the label
        entry_width: Width of entry field
        label_width: Width of label
        initial_value: Initial value for entry
        
    Returns:
        Tuple of (label_widget, entry_widget, string_var)
    """
    frame = create_standard_frame(parent)
    frame.pack(fill="x", pady=STANDARD_PADDING)
    
    label = tk.Label(frame, text=label_text, width=label_width, **DEFAULT_LABEL_STYLE)
    label.pack(side=tk.LEFT, padx=STANDARD_PADDING)
    
    var = tk.StringVar(value=initial_value)
    entry = tk.Entry(frame, textvariable=var, **DEFAULT_ENTRY_STYLE)
    entry.pack(side=tk.LEFT, padx=STANDARD_PADDING)
    
    return label, entry, var


def create_labeled_combobox(parent: tk.Widget, label_text: str, values: List[str],
                           combo_width: int = COMBO_WIDTH,
                           initial_value: str = None) -> tuple:
    """
    Create a labeled combobox with consistent styling.
    
    Args:
        parent: Parent widget
        label_text: Text for the label
        values: List of values for combobox
        combo_width: Width of combobox
        initial_value: Initial selection
        
    Returns:
        Tuple of (label_widget, combobox_widget, string_var)
    """
    frame = create_standard_frame(parent)
    frame.pack(fill="x", pady=STANDARD_PADDING)
    
    label = tk.Label(frame, text=label_text, **DEFAULT_LABEL_STYLE)
    label.pack(side=tk.LEFT, padx=STANDARD_PADDING)
    
    var = tk.StringVar()
    if initial_value and initial_value in values:
        var.set(initial_value)
    elif values:
        var.set(values[0])
    
    # Apply styling to combobox
    style = ttk.Style()
    style.configure('TCombobox', padding=(5, 7), font=FONT_REGULAR)
    
    combo = ttk.Combobox(frame, textvariable=var, values=values, 
                        width=combo_width, font=FONT_REGULAR, 
                        style='TCombobox', state="readonly")
    combo.pack(side=tk.LEFT, padx=STANDARD_PADDING)
    
    return label, combo, var


# =============================================================================
# STATUS AND INFO UTILITIES
# =============================================================================

def create_status_label(parent: tk.Widget, initial_text: str = "") -> tk.Label:
    """Create a status label for showing operation messages."""
    return tk.Label(
        parent,
        text=initial_text,
        font=FONT_REGULAR,
        bg=BG_COLOR,
        fg=SUCCESS_COLOR
    )


def create_info_label(parent: tk.Widget, text: str, 
                     font: tuple = FONT_REGULAR,
                     fg_color: str = "#333333") -> tk.Label:
    """Create an info label with consistent styling."""
    return tk.Label(
        parent,
        text=text,
        font=font,
        bg=BG_COLOR,
        fg=fg_color
    )


def create_header_label(parent: tk.Widget, text: str) -> tk.Label:
    """Create a header label with large font."""
    return tk.Label(
        parent,
        text=text,
        font=FONT_HEADER,
        bg=BG_COLOR,
        fg="#333333"
    )


def create_section_header(parent: tk.Widget, text: str) -> tk.Label:
    """Create a section header label with medium-large font and accent color."""
    return tk.Label(
        parent,
        text=text,
        font=FONT_BOLD,
        bg=BG_COLOR,
        fg=ACCENT_COLOR
    )


# =============================================================================
# DYNAMIC COMPONENT UTILITIES
# =============================================================================

def create_add_remove_buttons(parent: tk.Widget, add_command: Callable, 
                             remove_command: Callable, 
                             button_width: int = BUTTON_WIDTH) -> tuple:
    """
    Create standard add/remove button pair.
    
    Args:
        parent: Parent widget
        add_command: Command for add button
        remove_command: Command for remove button
        button_width: Width of buttons
        
    Returns:
        Tuple of (add_button, remove_button)
    """
    frame = create_standard_frame(parent)
    frame.pack(fill="x", pady=STANDARD_PADDING)
    
    add_btn = tk.Button(
        frame,
        text="+",
        command=add_command,
        width=button_width,
        bg=ACCENT_COLOR,
        fg="white",
        **DEFAULT_BUTTON_STYLE
    )
    add_btn.pack(side=tk.LEFT, padx=2)
    
    remove_btn = tk.Button(
        frame,
        text="-",
        command=remove_command,
        width=button_width,
        bg=ERROR_COLOR,
        fg="white",
        **DEFAULT_BUTTON_STYLE
    )
    remove_btn.pack(side=tk.LEFT, padx=2)
    
    return add_btn, remove_btn


# =============================================================================
# TABLE/TREEVIEW UTILITIES
# =============================================================================

def configure_treeview_style(table: ttk.Treeview):
    """Apply consistent styling to a Treeview widget."""
    style = ttk.Style()
    
    # Set row height for data rows
    style.configure("Treeview", 
                   rowheight=TABLE_ROW_HEIGHT,  
                   font=FONT_REGULAR,
                   background=BG_COLOR)
    
    # Configure Treeview headings
    style.configure("Treeview.Heading",
                   font=FONT_BOLD,
                   padding=(5, 20),
                   background=TABLE_HEADER_BG,
                   foreground=TABLE_HEADER_FG,
                   relief="flat")

    # Add custom style for selected and highlighted rows
    style.map('Treeview', 
             foreground=[('selected', TABLE_SELECTED_FG)],
             background=[('selected', TABLE_SELECTED_BG), ('', '#ffffff')])
    
    # Configure tag for highlighting rows
    table.tag_configure('highlight', background=HIGHLIGHT_COLOR)


def create_treeview_with_scrollbars(parent: tk.Widget, columns: tuple,
                                   height: int = 160) -> tuple:
    """
    Create a Treeview with scrollbars in a container frame.
    
    Args:
        parent: Parent widget
        columns: Column definitions for the Treeview
        height: Height of the container
        
    Returns:
        Tuple of (container_frame, treeview_widget, v_scrollbar, h_scrollbar)
    """
    # Create container frame with fixed height
    container = tk.Frame(parent, height=height, bg=BG_COLOR)
    container.pack(fill="x", expand=False)
    container.pack_propagate(False)

    # Create scrollbars
    v_scrollbar = ttk.Scrollbar(container)
    v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    h_scrollbar = ttk.Scrollbar(container, orient='horizontal')
    h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

    # Create Treeview widget
    treeview = ttk.Treeview(container, columns=columns, show="headings",
                           yscrollcommand=v_scrollbar.set, 
                           xscrollcommand=h_scrollbar.set)

    # Configure scrollbars
    v_scrollbar.config(command=treeview.yview)
    h_scrollbar.config(command=treeview.xview)

    # Pack treeview
    treeview.pack(side=tk.LEFT, fill="x", expand=False)
    
    # Apply standard styling
    configure_treeview_style(treeview)

    return container, treeview, v_scrollbar, h_scrollbar


# =============================================================================
# VALIDATION MESSAGE UTILITIES
# =============================================================================

def show_temporary_status(status_label: tk.Label, message: str, 
                         duration: int = 5000, color: str = SUCCESS_COLOR):
    """
    Show a temporary status message that disappears after duration.
    
    Args:
        status_label: Label widget to show message in
        message: Message to display
        duration: Duration in milliseconds
        color: Text color for the message
    """
    status_label.config(text=message, fg=color)
    
    def clear_status():
        if status_label.winfo_exists():
            status_label.config(text="")
    
    status_label.after(duration, clear_status)


def update_button_state(button: tk.Button, enabled: bool, 
                       enabled_color: str = None, disabled_color: str = DISABLED_COLOR):
    """
    Update button state and appearance.
    
    Args:
        button: Button widget to update
        enabled: Whether button should be enabled
        enabled_color: Color when enabled (uses current if None)
        disabled_color: Color when disabled
    """
    if enabled:
        button.config(state="normal")
        if enabled_color:
            button.config(bg=enabled_color)
    else:
        button.config(state="disabled", bg=disabled_color)

def bind_mouse_wheel_globally(widget, canvas):
    """
    Recursively bind mouse wheel events to a widget and all its children.
    
    Args:
        widget: The widget to bind events to
        canvas: The canvas that should be scrolled
    """
    def on_mouse_wheel(event):
        # Check if the canvas has scrollable content
        if canvas.bbox("all") is not None:
            # Scroll up or down based on wheel direction
            canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
    
    def on_mouse_wheel_linux(event):
        # For Linux systems
        if canvas.bbox("all") is not None:
            if event.num == 4:
                canvas.yview_scroll(-1, "units")
            elif event.num == 5:
                canvas.yview_scroll(1, "units")
    
    # Bind mouse wheel events to this widget
    try:
        # Windows and MacOS
        widget.bind("<MouseWheel>", on_mouse_wheel, add=True)
        # Linux
        widget.bind("<Button-4>", on_mouse_wheel_linux, add=True)
        widget.bind("<Button-5>", on_mouse_wheel_linux, add=True)
    except tk.TclError:
        # Some widgets don't support binding
        pass
    
    # Recursively bind to all children
    try:
        for child in widget.winfo_children():
            bind_mouse_wheel_globally(child, canvas)
    except tk.TclError:
        # Some widgets don't have children
        pass
