import logging
from tkinter import messagebox
from typing import Dict, Optional, Any

import tkinter as tk

from ..common import STANDARD_COORDINATE_LABELS, FONT_BOLD, BG_COLOR, FONT_REGULAR, ENTRY_WIDTH, \
    safe_float_conversion, FONT_HEADER, create_primary_button, create_danger_button, update_button_state, ACCENT_COLOR

COORDINATE_LABELS = STANDARD_COORDINATE_LABELS


class CoordinateSet:
    """Represents a set of X1, Y1, X2, Y2 coordinates with validation."""

    def __init__(self, frame: tk.Frame, set_number: int, row: int):
        self.frame = frame
        self.set_number = set_number
        self.row = row
        self.entries = {}
        self.create_ui()

    def create_ui(self):
        """Create the coordinate input fields."""
        # Label for this coordinate set
        tk.Label(self.frame, text=f"Set {self.set_number}:", font=FONT_BOLD,
                bg=BG_COLOR).grid(row=self.row, column=0, sticky="w", padx=5, pady=5)

        # Create entry fields for each coordinate
        for i, coord_label in enumerate(COORDINATE_LABELS):
            # Label
            tk.Label(self.frame, text=coord_label, font=FONT_REGULAR,
                    bg=BG_COLOR).grid(row=self.row, column=1 + i*2, sticky="w", padx=2, pady=5)

            # Entry field
            entry = tk.Entry(self.frame, width=ENTRY_WIDTH, font=FONT_REGULAR)
            entry.grid(row=self.row, column=2 + i*2, padx=5, pady=5)

            # Store reference to entry
            coord_key = coord_label.split()[0]  # Extract X1, Y1, X2, Y2
            self.entries[coord_key] = entry

    def get_values(self) -> Dict[str, float]:
        """Get the coordinate values with validation."""
        values = {}
        for coord, entry in self.entries.items():
            value = safe_float_conversion(entry.get().strip(), 0.0)
            if value is None:  # Only raise error for truly invalid data
                raise ValueError(f"Invalid value for {coord} in Set {self.set_number}: {entry.get()}")
            values[coord] = value
        return values

    def set_values(self, values: Dict[str, float]):
        """Set the coordinate values."""
        for coord, entry in self.entries.items():
            if coord in values:
                entry.delete(0, tk.END)
                entry.insert(0, str(values[coord]))

    def clear_values(self):
        """Clear all coordinate values."""
        for entry in self.entries.values():
            entry.delete(0, tk.END)

    def destroy(self):
        """Remove this coordinate set from the UI."""
        # Get all widgets in this row and destroy them
        for widget in self.frame.grid_slaves(row=self.row):
            widget.destroy()


class GTypeCoordinateSet:
    """Represents a coordinate set for G-type anchors (only X1, Y1)."""

    def __init__(self, frame: tk.Frame, set_number: int, row: int):
        self.frame = frame
        self.set_number = set_number
        self.row = row
        self.entries = {}
        self.create_ui()

    def create_ui(self):
        """Create the coordinate input fields for G-type anchors."""
        # Label for this coordinate set
        tk.Label(self.frame, text=f"Set {self.set_number}:", font=FONT_BOLD,
                bg=BG_COLOR).grid(row=self.row, column=0, sticky="w", padx=5, pady=5)

        # Only create X1 and Y1 fields for G-type anchors
        g_type_labels = ["X1 (m)", "Y1 (m)"]
        for i, coord_label in enumerate(g_type_labels):
            # Label
            tk.Label(self.frame, text=coord_label, font=FONT_REGULAR,
                    bg=BG_COLOR).grid(row=self.row, column=1 + i*2, sticky="w", padx=2, pady=5)

            # Entry field
            entry = tk.Entry(self.frame, width=ENTRY_WIDTH, font=FONT_REGULAR)
            entry.grid(row=self.row, column=2 + i*2, padx=5, pady=5)

            # Store reference to entry
            coord_key = coord_label.split()[0]  # Extract X1, Y1
            self.entries[coord_key] = entry

    def get_values(self) -> Dict[str, float]:
        """Get the coordinate values with validation."""
        values = {}
        for coord, entry in self.entries.items():
            value = safe_float_conversion(entry.get().strip(), 0.0)
            if value is None:  # Only raise error for truly invalid data
                raise ValueError(f"Invalid value for {coord} in Set {self.set_number}: {entry.get()}")
            values[coord] = value
        return values

    def set_values(self, values: Dict[str, float]):
        """Set the coordinate values."""
        for coord, entry in self.entries.items():
            if coord in values:
                entry.delete(0, tk.END)
                entry.insert(0, str(values[coord]))

    def clear_values(self):
        """Clear all coordinate values."""
        for entry in self.entries.values():
            entry.delete(0, tk.END)

    def destroy(self):
        """Remove this coordinate set from the UI."""
        # Get all widgets in this row and destroy them
        for widget in self.frame.grid_slaves(row=self.row):
            widget.destroy()


class AnchorSection:
    """Manages multiple coordinate sets for a single anchor (N, F1, F2, G1, G2)."""

    def __init__(self, parent_frame: tk.Frame, anchor_name: str, row: int, ui_components_manager=None):
        self.parent_frame = parent_frame
        self.anchor_name = anchor_name
        self.row = row
        self.coordinate_sets = []
        self.frame = None
        self.add_button = None
        self.remove_button = None
        self.anchor_name_var = None
        self.anchor_name_entry = None
        self.ui_components_manager = ui_components_manager
        self._syncing = False  # Flag to prevent infinite recursion
        self.create_ui()

    def create_ui(self):
        """Create the UI for this anchor section."""
        # Create frame for this anchor
        self.frame = tk.LabelFrame(self.parent_frame,
                                  text=f"Anchor_{self.anchor_name}",
                                  font=FONT_HEADER,
                                  bg=BG_COLOR,
                                  pady=10)
        self.frame.grid(row=self.row, column=0, sticky="ew", padx=10, pady=10)

        # Configure grid weight for proper expansion
        self.frame.grid_columnconfigure(0, weight=1)

        # Add anchor name entry field
        anchor_name_frame = tk.Frame(self.frame, bg=BG_COLOR)
        anchor_name_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=5)

        tk.Label(anchor_name_frame, text="Anchor Name:", font=FONT_BOLD,
                bg=BG_COLOR).pack(side=tk.LEFT, padx=5)

        self.anchor_name_var = tk.StringVar()
        self.anchor_name_entry = tk.Entry(anchor_name_frame, textvariable=self.anchor_name_var,
                                        width=20, font=FONT_REGULAR)
        self.anchor_name_entry.pack(side=tk.LEFT, padx=5)

        # Add initial coordinate set
        self.add_coordinate_set()

        # Create buttons frame
        buttons_frame = tk.Frame(self.frame, bg=BG_COLOR)
        buttons_frame.grid(row=1000, column=0, sticky="ew", padx=5, pady=10)

        # Add set button
        self.add_button = create_primary_button(
            buttons_frame,
            "Add Coordinate Set",
            self.add_coordinate_set,
            width=18, height=1
        )

        # Remove set button
        self.remove_button = create_danger_button(
            buttons_frame,
            "Remove Coordinate Set",
            self.remove_coordinate_set,
            width=18, height=1
        )

        # Update button states
        self.update_button_states()

    def sync_to_paired_anchor(self):
        """Sync this anchor to match the coordinate set count of its paired anchor."""
        if self._syncing or not self.ui_components_manager:
            return

        paired_anchor_name = self.get_paired_anchor_name()
        if not paired_anchor_name:
            return

        # Check if paired anchor exists
        if paired_anchor_name not in self.ui_components_manager.anchor_sections:
            return

        paired_anchor = self.ui_components_manager.anchor_sections[paired_anchor_name]
        target_count = len(paired_anchor.coordinate_sets)
        current_count = len(self.coordinate_sets)

        # Set syncing flag to prevent recursion
        self._syncing = True

        try:
            if target_count > current_count:
                # Add coordinate sets to match paired anchor
                while len(self.coordinate_sets) < target_count:
                    self._add_coordinate_set_internal()
            elif target_count < current_count:
                # Remove coordinate sets to match paired anchor
                while len(self.coordinate_sets) > target_count and len(self.coordinate_sets) > 1:
                    self._remove_coordinate_set_internal()

            # Update button states
            self.update_button_states()

        finally:
            # Clear syncing flag
            self._syncing = False

    def get_paired_anchor_name(self) -> Optional[str]:
        """Get the name of the paired anchor (F1<->F2, G1<->G2)."""
        if self.anchor_name == "F1":
            return "F2"
        elif self.anchor_name == "F2":
            return "F1"
        elif self.anchor_name == "G1":
            return "G2"
        elif self.anchor_name == "G2":
            return "G1"
        return None

    def add_coordinate_set(self):
        """Add a new coordinate set to this anchor."""
        set_number = len(self.coordinate_sets) + 1
        row = len(self.coordinate_sets) + 1  # Start from row 1 (row 0 is for anchor name)

        # Use appropriate coordinate set based on anchor type
        if self.anchor_name.startswith('G'):
            # G-type anchors only need X1, Y1
            coord_set = GTypeCoordinateSet(self.frame, set_number, row)
        else:
            # N-type and F-type anchors need X1, Y1, X2, Y2
            coord_set = CoordinateSet(self.frame, set_number, row)

        self.coordinate_sets.append(coord_set)
        self.update_button_states()

        # Sync with paired anchor if applicable
        self.sync_with_paired_anchor("add")

        logging.info(f"Added coordinate set {set_number} to {self.anchor_name}")

    def _add_coordinate_set_internal(self):
        """Add a new coordinate set without triggering synchronization."""
        set_number = len(self.coordinate_sets) + 1
        row = len(self.coordinate_sets) + 1

        # Use appropriate coordinate set based on anchor type
        if self.anchor_name.startswith('G'):
            coord_set = GTypeCoordinateSet(self.frame, set_number, row)
        else:
            coord_set = CoordinateSet(self.frame, set_number, row)

        self.coordinate_sets.append(coord_set)
        self.update_button_states()

    def remove_coordinate_set(self):
        """Remove the last coordinate set from this anchor."""
        if len(self.coordinate_sets) <= 1:
            messagebox.showwarning("Cannot Remove",
                                 f"At least one coordinate set is required for {self.anchor_name}")
            return

        # Remove the last coordinate set
        coord_set = self.coordinate_sets.pop()
        coord_set.destroy()

        self.update_button_states()

        # Sync with paired anchor if applicable
        self.sync_with_paired_anchor("remove")

        logging.info(f"Removed coordinate set from {self.anchor_name}")

    def _remove_coordinate_set_internal(self):
        """Remove the last coordinate set without triggering synchronization."""
        if len(self.coordinate_sets) <= 1:
            return

        coord_set = self.coordinate_sets.pop()
        coord_set.destroy()
        self.update_button_states()

    def sync_with_paired_anchor(self, action: str):
        """Sync coordinate sets with paired anchor to maintain same number of sets."""
        if self._syncing or not self.ui_components_manager:
            return

        paired_anchor_name = self.get_paired_anchor_name()
        if not paired_anchor_name:
            return

        # Check if paired anchor exists
        if paired_anchor_name not in self.ui_components_manager.anchor_sections:
            return

        paired_anchor = self.ui_components_manager.anchor_sections[paired_anchor_name]

        # Set syncing flag to prevent infinite recursion
        self._syncing = True
        paired_anchor._syncing = True

        try:
            current_sets = len(self.coordinate_sets)
            paired_sets = len(paired_anchor.coordinate_sets)

            if action == "add" and current_sets > paired_sets:
                # Add sets to paired anchor to match current anchor
                while len(paired_anchor.coordinate_sets) < current_sets:
                    paired_anchor._add_coordinate_set_internal()
            elif action == "remove" and current_sets < paired_sets:
                # Remove sets from paired anchor to match current anchor
                while len(paired_anchor.coordinate_sets) > current_sets and len(paired_anchor.coordinate_sets) > 1:
                    paired_anchor._remove_coordinate_set_internal()

            # Update button states for both anchors
            paired_anchor.update_button_states()

        finally:
            # Clear syncing flags
            self._syncing = False
            paired_anchor._syncing = False

    def update_button_states(self):
        """Update the state of add/remove buttons."""
        current_count = len(self.coordinate_sets)

        # Remove button: disabled if only have 1 set
        if self.remove_button:
            update_button_state(self.remove_button, current_count > 1, ACCENT_COLOR)

    def get_all_values(self) -> Dict[str, Any]:
        """Get all coordinate values from this anchor."""
        coordinates = []
        for coord_set in self.coordinate_sets:
            try:
                values = coord_set.get_values()
                coordinates.append(values)
            except ValueError as e:
                raise ValueError(f"Error in {self.anchor_name}: {str(e)}")

        return {
            "anchor_name": self.anchor_name_var.get() if self.anchor_name_var else self.anchor_name,
            "coordinates": coordinates
        }

    def set_anchor_name(self, name: str):
        """Set the anchor name."""
        if self.anchor_name_var:
            self.anchor_name_var.set(name)

    def get_anchor_name(self) -> str:
        """Get the current anchor name."""
        return self.anchor_name_var.get() if self.anchor_name_var else self.anchor_name

    def clear_all_sets(self):
        """Clear all coordinate sets."""
        for coord_set in self.coordinate_sets:
            coord_set.destroy()
        self.coordinate_sets.clear()

    def destroy(self):
        """Remove this entire anchor section."""
        if self.frame:
            self.frame.destroy()
        self.coordinate_sets.clear()


class GeometryUIComponents:
    """Manages UI components for anchor geometry configuration."""

    def __init__(self):
        self.anchor_type_var = None
        self.anchor_sections = {}  # Dictionary: anchor_name -> AnchorSection
        self.content_frame = None
        self.current_anchor_type = None
        self.anchor_management_frame = None
        self.add_anchor_button = None
        self.remove_anchor_button = None
        self.status_label = None
        self.stage_label = None
        self.excel_master = None
        self.selected_stage = None

    def clear_all_anchors(self):
        """Clear all anchor sections."""
        for anchor_section in self.anchor_sections.values():
            anchor_section.destroy()
        self.anchor_sections.clear()

    def add_anchor_section(self, anchor_name: str) -> AnchorSection:
        """Add a new anchor section."""
        if anchor_name in self.anchor_sections:
            logging.warning(f"Anchor section {anchor_name} already exists")
            return self.anchor_sections[anchor_name]

        row = len(self.anchor_sections)
        anchor_section = AnchorSection(self.content_frame, anchor_name, row, self)
        self.anchor_sections[anchor_name] = anchor_section
        return anchor_section

    def remove_anchor_section(self, anchor_name: str):
        """Remove an anchor section."""
        if anchor_name in self.anchor_sections:
            self.anchor_sections[anchor_name].destroy()
            del self.anchor_sections[anchor_name]

            # Reorganize remaining sections
            self._reorganize_anchor_sections()

    def _reorganize_anchor_sections(self):
        """Reorganize anchor sections after removal."""
        for i, (anchor_name, anchor_section) in enumerate(self.anchor_sections.items()):
            anchor_section.row = i
            anchor_section.frame.grid(row=i, column=0, sticky="ew", padx=10, pady=10)

    def get_all_anchor_data(self) -> Dict[str, Dict[str, Any]]:
        """Get all anchor data from all sections."""
        all_data = {}
        for anchor_name, anchor_section in self.anchor_sections.items():
            all_data[anchor_name] = anchor_section.get_all_values()
        return all_data

    def update_anchor_management_buttons(self):
        """Update the state of anchor management buttons (for F2/G2)."""
        anchor_type = self.anchor_type_var.get() if self.anchor_type_var else None

        if not self.add_anchor_button or not self.remove_anchor_button:
            return

        if anchor_type == "F":
            # For type F: can add F2 if not present
            self.anchor_management_frame.pack(fill="x", padx=20, pady=10)
            if "F2" not in self.anchor_sections:
                self.add_anchor_button.config(state="normal", text="Add F2 Anchor", bg=ACCENT_COLOR)
                self.remove_anchor_button.config(state="disabled", bg="#CCCCCC")
            else:
                self.add_anchor_button.config(state="disabled", bg="#CCCCCC")
                self.remove_anchor_button.config(state="normal", text="Remove F2 Anchor", bg="#FF6B6B")

        elif anchor_type == "G":
            # For type G: can add G2 if not present
            self.anchor_management_frame.pack(fill="x", padx=20, pady=10)
            if "G2" not in self.anchor_sections:
                self.add_anchor_button.config(state="normal", text="Add G2 Anchor", bg=ACCENT_COLOR)
                self.remove_anchor_button.config(state="disabled", bg="#CCCCCC")
            else:
                self.add_anchor_button.config(state="disabled", bg="#CCCCCC")
                self.remove_anchor_button.config(state="normal", text="Remove G2 Anchor", bg="#FF6B6B")

        else:  # Type N
            # For type N: no additional anchors allowed - hide the entire frame
            self.anchor_management_frame.pack_forget()
