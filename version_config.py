"""
Version Configuration - Single Source of Truth

This module serves as the central configuration for all version information 
in the PLAXIS Automation application. All version strings throughout the 
codebase should reference this module instead of hardcoding version numbers.

Usage:
    from version_config import VERSION, APP_VERSION, APP_VERSION_V_PREFIX

Author: Refactored for single-source-of-truth versioning
"""

# Core version components
VERSION_MAJOR = 1
VERSION_MINOR = 8
VERSION_PATCH = 1

# Main version string (semantic versioning format)
VERSION = f"{VERSION_MAJOR}.{VERSION_MINOR}.{VERSION_PATCH}"

# App version formats for different use cases
APP_VERSION = VERSION  # Standard format: "1.8.1"
APP_VERSION_V_PREFIX = f"V{VERSION}"  # With V prefix: "V1.8.1"

# Package version for __init__.py (uses semantic versioning)
PACKAGE_VERSION = VERSION

# Build version for build tools
BUILD_VERSION = VERSION

# Application metadata
APP_NAME = "PLAXIS Automation Tool"
APP_TITLE = "PLAXIS Automation Tool"
COPYRIGHT = "Copyright © 2023 Alex Sze"

# Version history tracking
VERSION_HISTORY = [
    "1.6.0",
    "1.7.0", 
    "1.8.0",
    "1.8.1"  # Current version
]

# Development information
DEVELOPMENT_BRANCH = f"developing_v{VERSION}"
RELEASE_BRANCH = f"release_v{VERSION}"

def get_version_info():
    """
    Get comprehensive version information.
    
    Returns:
        dict: Dictionary containing all version-related information
    """
    return {
        "version": VERSION,
        "app_version": APP_VERSION,
        "app_version_v_prefix": APP_VERSION_V_PREFIX,
        "package_version": PACKAGE_VERSION,
        "build_version": BUILD_VERSION,
        "major": VERSION_MAJOR,
        "minor": VERSION_MINOR,
        "patch": VERSION_PATCH,
        "app_name": APP_NAME,
        "copyright": COPYRIGHT,
        "development_branch": DEVELOPMENT_BRANCH,
        "release_branch": RELEASE_BRANCH
    }

def get_version_string(format_type="standard"):
    """
    Get version string in specified format.
    
    Args:
        format_type (str): Format type - "standard", "v_prefix", "semantic"
        
    Returns:
        str: Formatted version string
    """
    if format_type == "v_prefix":
        return APP_VERSION_V_PREFIX
    elif format_type == "semantic":
        return VERSION
    elif format_type == "standard":
        return APP_VERSION
    else:
        raise ValueError(f"Unknown format type: {format_type}")

# For backward compatibility - these can be imported directly
__version__ = VERSION
__app_version__ = APP_VERSION