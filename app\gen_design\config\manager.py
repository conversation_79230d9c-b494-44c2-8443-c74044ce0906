"""
Configuration Manager Module

This module provides centralized configuration management functionality for
the generative design system, including configuration status tracking and
data storage coordination.
"""

import logging
from typing import Dict, Any, Optional

# Import configuration utilities from common
from ..common.config_utils import (
    initialize_comprehensive_config,
    ensure_comprehensive_config_exists,
    get_configuration_status,
    is_configuration_complete,
    reset_configuration
)


class ConfigurationManager:
    """
    Central manager for configuration data across all generative design components.
    """
    
    def __init__(self, app_instance):
        self.app_instance = app_instance
        self.config_data = {
            'anchor_geometry': {'configured': False, 'data': {}, 'combinations': 1},
            'anchor_parameters': {'configured': False, 'data': {}, 'combinations': 1},
            'excavation_level': {'configured': False, 'data': {}, 'combinations': 1}
        }
    
    def update_geometry_config(self, geometry_data: Dict[str, Any], stage: Optional[str] = None):
        """Update anchor geometry configuration."""
        try:
            self.config_data['anchor_geometry'].update({
                'configured': True,
                'data': geometry_data,
                'stage': stage,
                'combinations': self._calculate_geometry_combinations(geometry_data)
            })
            logging.info(f"Updated anchor geometry config for stage {stage}")
        except Exception as e:
            logging.error(f"Error updating geometry config: {e}")
    
    def update_parameters_config(self, parameters_data: Dict[str, Any], stage: Optional[str] = None):
        """Update anchor parameters configuration."""
        try:
            self.config_data['anchor_parameters'].update({
                'configured': True,
                'data': parameters_data,
                'stage': stage,
                'combinations': self._calculate_parameter_combinations(parameters_data)
            })
            logging.info(f"Updated anchor parameters config for stage {stage}")
        except Exception as e:
            logging.error(f"Error updating parameters config: {e}")
    
    def update_excavation_config(self, excavation_data: Dict[str, Any], stage: Optional[str] = None):
        """Update excavation level configuration."""
        try:
            self.config_data['excavation_level'].update({
                'configured': True,
                'data': excavation_data,
                'stage': stage,
                'combinations': self._calculate_excavation_combinations(excavation_data)
            })
            logging.info(f"Updated excavation level config for stage {stage}")
        except Exception as e:
            logging.error(f"Error updating excavation config: {e}")
    
    def get_total_combinations(self) -> int:
        """Calculate total combinations across all configurations."""
        return (self.config_data['anchor_geometry']['combinations'] * 
                self.config_data['anchor_parameters']['combinations'] * 
                self.config_data['excavation_level']['combinations'])
    
    def get_configuration_summary(self) -> Dict[str, Any]:
        """Get a summary of all configuration states."""
        return {
            'anchor_geometry': {
                'configured': self.config_data['anchor_geometry']['configured'],
                'stage': self.config_data['anchor_geometry'].get('stage'),
                'combinations': self.config_data['anchor_geometry']['combinations']
            },
            'anchor_parameters': {
                'configured': self.config_data['anchor_parameters']['configured'],
                'stage': self.config_data['anchor_parameters'].get('stage'),
                'combinations': self.config_data['anchor_parameters']['combinations']
            },
            'excavation_level': {
                'configured': self.config_data['excavation_level']['configured'],
                'stage': self.config_data['excavation_level'].get('stage'),
                'combinations': self.config_data['excavation_level']['combinations']
            },
            'total_combinations': self.get_total_combinations()
        }
    
    def is_ready_for_generation(self) -> bool:
        """Check if all required configurations are complete."""
        return (self.config_data['anchor_geometry']['configured'] and
                self.config_data['anchor_parameters']['configured'] and
                self.config_data['excavation_level']['configured'])
    
    def reset_all_configurations(self):
        """Reset all configuration data."""
        for config_type in self.config_data:
            self.config_data[config_type] = {'configured': False, 'data': {}, 'combinations': 1}
        logging.info("Reset all configuration data")
    
    def _calculate_geometry_combinations(self, geometry_data: Dict[str, Any]) -> int:
        """Calculate number of combinations from geometry data."""
        try:
            if not geometry_data or 'anchors' not in geometry_data:
                return 1
            
            total_combinations = 1
            for anchor_name, anchor_data in geometry_data['anchors'].items():
                if 'coordinates' in anchor_data:
                    coord_count = len(anchor_data['coordinates'])
                    if coord_count > 0:
                        total_combinations *= coord_count
            
            return max(total_combinations, 1)
        except Exception as e:
            logging.error(f"Error calculating geometry combinations: {e}")
            return 1
    
    def _calculate_parameter_combinations(self, parameters_data: Dict[str, Any]) -> int:
        """Calculate number of combinations from parameter data."""
        try:
            if not parameters_data:
                return 1
            
            total_combinations = 1
            for anchor_name, anchor_params in parameters_data.items():
                if isinstance(anchor_params, dict):
                    # Count combinations from sections
                    if 'sections' in anchor_params:
                        sections_count = len(anchor_params['sections'])
                        if sections_count > 0:
                            total_combinations *= sections_count
                    
                    # Count combinations from spacing
                    if 'spacing' in anchor_params:
                        spacing_count = len(anchor_params['spacing'])
                        if spacing_count > 0:
                            total_combinations *= spacing_count
            
            return max(total_combinations, 1)
        except Exception as e:
            logging.error(f"Error calculating parameter combinations: {e}")
            return 1
    
    def _calculate_excavation_combinations(self, excavation_data: Dict[str, Any]) -> int:
        """Calculate number of combinations from excavation data."""
        try:
            if not excavation_data or 'stage_data' not in excavation_data:
                return 1
            
            stage_data = excavation_data['stage_data']
            total_combinations = 1
            
            for stage_name, polyline_sets in stage_data.items():
                if isinstance(polyline_sets, list) and polyline_sets:
                    # Each polyline set represents a potential combination
                    total_combinations *= len(polyline_sets)
            
            return max(total_combinations, 1)
        except Exception as e:
            logging.error(f"Error calculating excavation combinations: {e}")
            return 1


# Export the functions from config_utils for backward compatibility
__all__ = [
    'ConfigurationManager',
    'initialize_comprehensive_config',
    'ensure_comprehensive_config_exists',
    'get_configuration_status',
    'is_configuration_complete',
    'reset_configuration'
]
