"""
Configuration Utilities Module

This module provides utilities for managing configuration data across
the generative design components. It centralizes configuration state
management and update operations.
"""

import logging
from typing import Dict, Any, Optional


def initialize_comprehensive_config() -> Dict[str, Any]:
    """
    Initialize the comprehensive configuration structure with default values.
    
    Returns:
        Dict containing the initialized configuration structure
    """
    config = {
        'anchor_geometry': {
            'configured': False,
            'anchors': {},
            'anchor_type': None,
            'stage': None
        },
        'anchor_parameters': {
            'configured': False,
            'parameters': {},
            'stage': None
        },
        'excavation_level': {
            'configured': False,
            'stage_data': {},
            'stage': None
        }
    }
    
    logging.info("Comprehensive configuration initialized")
    return config


def ensure_comprehensive_config_exists(app_instance) -> None:
    """
    Ensure that the app instance has a comprehensive_config attribute.
    If it doesn't exist, initialize it with default values.
    
    Args:
        app_instance: The application instance to check and initialize
    """
    if not hasattr(app_instance, 'comprehensive_config'):
        app_instance.comprehensive_config = initialize_comprehensive_config()
        logging.info("Created new comprehensive_config for app instance")


def update_anchor_geometry_config(app_instance, geometry_data: Dict[str, Any], stage: Optional[str] = None):
    """Update anchor geometry configuration in the app instance."""
    try:
        ensure_comprehensive_config_exists(app_instance)
        
        app_instance.comprehensive_config['anchor_geometry'].update({
            'configured': True,
            'anchors': geometry_data.get('anchors', {}),
            'anchor_type': geometry_data.get('anchor_type'),
            'stage': stage
        })
        
        logging.info(f"Updated anchor geometry config for stage {stage}")
        
    except Exception as e:
        logging.error(f"Error updating anchor geometry config: {e}")


def update_anchor_parameters_config(app_instance, parameters_data: Dict[str, Any], stage: Optional[str] = None):
    """Update anchor parameters configuration in the app instance."""
    try:
        ensure_comprehensive_config_exists(app_instance)
        
        # The parameters_data comes directly from the anchor sections
        # It's in the format: {anchor_name: parameter_data}
        # We need to wrap it in a 'parameters' key for the display functions
        app_instance.comprehensive_config['anchor_parameters'].update({
            'configured': True,
            'parameters': parameters_data,  # Store directly for backward compatibility
            'stage': stage
        })
        
        logging.info(f"Updated anchor parameters config for stage {stage}")
        logging.info(f"Parameters data keys: {list(parameters_data.keys())}")
        
    except Exception as e:
        logging.error(f"Error updating anchor parameters config: {e}")
        logging.exception("Detailed traceback:")


def update_excavation_level_config(app_instance, excavation_data: Dict[str, Any], stage: Optional[str] = None):
    """Update excavation level configuration in the app instance."""
    try:
        ensure_comprehensive_config_exists(app_instance)
        
        app_instance.comprehensive_config['excavation_level'].update({
            'configured': True,
            'stage_data': excavation_data.get('stage_data', {}),
            'stage': stage
        })
        
        logging.info(f"Updated excavation level config for stage {stage}")
        
    except Exception as e:
        logging.error(f"Error updating excavation level config: {e}")


def get_configuration_status(app_instance) -> Dict[str, bool]:
    """
    Get the configuration status for all components.
    
    Args:
        app_instance: The application instance to check
        
    Returns:
        Dict mapping component names to their configuration status
    """
    if not hasattr(app_instance, 'comprehensive_config'):
        return {
            'anchor_geometry': False,
            'anchor_parameters': False,
            'excavation_level': False
        }
    
    return {
        'anchor_geometry': app_instance.comprehensive_config.get('anchor_geometry', {}).get('configured', False),
        'anchor_parameters': app_instance.comprehensive_config.get('anchor_parameters', {}).get('configured', False),
        'excavation_level': app_instance.comprehensive_config.get('excavation_level', {}).get('configured', False)
    }


def is_configuration_complete(app_instance) -> bool:
    """
    Check if all required configurations are complete.
    
    Args:
        app_instance: The application instance to check
        
    Returns:
        True if all configurations are complete, False otherwise
    """
    status = get_configuration_status(app_instance)
    return all(status.values())


def reset_configuration(app_instance, component: Optional[str] = None):
    """
    Reset configuration for a specific component or all components.
    
    Args:
        app_instance: The application instance
        component: Specific component to reset ('anchor_geometry', 'anchor_parameters', 
                  'excavation_level'), or None to reset all
    """
    try:
        ensure_comprehensive_config_exists(app_instance)
        
        if component is None:
            # Reset all configurations
            app_instance.comprehensive_config = initialize_comprehensive_config()
            logging.info("Reset all configurations")
        elif component in app_instance.comprehensive_config:
            # Reset specific component
            initial_config = initialize_comprehensive_config()
            app_instance.comprehensive_config[component] = initial_config[component]
            logging.info(f"Reset {component} configuration")
        else:
            logging.warning(f"Unknown component: {component}")
            
    except Exception as e:
        logging.error(f"Error resetting configuration: {e}")