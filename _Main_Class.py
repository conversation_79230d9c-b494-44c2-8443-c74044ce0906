"""
Core data structures for the Plaxis Automation tool.

This module defines the primary data models used throughout the application for
storing file paths, excel data, and geometric information.
"""
from typing import Optional
import pandas as pd
from dataclasses import dataclass


@dataclass
class FilePaths:
    """Stores file paths for various input and output files used by the application."""
    
    ExcelProperty: str = ''
    ExcelGeometry: str = ''
    ExcelGeology: str = ''
    ExcelPlaxis: str = ''
    AutoRunLog: str = ''
    ELSSteelSection: str = 'Library_Steel/ELS_Steel_Section.xlsx'
    
    def validate_paths(self) -> bool:
        """Validates that all required file paths are set.
        
        Returns:
            bool: True if all required paths are set, False otherwise.
        """
        required_files = [self.ExcelProperty, self.ExcelGeometry, 
                         self.ExcelGeology, self.ExcelPlaxis]
        return all(required_files)


class ExcelInputs:
    """Manages dataframes loaded from input Excel files."""
    
    def __init__(self) -> None:
        """Initialize empty dataframes for all input data."""
        self.SoilProperties: pd.DataFrame = pd.DataFrame()
        self.Borehole: pd.DataFrame = pd.DataFrame()
        self.ELSSteelSection: pd.DataFrame = pd.DataFrame()
        self.PlateProp: pd.DataFrame = pd.DataFrame()
        self.Summary: pd.DataFrame = pd.DataFrame()
        self.Section: pd.DataFrame = pd.DataFrame()
        self.Anchor: pd.DataFrame = pd.DataFrame()
        self.Plate: pd.DataFrame = pd.DataFrame()
        self.VBH: pd.DataFrame = pd.DataFrame()
        self.GeoSection: pd.DataFrame = pd.DataFrame()
    
    def is_loaded(self, df_name: str) -> bool:
        """Check if a specific dataframe has been loaded with data.
        
        Args:
            df_name: Name of the dataframe attribute to check
            
        Returns:
            bool: True if the dataframe is not empty, False otherwise
        """
        if hasattr(self, df_name):
            df = getattr(self, df_name)
            return not df.empty
        return False


class ExcelMaster:
    """Master collection of all processed Excel data used in the application."""
    
    def __init__(self) -> None:
        """Initialize empty dataframes for all master data."""
        # Site geometry and conditions
        self.Section: pd.DataFrame = pd.DataFrame()
        self.GroundProfile: pd.DataFrame = pd.DataFrame()
        self.SoilProperties: pd.DataFrame = pd.DataFrame()
        
        # Structural elements
        self.PlateLoc: pd.DataFrame = pd.DataFrame()
        self.AnchorLoc: pd.DataFrame = pd.DataFrame()
        
        # Water levels and loading conditions
        self.EL: pd.DataFrame = pd.DataFrame()
        self.IWL: pd.DataFrame = pd.DataFrame()
        self.LineLoad: pd.DataFrame = pd.DataFrame()
        self.PointLoad: pd.DataFrame = pd.DataFrame()
        
        # PLAXIS specific data
        self.PLAXISSoil: pd.DataFrame = pd.DataFrame()
        self.StructurePolygon: pd.DataFrame = pd.DataFrame()
        self.StructureGeometry: pd.DataFrame = pd.DataFrame()
        self.StructureMaterial: pd.DataFrame = pd.DataFrame()

        # Configuration and logging
        self.InputParameter: pd.DataFrame = pd.DataFrame()
        self.AutoRunLog: pd.DataFrame = pd.DataFrame()


@dataclass
class CrossSection:
    """Represents a cross-section in the model with coordinates and deflection limit."""
    
    x1: float = 0.0
    y1: float = 0.0
    x2: float = 0.0
    y2: float = 0.0
    uy_limit: float = 0.0
    
    @property
    def length(self) -> float:
        """Calculate the length of the cross-section."""
        return ((self.x2 - self.x1)**2 + (self.y2 - self.y1)**2)**0.5


@dataclass
class Anchor:
    """Represents an anchor in the model with geometry and material properties."""
    
    layer: str = ''
    x1: float = 0.0
    y1: float = 0.0
    x2: float = 0.0
    y2: float = 0.0
    alpha: float = 0.0  # Angle in degrees
    theta: float = 0.0  # Angle in degrees
    spacing: float = 0.0  # Spacing in meters
    section: str = ''  # Section profile
    unit: str = ''  # Unit type
    prestress: float = 0.0  # Prestress in kN/m
    
    @property
    def length(self) -> float:
        """Calculate the length of the anchor."""
        return ((self.x2 - self.x1)**2 + (self.y2 - self.y1)**2)**0.5
