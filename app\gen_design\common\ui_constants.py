"""
Shared UI Constants for Generative Design Module

This module contains all UI styling constants used across the generative design
modules to ensure consistent appearance and easy maintenance.
"""

# =============================================================================
# FONT CONSTANTS
# =============================================================================

# Standard fonts for consistent UI appearance
FONT_REGULAR = ("Helvetica", 10)
FONT_BOLD = ("Helvetica", 10, "bold")
FONT_HEADER = ("Helvetica", 12, "bold")

# Alternative fonts for specific components (keep for backward compatibility)
HEADER_FONT = ("Arial", 9, "bold")  # Used in anchor parameters
BUTTON_FONT = ("Arial", 8)          # Used for small buttons

# =============================================================================
# COLOR CONSTANTS
# =============================================================================

# Apple-inspired color scheme
BG_COLOR = "#f5f5f7"        # Light gray background
ACCENT_COLOR = "#0071e3"     # Blue accent color  
HIGHLIGHT_COLOR = "#f2f2f7"  # Light highlight color

# Semantic colors for different states
SUCCESS_COLOR = "#4CAF50"    # Green for success messages
ERROR_COLOR = "#FF6B6B"      # Red for errors and remove buttons
WARNING_COLOR = "#FF9800"    # Orange for warnings
DISABLED_COLOR = "#CCCCCC"   # Gray for disabled elements

# Table and component colors
TABLE_HEADER_BG = "#e6e6e6"
TABLE_HEADER_FG = "#333333"
TABLE_SELECTED_BG = ACCENT_COLOR
TABLE_SELECTED_FG = "#ffffff"

# =============================================================================
# SIZE CONSTANTS
# =============================================================================

# Standard widget sizes
ENTRY_WIDTH = 12
BUTTON_WIDTH = 3
COMBO_WIDTH = 15
LABEL_WIDTH = 15

# Window and layout sizes
TABLE_ROW_HEIGHT = 30
STANDARD_PADDING = 5
LARGE_PADDING = 10
SECTION_PADDING = 15

# =============================================================================
# WINDOW GEOMETRY CONSTANTS
# =============================================================================

# Default window sizes for different modules
DEFAULT_WINDOW_SIZES = {
    "geometry": "1200x700",
    "parameters": "600x700", 
    "excavation": "900x700",
    "main_config": "1000x500",
    "configuration_details": "900x700"
}

# Parameter window sizes by anchor type
PARAM_WINDOW_GEOMETRY = {
    "N": "1000x800",
    "F": "1000x800", 
    "G": "1400x800"
}

# =============================================================================
# COMMON ICON AND IMAGE CONSTANTS
# =============================================================================

# Application icon path (used across all windows)
APP_ICON_PATH = 'AIS.ico'

# =============================================================================
# UI COMPONENT DEFAULTS
# =============================================================================

# Default button styling
DEFAULT_BUTTON_STYLE = {
    "relief": "flat",
    "borderwidth": 0,
    "cursor": "hand2",
    "font": FONT_BOLD
}

# Default entry styling  
DEFAULT_ENTRY_STYLE = {
    "width": ENTRY_WIDTH,
    "font": FONT_REGULAR,
    "justify": "center"
}

# Default label styling
DEFAULT_LABEL_STYLE = {
    "font": FONT_REGULAR,
    "bg": BG_COLOR
}

# Frame styling
DEFAULT_FRAME_STYLE = {
    "bg": BG_COLOR
}

# =============================================================================
# ANCHOR TYPE CONSTANTS
# =============================================================================

# Available anchor types
ANCHOR_TYPES = ["N", "F", "G"]

# Default unit options for anchor parameters
UNIT_OPTIONS = ["1", "2", "3", "4", "5", "6", "7", "8"]

# Coordinate labels for different types
STANDARD_COORDINATE_LABELS = ["X1 (m)", "Y1 (m)", "X2 (m)", "Y2 (m)"]
EXCAVATION_COORDINATE_LABELS = ["X:", "Y:"]

# =============================================================================
# VALIDATION CONSTANTS
# =============================================================================

# Maximum limits for UI components
MAX_SETS_PER_STAGE = 10
MIN_COORDINATE_SETS = 1

# Default values for various fields
DEFAULT_COORDINATE_VALUE = 0.0
DEFAULT_SETTLEMENT_VALUES = ["0", "0", "0", "0"]
SETTLEMENT_COLUMNS = ["Line_x1", "Line_y1", "Line_x2", "Line_y2"]
SETTLEMENT_DEFAULT_VALUES = ["0", "0", "0", "0"]