#!/usr/bin/env python3
"""
PLAXIS Automation Tool - Main Entry Point

This module serves as the entry point for the PLAXIS Automation Tool application.
It configures logging, initializes the main application window, and handles
top-level exception management.
"""

# Standard library imports
import os
import sys
import logging
import warnings
import traceback
from datetime import datetime

# Third-party library imports
import tkinter as tk
import pandas as pd

# Configure constants
APP_NAME = "PLAXIS Automation Tool"
from version_config import APP_VERSION
LOG_DIR = os.path.join(os.path.expanduser("~"), ".plaxis_automation", "logs")


def setup_logging():
    """Configure application logging to both console and file."""
    # Create log directory if it doesn't exist
    os.makedirs(LOG_DIR, exist_ok=True)

    # Generate log filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(LOG_DIR, f"plaxis_automation_{timestamp}.log")

    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # Console handler with basic formatting
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_format = logging.Formatter('%(levelname)s: %(message)s')
    console_handler.setFormatter(console_format)

    # File handler with detailed formatting
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.DEBUG)  # More detailed in log file
    file_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_format)

    # Add both handlers to logger
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)

    return log_file


def setup_pandas_and_warnings():
    """Configure pandas and warnings settings."""
    # Suppress common warnings
    warnings.simplefilter(action='ignore', category=UserWarning)
    warnings.simplefilter(action='ignore', category=FutureWarning)
    warnings.simplefilter(action='ignore', category=DeprecationWarning)

    # Configure pandas
    pd.options.mode.chained_assignment = None  # default='warn'


def check_system_requirements():
    """Verify system meets minimum requirements for the application."""
    try:
        # Check Python version
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 6):
            logging.error(f"Python 3.6+ is required. Current version: {sys.version}")
            return False

        # Check if required directories are writable
        test_dirs = [LOG_DIR, os.path.join(os.path.expanduser("~"), ".plaxis_automation")]
        for directory in test_dirs:
            os.makedirs(directory, exist_ok=True)
            test_file = os.path.join(directory, ".write_test")
            try:
                with open(test_file, 'w') as f:
                    f.write("test")
                os.remove(test_file)
            except (PermissionError, OSError) as e:
                logging.error(f"Cannot write to directory {directory}: {e}")
                return False

        return True

    except Exception as e:
        logging.error(f"Error checking system requirements: {e}")
        return False


def show_error_dialog(error_msg):
    """Display error in a simple Tkinter dialog when GUI is not yet available."""
    try:
        root = tk.Tk()
        root.withdraw()  # Hide the main window

        dialog = tk.Toplevel(root)
        dialog.title(f"{APP_NAME} - Error")
        dialog.geometry("500x300")
        dialog.resizable(True, True)

        # Add error information
        tk.Label(dialog, text="An error occurred during startup:",
                font=("Arial", 12, "bold")).pack(pady=(20, 10))

        error_frame = tk.Frame(dialog)
        error_frame.pack(fill="both", expand=True, padx=20, pady=10)

        error_text = tk.Text(error_frame, wrap="word", height=10)
        error_text.pack(fill="both", expand=True)
        error_text.insert("1.0", error_msg)
        error_text.config(state="disabled")

        # Add OK button
        tk.Button(dialog, text="OK", command=lambda: sys.exit(1),
                 width=10).pack(pady=(0, 20))

        # Center the dialog
        dialog.update_idletasks()
        width = dialog.winfo_width()
        height = dialog.winfo_height()
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f'{width}x{height}+{x}+{y}')

        dialog.protocol("WM_DELETE_WINDOW", lambda: sys.exit(1))
        dialog.lift()
        dialog.attributes('-topmost', True)
        dialog.after_idle(dialog.attributes, '-topmost', False)

        dialog.mainloop()

    except:
        # If even showing the error dialog fails, fall back to console
        print(f"CRITICAL ERROR: {error_msg}", file=sys.stderr)
        sys.exit(1)


def main():
    """Main entry point for the application."""
    log_file = None

    try:
        # Setup logging first to capture all events
        log_file = setup_logging()
        logging.info(f"Starting {APP_NAME} v{APP_VERSION}")

        # Configure pandas and warnings
        setup_pandas_and_warnings()

        # Verify system requirements
        if not check_system_requirements():
            error_msg = "System requirements not met. See log file for details."
            logging.error(error_msg)
            show_error_dialog(error_msg)
            return 1

        # Import the main application class here to avoid circular imports
        # and to ensure logging is already configured before import
        from app import PlaxisAutomationApp

        # Create main application window
        logging.info("Initializing main application window")
        root = tk.Tk()
        root.title(APP_NAME)

        # Create and run application
        app = PlaxisAutomationApp(root)
        root.mainloop()

        logging.info("Application closed normally")
        return 0

    except Exception as e:
        # Get detailed exception information
        exc_info = traceback.format_exc()
        error_msg = f"{type(e).__name__}: {str(e)}\n\n{exc_info}"

        # Log the error
        if 'logging' in sys.modules and logging.getLogger().handlers:
            logging.critical(f"Unhandled exception: {error_msg}")
        else:
            print(f"CRITICAL ERROR: {error_msg}", file=sys.stderr)

        # Show error dialog
        show_error_dialog(f"An unhandled error occurred:\n\n{error_msg}\n\n" +
                         f"Error details have been saved to: {log_file}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
