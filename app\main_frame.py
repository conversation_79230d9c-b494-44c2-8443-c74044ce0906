"""
PLAXIS Automation Tool - Main Application

This module defines the main application class that handles the UI, authentication,
session management, and provides interfaces to various PLAXIS operations.
"""
import time
import sys
import threading
import logging
from typing import Optional, Dict, Any, List
import tkinter as tk
from tkinter import messagebox, ttk
import os
import json
import traceback
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

# Import functions from other App modules - these are imported but actual connections
# are made in the __init__ method using __get__ to bind them as methods
from .login_frame import (
    show_login_frame, check_license, send_password, login,
    force_security_exit, dev_login,
    logout, update_timer, monitor_elapsed_time, close_program
)
from app.build_fem.main_window import build_plaxis
from app.gen_design.main_window import autorun_plaxis
from .config_server import show_plaxis_password_dialog, load_plaxis_password


class PlaxisAutomationApp:
    """
    Main application class for the PLAXIS Automation Tool.
    
    This class manages the application lifecycle, user interface, authentication,
    and provides access to PLAXIS automation features.
    """

    # Application constants
    APP_TITLE: str = "PLAXIS Automation Tool"
    from version_config import APP_VERSION
    COPYRIGHT: str = "Copyright © 2023 Alex Sze"
    SESSION_DURATION: int = 4 * 60 * 60  # 4 hours in seconds
    ICON_PATH: str = '../AIS.ico'
    DATA_DIR: str = "C:\\ELS_RPA"
    SECURITY_LOG_FILE: str = "security_log.json"
    
    # UI Constants
    FONT_TITLE: tuple = ("Arial", 16, "bold")
    FONT_SUBTITLE: tuple = ("Arial", 14, "bold")
    FONT_NORMAL: tuple = ("Arial", 10)
    FONT_ITALIC: tuple = ("Arial", 10, "italic")
    BUTTON_WIDTH: int = 40
    BUTTON_HEIGHT: int = 2

    def __init__(self, root: tk.Tk) -> None:
        """
        Initialize the application with UI and security settings.
        
        Args:
            root: The root Tkinter window
        """
        self.root = root
        self.root.title(self.APP_TITLE)
        self.root.geometry("700x700")
        self.root.resizable(False, False)
        
        # Thread pool for background tasks
        self.executor = ThreadPoolExecutor(max_workers=3)

        # Set protocol for window close button
        self.root.protocol("WM_DELETE_WINDOW", self.perform_exit)

        # Initialize application components in the correct order
        try:
            # Initialize user and security variables
            self._initialize_security_variables()

            # Setup data directory and security log
            self._setup_data_structures()

            # Bind imported methods to this instance
            self._bind_imported_methods()

            # Initialize PLAXIS specific variables
            self._initialize_plaxis_variables()

            # Start the timer monitoring thread
            self._start_monitor_thread()

            # Create the menu bar
            self.create_menu_bar()

            # Show the login frame
            self.show_login_frame()
            
        except Exception as e:
            logging.error(f"Error during application initialization: {str(e)}")
            logging.error(traceback.format_exc())
            messagebox.showerror(
                "Initialization Error",
                f"An error occurred during startup: {str(e)}\n\nPlease restart the application."
            )
            # Clean exit if initialization fails
            self.perform_exit()

    def _initialize_security_variables(self) -> None:
        """Initialize all user and security-related variables."""
        # User identification
        self.user_name: str = ""
        self.user_email: str = ""

        # Authentication
        self.login_status: bool = False
        self.password_salt: str = ""
        self.password_hash: str = ""
        self.session_token: str = ""
        self.plaxis_password: str = ""

        # Security tracking
        self.login_attempts: int = 0
        self.last_attempt_time: datetime = datetime.now()

        # Session timer
        self.start_time: float = time.time()
        self.duration: int = self.SESSION_DURATION
        self.timer_active: bool = False
        self.time_label: Optional[tk.Label] = None
        self.password_status_label: Optional[tk.Label] = None
        
        # Synchronization objects
        self.timer_lock: threading.Lock = threading.Lock()

    def _setup_data_structures(self) -> None:
        """Create necessary directories and files for app data."""
        try:
            # Create data directory if it doesn't exist
            os.makedirs(self.DATA_DIR, exist_ok=True)
            logging.info(f"Ensured data directory exists at {self.DATA_DIR}")

            # Initialize security log file
            self.security_log = os.path.join(self.DATA_DIR, self.SECURITY_LOG_FILE)
            if not os.path.exists(self.security_log):
                self._create_security_log_file()
                
        except PermissionError as pe:
            logging.error(f"Permission denied creating app directories: {pe}")
            self._show_setup_error(
                f"Cannot access required directory {self.DATA_DIR}.\n"
                f"Please run the application with appropriate permissions."
            )
        except OSError as oe:
            logging.error(f"OS error when creating app directories: {oe}")
            self._show_setup_error(f"Error creating necessary directories: {oe}")

    def _create_security_log_file(self) -> None:
        """Create and initialize the security log file."""
        try:
            log_data = {
                "login_attempts": {},
                "failed_logins": {},
                "created_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "app_version": self.APP_VERSION
            }
            
            with open(self.security_log, "w") as f:
                json.dump(log_data, f, indent=4)
                
            logging.info(f"Created security log at {self.security_log}")
            
        except Exception as e:
            logging.error(f"Failed to create security log file: {e}")
            # Continue without the security log if we can't create it

    def _show_setup_error(self, message: str) -> None:
        """Display a setup error message to the user."""
        messagebox.showerror("Setup Error", message)

    def _bind_imported_methods(self) -> None:
        """Bind imported functions as methods to this instance."""
        # Login related methods
        self.show_login_frame = show_login_frame.__get__(self, PlaxisAutomationApp)
        self.check_license = check_license.__get__(self, PlaxisAutomationApp)
        self.send_password = send_password.__get__(self, PlaxisAutomationApp)
        self.login = login.__get__(self, PlaxisAutomationApp)
        self.force_security_exit = force_security_exit.__get__(self, PlaxisAutomationApp)
        self.show_plaxis_password_dialog = show_plaxis_password_dialog.__get__(self, PlaxisAutomationApp)
        self.dev_login = dev_login.__get__(self, PlaxisAutomationApp)
        self.logout = logout.__get__(self, PlaxisAutomationApp)
        self.update_timer = update_timer.__get__(self, PlaxisAutomationApp)
        self.monitor_elapsed_time = monitor_elapsed_time.__get__(self, PlaxisAutomationApp)
        self.close_program = close_program.__get__(self, PlaxisAutomationApp)

        # PLAXIS functions
        self.build_plaxis = build_plaxis.__get__(self, PlaxisAutomationApp)
        self.autorun_plaxis = autorun_plaxis.__get__(self, PlaxisAutomationApp)

    def _initialize_plaxis_variables(self) -> None:
        """Initialize variables related to PLAXIS operations."""
        self.file_paths = None
        self.excel_inputs = None
        self.excel_master = None
        self.autorun_params = None
        
        # Initialize comprehensive configuration storage
        try:
            from app.gen_design.main_window import _initialize_comprehensive_config
            _initialize_comprehensive_config(self)
        except ImportError:
            logging.warning("Could not import comprehensive configuration initialization")
            # Initialize basic structure as fallback
            self.comprehensive_config = {
                'anchor_geometry': {'configured': False},
                'excavation_level': {'configured': False},
                'anchor_parameters': {'configured': False}
            }
        except Exception as e:
            logging.error(f"Error initializing comprehensive configuration: {str(e)}")
        
        # Automatically load PLAXIS password if it exists
        self._auto_load_plaxis_password()

    def _auto_load_plaxis_password(self) -> None:
        """Automatically load PLAXIS password from data file if it exists."""
        try:
            saved_password = load_plaxis_password()
            if saved_password:
                self.plaxis_password = saved_password
                logging.info("PLAXIS password automatically loaded from data file")
            else:
                logging.debug("No saved PLAXIS password found")
        except Exception as e:
            logging.error(f"Error auto-loading PLAXIS password: {e}")
            # Continue without password if loading fails

    def update_password_status_display(self) -> None:
        """Update the password status display label."""
        if hasattr(self, 'password_status_label') and self.password_status_label and self.password_status_label.winfo_exists():
            if self.plaxis_password:
                # Show masked password (first 2 characters + asterisks)
                masked_password = self.plaxis_password[:2] + "*" * (len(self.plaxis_password) - 2) if len(self.plaxis_password) > 2 else "*" * len(self.plaxis_password)
                password_status = f"Configured ✓ ({masked_password})"
            else:
                password_status = "Not Configured ✗"
            
            self.password_status_label.config(text=f"PLAXIS Password Status: {password_status}")

    def _start_monitor_thread(self) -> None:
        """Start the thread that monitors session time."""
        self.monitor_thread = threading.Thread(
            target=self.monitor_elapsed_time,
            name="SessionMonitor",
            daemon=True
        )
        self.monitor_thread.start()
        logging.debug("Session monitor thread started")

    def create_menu_bar(self) -> None:
        """Create the top menu bar with Help options."""
        try:
            menubar = tk.Menu(self.root)
            self.root.config(menu=menubar)

            # Create Help menu with About option
            help_menu = tk.Menu(menubar, tearoff=0)
            help_menu.add_command(label="About", command=self.show_about)
            
            menubar.add_cascade(label="Help", menu=help_menu)
        except Exception as e:
            logging.error(f"Error creating menu bar: {e}")
            # Continue without menu if creation fails

    def show_about(self) -> None:
        """Display information about the application."""
        about_text = f"""
    =====================================
            {self.APP_TITLE}
                 Version: {self.APP_VERSION}
    =====================================

    {self.COPYRIGHT}
    All rights reserved.

    The software provided is protected under copyright law.
    Any reproduction, distribution, or modification of this 
    software without prior written permission from the owner 
    is strictly prohibited.

    -------------------------------------
                DISCLAIMER
    -------------------------------------
    The PLAXIS automation tool is intended to 
    assist in automating calculations and providing design 
    suggestions.

    However, it is crucial for users to recognize that they 
    bear full responsibility for verifying all aspects of 
    the designs generated by this software.

    Users must ensure compliance with applicable codes, 
    standards, and regulations.
    """

        messagebox.showinfo("About PLAXIS Automation Tool", about_text)

    def show_main_frame(self) -> None:
        """Show the main application frame with PLAXIS options."""
        # Clear existing widgets
        self._clear_window()

        # Try to set window icon if available
        self._set_window_icon()

        # Create UI sections
        self._create_header_section()
        self._create_main_menu_section()

        # Activate timer and start updates
        with self.timer_lock:
            self.timer_active = True
        self.update_timer()

    def _clear_window(self) -> None:
        """Clear all widgets from the root window."""
        # Deactivate existing timer first to prevent updates
        with self.timer_lock:
            self.timer_active = False

        # Destroy all widgets in the root window
        for widget in self.root.winfo_children():
            widget.destroy()

        # Recreate the menu bar
        self.create_menu_bar()

    def _set_window_icon(self) -> None:
        """Set the application icon if available."""
        try:
            self.root.iconbitmap(self.ICON_PATH)
        except tk.TclError:
            logging.debug(f"Could not load icon: {self.ICON_PATH}")
        except Exception as e:
            logging.debug(f"Error setting window icon: {str(e)}")

    def _create_header_section(self) -> None:
        """Create the header section with title and user info."""
        header_frame = tk.Frame(self.root)
        header_frame.pack(fill="x", padx=20, pady=10)

        # Title
        title_label = tk.Label(
            header_frame, 
            text=self.APP_TITLE, 
            font=self.FONT_TITLE
        )
        title_label.pack(anchor="w")

        # User info and logout
        user_label = tk.Label(
            header_frame,
            text=f"Logged in as: {self.user_name}",
            font=self.FONT_ITALIC
        )
        user_label.pack(anchor="e")

        logout_btn = tk.Button(
            header_frame, 
            text="Logout",
            command=self.logout,
            width=10, height=1
        )
        logout_btn.pack(anchor="e", pady=(5, 0))

        # Separator
        separator = ttk.Separator(self.root, orient="horizontal")
        separator.pack(fill="x", padx=20, pady=5)

    def _create_main_menu_section(self) -> None:
        """Create the main menu with operation buttons."""
        menu_frame = tk.Frame(self.root)
        menu_frame.pack(expand=True, fill="both", padx=40, pady=20)

        # Title
        tk.Label(
            menu_frame, 
            text="PLAXIS Operations:", 
            font=self.FONT_SUBTITLE
        ).pack(anchor="w", pady=(0, 20))

        # PLAXIS Password section
        self._create_password_section(menu_frame)

        # Operation buttons section
        self._create_operation_buttons(menu_frame)

        # Footer section with exit button and timer
        self._create_footer_section(menu_frame)

    def _create_password_section(self, parent_frame: tk.Frame) -> None:
        """
        Create the PLAXIS password configuration section.
        
        Args:
            parent_frame: Frame to add widgets to
        """
        config_password_btn = tk.Button(
            parent_frame, 
            text="Configure PLAXIS Password",
            command=self.show_plaxis_password_dialog,
            width=self.BUTTON_WIDTH, 
            height=2
        )
        config_password_btn.pack(pady=10)

        # Password status indicator - show actual password if configured
        if self.plaxis_password:
            # Show masked password (first 2 characters + asterisks)
            masked_password = self.plaxis_password[:2] + "*" * (len(self.plaxis_password) - 2) if len(self.plaxis_password) > 2 else "*" * len(self.plaxis_password)
            password_status = f"Configured ✓ ({masked_password})"
        else:
            password_status = "Not Configured ✗"
            
        self.password_status_label = tk.Label(
            parent_frame,
            text=f"PLAXIS Password Status: {password_status}",
            font=self.FONT_NORMAL
        )
        self.password_status_label.pack(pady=(0, 20))

    def _create_operation_buttons(self, parent_frame: tk.Frame) -> None:
        """
        Create the main operation buttons.
        
        Args:
            parent_frame: Frame to add widgets to
        """
        # Button 1: Import from BIM (placeholder)
        tk.Button(
            parent_frame, 
            text="1. Import from BIM Data",
            command=lambda: messagebox.showinfo("Not Available", "This feature is not yet released."),
            width=self.BUTTON_WIDTH, 
            height=self.BUTTON_HEIGHT
        ).pack(pady=10)

        # Button 2: Build PLAXIS Model
        tk.Button(
            parent_frame, 
            text="2. Build PLAXIS Model",
            command=self.build_plaxis,
            width=self.BUTTON_WIDTH, 
            height=self.BUTTON_HEIGHT
        ).pack(pady=10)

        # Button 3: ELS Generative Design
        tk.Button(
            parent_frame, 
            text="3. ELS Generative Design",
            command=self.autorun_plaxis,
            width=self.BUTTON_WIDTH, 
            height=self.BUTTON_HEIGHT
        ).pack(pady=10)

    def _create_footer_section(self, parent_frame: tk.Frame) -> None:
        """
        Create the footer section with exit button and timer.
        
        Args:
            parent_frame: Frame to add widgets to
        """
        # Exit button
        exit_btn = tk.Button(
            parent_frame, 
            text="Exit",
            command=self.perform_exit,
            width=20, 
            height=1
        )
        exit_btn.pack(pady=20)

        # Timer display
        self.time_label = tk.Label(
            parent_frame, 
            text="Time remaining: --:--:--", 
            font=self.FONT_NORMAL
        )
        self.time_label.pack(pady=10)

    def complete_operation(self, window: Optional[tk.Toplevel], message: str) -> None:
        """
        Show success message and close progress window.
        
        Args:
            window: Progress window to close
            message: Success message to display
        """
        try:
            if window and window.winfo_exists():
                window.destroy()
            messagebox.showinfo("Success", message)
        except tk.TclError:
            # Window may have been destroyed already
            messagebox.showinfo("Success", message)
        except Exception as e:
            logging.error(f"Error completing operation: {e}")
            messagebox.showinfo("Operation Complete", message)

    def perform_exit(self) -> None:
        """Handle application exit gracefully."""
        try:
            # Stop timer updates and background tasks
            with self.timer_lock:
                self.timer_active = False
            
            # Shut down thread pool
            self.executor.shutdown(wait=False)
            
            # Log the exit
            username = self.user_name or "Unknown"
            logging.info(f"User {username} is exiting the application")

            # Explicitly destroy the root window
            if self.root and self.root.winfo_exists():
                self.root.destroy()

            # Exit the application with normal exit code
            sys.exit(0)
            
        except Exception as e:
            logging.error(f"Error during application exit: {e}")
            logging.error(traceback.format_exc())
            # Force exit as last resort
            os._exit(0)

    def _update_main_window_with_saved_config(self, saved_parameters: Dict[str, Dict[str, Any]], saved_values: List[str], config_type: str = 'anchor_parameters'):
        """
        Update the main window to display saved configuration information.
        
        Args:
            saved_parameters: Dictionary of saved parameters
            saved_values: List of summary strings
            config_type: Type of configuration ('anchor_parameters', 'anchor_geometry', 'excavation_level')
        """
        try:
            # Store the saved configuration for display (backwards compatibility)
            self.saved_config_summary = saved_values
            self.saved_config_count = len(saved_parameters)
            
            # Update comprehensive configuration based on type if the method exists
            if hasattr(self, 'comprehensive_config'):
                try:
                    from app.gen_design.main_window import (
                        update_anchor_parameters_config,
                        update_anchor_geometry_config, 
                        update_excavation_level_config
                    )
                    
                    if config_type == 'anchor_parameters':
                        update_anchor_parameters_config(self, {'parameters': saved_parameters})
                    elif config_type == 'anchor_geometry':
                        update_anchor_geometry_config(self, {'anchors': saved_parameters})
                    elif config_type == 'excavation_level':
                        update_excavation_level_config(self, {'stage_data': saved_parameters})
                        
                except ImportError:
                    logging.warning("Could not import comprehensive configuration functions")
                except Exception as e:
                    logging.error(f"Error updating comprehensive configuration: {str(e)}")
            
            # Update any existing configuration display
            self._refresh_config_display()
            
            logging.info(f"Updated main window with {config_type} configuration for {len(saved_parameters)} items")
            
        except Exception as e:
            logging.error(f"Error updating main window with saved config: {str(e)}")

    def _refresh_config_display(self):
        """
        Refresh the configuration display in the main window.
        
        Shows a summary of saved configuration parameters.
        """
        try:
            # Find the configuration window if it exists
            if hasattr(self, 'config_window') and self.config_window.winfo_exists():
                # Look for existing config display frame or create one
                config_display_frame = None
                for child in self.config_window.winfo_children():
                    if hasattr(child, 'config_display_id'):
                        config_display_frame = child
                        break
                
                if not config_display_frame:
                    # Create new config display frame
                    config_display_frame = tk.Frame(self.config_window, bg="#f5f5f7")
                    config_display_frame.config_display_id = True  # Mark it for identification
                    config_display_frame.pack(fill="x", padx=20, pady=10)
                
                # Clear existing content
                for widget in config_display_frame.winfo_children():
                    widget.destroy()
                
                # Check if comprehensive configuration exists and has configured items
                if hasattr(self, 'comprehensive_config'):
                    config_count = 0
                    config_types = []
                    
                    # Count configured items
                    for config_type, config_data in self.comprehensive_config.items():
                        if config_data.get('configured', False):
                            config_count += 1
                            config_types.append(config_type.replace('_', ' ').title())
                    
                    if config_count > 0:
                        # Display comprehensive configuration summary
                        config_label = tk.Label(
                            config_display_frame,
                            text=f"✓ Configuration saved: {', '.join(config_types)}",
                            font=("Helvetica", 10, "bold"),
                            bg="#f5f5f7",
                            fg="#4CAF50"  # Green color to indicate success
                        )
                        config_label.pack(fill="x", pady=5)
                        
                        # Add a details button to show full configuration
                        details_btn = tk.Button(
                            config_display_frame,
                            text="View Configuration Details",
                            command=self._show_config_details,
                            bg="#f2f2f7",
                            fg="#333333",
                            font=("Helvetica", 10),
                            relief="flat",
                            borderwidth=1,
                            cursor="hand2"
                        )
                        details_btn.pack(pady=5)
                        return  # Exit early if comprehensive config was displayed
                
                # Fall back to legacy system if no comprehensive configuration
                if hasattr(self, 'saved_config_count') and self.saved_config_count > 0:
                    config_label = tk.Label(
                        config_display_frame,
                        text=f"✓ Configuration saved for {self.saved_config_count} anchor(s)",
                        font=("Helvetica", 10, "bold"),
                        bg="#f5f5f7",
                        fg="#4CAF50"  # Green color to indicate success
                    )
                    config_label.pack(fill="x", pady=5)
                    
                    # Add a details button to show full configuration
                    details_btn = tk.Button(
                        config_display_frame,
                        text="View Configuration Details",
                        command=self._show_config_details,
                        bg="#f2f2f7",
                        fg="#333333",
                        font=("Helvetica", 10),
                        relief="flat",
                        borderwidth=1,
                        cursor="hand2"
                    )
                    details_btn.pack(pady=5)
                
        except Exception as e:
            logging.error(f"Error refreshing config display: {str(e)}")

    def _show_config_details(self):
        """
        Show detailed configuration information in a comprehensive modal window.
        
        Displays configuration from anchor geometry, excavation level, and anchor parameters.
        """
        try:
            # Check if comprehensive configuration exists
            if hasattr(self, 'comprehensive_config'):
                # Import the comprehensive display function
                from app.gen_design.main_window import _show_config_details as show_comprehensive_details
                show_comprehensive_details(self)
            else:
                # Fall back to legacy display
                if hasattr(self, 'saved_config_summary') and self.saved_config_summary:
                    summary_text = "\n\n".join(self.saved_config_summary)
                    messagebox.showinfo("Configuration Details", summary_text)
                else:
                    messagebox.showinfo("No Configuration", "No configuration has been saved yet.")
                
        except Exception as e:
            logging.error(f"Error showing config details: {str(e)}")
            # Fall back to simple display if comprehensive fails
            try:
                if hasattr(self, 'saved_config_summary') and self.saved_config_summary:
                    summary_text = "\n\n".join(self.saved_config_summary)
                    messagebox.showinfo("Configuration Details", summary_text)
                else:
                    messagebox.showinfo("No Configuration", "No configuration has been saved yet.")
            except:
                messagebox.showerror("Error", "Unable to display configuration details.")
