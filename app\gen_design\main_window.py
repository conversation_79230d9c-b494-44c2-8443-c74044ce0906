"""
PLAXIS Generative Design Module

This module provides functionality for configuring and running
generative design iterations with PLAXIS models.
"""
import logging
import tkinter as tk
from tkinter import messagebox, ttk, filedialog
import os
import pandas as pd
from typing import Optional, Dict, List, Any

# Local application imports
import _Main_Class
import _Main_Read
from app.login_frame import send_email_log
from .anchors.definitions import edit_unified_anchor
from .excavation.definitions import edit_excavation_level

# Import shared utilities and constants
from .common import (
    FONT_REGULAR, FONT_BOLD, FONT_HEADER, BG_COLOR, ACCENT_COLOR, HIGHLIGHT_COLOR,
    SUCCESS_COLOR, TABLE_ROW_HEIGHT, create_modal_window, create_primary_button, 
    create_standard_frame, create_info_label, create_labeled_combobox, 
    create_treeview_with_scrollbars, configure_treeview_style, DEFAULT_WINDOW_SIZES, 
    create_styled_button, create_main_configuration_window, create_stage_selection_frame,
    create_action_buttons_frame, create_secondary_button, validate_dataframe,
    extract_unique_values, safe_float_conversion, safe_int_conversion, 
    safe_string_conversion, handle_error_with_logging, create_header_label,
    create_section_header, create_scrollable_content_area, initialize_comprehensive_config
)


def autorun_plaxis(self):
    """
    Run the PLAXIS Auto Run function with file selection.
    
    This function allows users to select an input file, configure anchor properties,
    and run generative design iterations using PLAXIS.
    """
    try:
        # Log the usage of this feature
        _log_feature_usage(self)

        # Check if PLAXIS password is configured
        if not _ensure_plaxis_password(self):
            return
        
        # Ask for the input file path
        input_filepath = _select_input_file()
        if not input_filepath:
            return  # User cancelled the file dialog

        # Log the selected file
        file_name = os.path.basename(input_filepath)
        logging.info(f"User {self.user_name} selected file: {file_name} for PLAXIS AutoRun")

        # Initialize and load the data
        self.file_paths, self.excel_master = _initialize_data(self, input_filepath)
        
        # Get available stage options
        stage_options = _get_stage_options(self.excel_master)
        
        # Create and show the configuration window
        progress_window = _create_configuration_window(self)
        
        # Create configuration UI components
        config_frame_result = _create_config_frame(progress_window, stage_options)
        stage_var = config_frame_result["stage_var"]
        
        # Create anchor table and its container
        anchor_frame, anchor_info_label, anchor_table = _create_anchor_table_frame(progress_window)
        
        # Function to update anchor list based on stage selection
        def update_anchor_list(*args):
            _update_anchor_display(self, anchor_table, anchor_info_label, stage_var.get())
            
        # Update anchor list when stage selection changes
        stage_var.trace("w", update_anchor_list)
        
        # Initialize the anchor list
        update_anchor_list()
        
        # Initialize comprehensive configuration storage
        _initialize_comprehensive_config(self)
        
        # Create buttons for actions
        _create_action_buttons(self, progress_window, anchor_table)

    except Exception as e:
        messagebox.showerror("Error", f"Failed to run PLAXIS Auto Run: {str(e)}")
        logging.error(f"Error in plaxis_autorun: {str(e)}")
        # Include traceback for detailed debugging
        logging.exception("Detailed traceback:")


def _log_feature_usage(self):
    """Log the usage of the generative design feature."""
    try:
        send_email_log(
            user_name=self.user_name,
            case="3. ELS Generative Design",
            version_type="Base",
            user_email=self.user_email or f"{self.user_name}@asiainfrasolutions.com"
        )
        logging.info(f"User {self.user_name} initiated PLAXIS AutoRun")
    except Exception as e:
        logging.error(f"Failed to log PLAXIS AutoRun: {str(e)}")


def _ensure_plaxis_password(self) -> bool:
    """
    Ensure PLAXIS password is configured.
    
    Returns:
        bool: True if password is configured, False otherwise
    """
    if not self.plaxis_password:
        messagebox.showinfo("PLAXIS Password Required",
                          "Please configure your PLAXIS password before running AutoRun.")
        self.show_plaxis_password_dialog()
        return bool(self.plaxis_password)
    return True


def _select_input_file() -> str:
    """
    Display file selection dialog and return the selected file path.
    
    Returns:
        str: Selected file path or empty string if cancelled
    """
    return filedialog.askopenfilename(
        title="Select PLAXIS Input Excel File for AutoRun",
        filetypes=[("Excel Files", "*.xlsx;*.xls"), ("All Files", "*.*")],
        initialdir=os.path.expanduser("~")
    )


def _initialize_data(self, input_filepath: str) -> tuple:
    """
    Initialize and read data from the input file.
    
    Args:
        input_filepath: Path to the input Excel file
        
    Returns:
        tuple: (file_paths, excel_master)
    """
    file_paths = _Main_Class.FilePaths()
    file_paths.ExcelPlaxis = input_filepath
    excel_master = _Main_Class.ExcelMaster()
    excel_master = _Main_Read.read_input_plaxis(file_paths.ExcelPlaxis, excel_master)
    return file_paths, excel_master


def _get_stage_options(excel_master: Any) -> List[str]:
    """
    Extract unique stage values from AnchorLoc DataFrame.
    
    Args:
        excel_master: ExcelMaster object containing anchor data
        
    Returns:
        List of stage options as strings
    """
    try:
        if hasattr(excel_master, 'AnchorLoc') and validate_dataframe(excel_master.AnchorLoc, 'AnchorLoc'):
            return extract_unique_values(excel_master.AnchorLoc, 'Stage', convert_to_string=True)
        else:
            logging.warning("No valid AnchorLoc data found, using default stage options")
            return ["0", "1", "2"]  # Default fallback values
    except Exception as e:
        logging.error(f"Error extracting stage options: {e}")
        return ["0", "1", "2"]  # Default fallback values


def _create_configuration_window(self) -> tk.Toplevel:
    """
    Create the main configuration window.
    
    Returns:
        tk.Toplevel: The configuration window
    """
    window = create_main_configuration_window(self.root)
    
    # Store reference to configuration window
    self.config_window = window
    
    return window


def _create_config_frame(window: tk.Toplevel, stage_options: List[str]) -> Dict:
    """
    Create frame for configuration options.
    
    Args:
        window: Parent window
        stage_options: List of stage options
        
    Returns:
        dict: Dictionary containing the created widgets
    """
    return create_stage_selection_frame(window, stage_options, initial_selection=0)


def _create_anchor_table_frame(window: tk.Toplevel) -> tuple:
    """
    Create frame and table for displaying anchor information.
    
    Args:
        window: Parent window
        
    Returns:
        tuple: (anchor_frame, anchor_info_label, anchor_table)
    """
    # Create a frame with fixed height for anchor information display
    anchor_frame = create_standard_frame(window)
    anchor_frame.pack(fill="x", padx=20, pady=(5, 15))  # Don't expand vertically
    
    # Add a label above the table to display the anchor count info
    anchor_info_label = create_info_label(anchor_frame, "", font=FONT_BOLD, fg_color=ACCENT_COLOR)
    anchor_info_label.pack(fill="x", pady=(0, 5))

    # Define columns for the table
    columns = ("Anchor Name", "Anchor Type (F/N/G)", "Section", "Unit",
               "Spacing (m)", "Prestress (kN/m)",
               "EB TSkinStartMax (kN/m)", "EB TSkinEndMax (kN/m)")

    # Create Treeview with scrollbars using shared utility
    table_container, anchor_table, v_scrollbar, h_scrollbar = create_treeview_with_scrollbars(
        anchor_frame, columns, height=160
    )

    # Define column headings and widths
    _configure_table_columns(anchor_table, columns)

    return anchor_frame, anchor_info_label, anchor_table


def _configure_table_columns(table: ttk.Treeview, columns: tuple):
    """Configure the columns in the anchor table."""
    # Create shorter display text for columns with parentheses
    display_texts = {
        "Anchor Name": "Anchor\nName",
        "Anchor Type (F/N/G)": "Anchor Type\n(F/N/G)",
        "Section": "Section",
        "Unit": "Unit",
        "Spacing (m)": "Spacing\n(m)",
        "Prestress (kN/m)": "Prestress\n(kN/m)",
        "EB TSkinStartMax (kN/m)": "EB TSkinStartMax\n(kN/m)",
        "EB TSkinEndMax (kN/m)": "EB TSkinEndMax\n(kN/m)"
    }
    
    # Set column headings with explicit line breaks
    for col in columns:
        display_text = display_texts.get(col, col)
        table.heading(col, text=display_text)
        table.column(col, width=80, anchor="center")

    # Adjust column widths to ensure entire text is visible
    column_widths = {
        "Anchor Name": 120,
        "Anchor Type (F/N/G)": 100,
        "Section": 120,
        "Unit": 50,
        "Spacing (m)": 100,
        "Prestress (kN/m)": 120,
        "EB TSkinStartMax (kN/m)": 150,
        "EB TSkinEndMax (kN/m)": 150
    }
    
    # Apply column widths and set minimum width to ensure text is visible
    for col, width in column_widths.items():
        if col in columns:
            table.column(col, width=width, minwidth=width)


def _update_anchor_display(self, table: ttk.Treeview, info_label: tk.Label, stage: str):
    """
    Update the anchor table with data for the selected stage.
    
    Args:
        table: The ttk.Treeview widget to update
        info_label: Label to show anchor count information
        stage: Selected stage as string
    """
    try:
        # Clear current table
        for item in table.get_children():
            table.delete(item)

        # Normalize and validate stage value
        stage_index = safe_int_conversion(stage, 1)
        
        # Check if we have anchor data using validation utility
        if not hasattr(self.excel_master, 'AnchorLoc') or not validate_dataframe(self.excel_master.AnchorLoc, 'AnchorLoc'):
            info_label.config(text="No anchor data available")
            return

        # Filter anchors by selected stage
        filtered_anchors = self.excel_master.AnchorLoc[self.excel_master.AnchorLoc['Stage'] == stage_index]

        if filtered_anchors.empty:
            info_label.config(text=f"No anchors found for stage {stage_index}")
            return

        # Update the info label with count info
        info_label.config(text=f"Anchors for Stage {stage_index} (Total: {len(filtered_anchors)})")

        # Add anchors to table
        for _, anchor in filtered_anchors.iterrows():
            _add_anchor_to_table(table, anchor)

    except Exception as e:
        logging.error(f"Error updating anchor list: {e}")
        table.delete(*table.get_children())
        table.insert('', 'end', values=(f"Error: {str(e)}", "", "", "", "", "", "", ""))


def _add_anchor_to_table(table: ttk.Treeview, anchor: pd.Series):
    """
    Add a single anchor to the table.
    
    Args:
        table: The table to add the anchor to
        anchor: Series containing anchor data
    """
    try:
        # Extract anchor properties with safe defaults using utility functions
        name = safe_string_conversion(anchor.get('Anchor Name'), 'N/A')
        anchor_type = safe_string_conversion(anchor.get('Anchor Type (F/N/G)'), 'N/A')
        section = safe_string_conversion(anchor.get('Section'), 'N/A')
        unit = safe_string_conversion(anchor.get('Unit'), 'N/A')
        
        # Handle numerical values with safe conversion and proper formatting
        spacing = round(safe_float_conversion(anchor.get('Spacing (m)'), 0.0), 2)
        prestress = round(safe_float_conversion(anchor.get('Prestress (kN/m)'), 0.0), 1)

        eb_tskin_start = round(safe_float_conversion(anchor.get('EB TSkinStartMax (kN/m)'), 0.0), 1)
        eb_tskin_end = round(safe_float_conversion(anchor.get('EB TSkinEndMax (kN/m)'), 0.0), 1)

        # Insert row in treeview
        item = table.insert('', 'end', values=(
            name, anchor_type, section, unit, spacing, prestress,
            eb_tskin_start, eb_tskin_end
        ))

        # Highlight 'N' type anchors
        if anchor_type == 'N':
            table.item(item, tags=('highlight',))
            
    except Exception as e:
        error_message = handle_error_with_logging(e, "adding anchor to table", show_dialog=False)
        table.insert('', 'end', values=(
            f"Error displaying anchor: {str(e)}", "", "", "", "", "", "", ""
        ))


def _create_action_buttons(self, window: tk.Toplevel, anchor_table: ttk.Treeview):
    """
    Create action buttons for the configuration window.
    
    Args:
        window: Parent window
        anchor_table: The anchor table widget
    """
    # Import the execute function
    from .run import execute_generative_design_process
    
    # Get reference to the stage variable from the config frame
    stage_var = None
    for child in window.winfo_children():
        if isinstance(child, tk.Frame) and hasattr(child, 'winfo_children'):
            for widget in child.winfo_children():
                if isinstance(widget, ttk.Combobox):
                    stage_var = widget.cget('textvariable')
                    break
            if stage_var:
                break
    
    # Define buttons configuration - unified anchor editing
    buttons_config = [
        {
            "text": "Edit Anchor",
            "command": lambda: edit_unified_anchor(self, anchor_table, window, window.getvar(stage_var)),
            "style": "primary",
            "width": 18,
            "height": 1
        },
        {
            "text": "Edit Excavation Level", 
            "command": lambda: edit_excavation_level(self, anchor_table, window, window.getvar(stage_var)),
            "style": "primary",
            "width": 18,
            "height": 1
        },
        {
            "text": "View Configuration Detail",
            "command": lambda: _show_enhanced_config_details(self),
            "style": "link",
            "width": 20,
            "height": 1
        }
    ]
    
    # Create action buttons frame using utility
    buttons_frame = create_action_buttons_frame(window, buttons_config)
    
    # Add the Generative Design button separately with special styling
    create_styled_button(
        buttons_frame,
        "Generative Design",
        lambda: execute_generative_design_process(self),
        width=18, height=1, bg_color="#4CAF50", fg_color="white",
        pack_side=tk.LEFT, padx=5, pady=10
    )

def _show_enhanced_config_details(self):
    """
    Show enhanced configuration details using the integrated configuration system.
    """
    try:
        # Import the integrated configuration details module
        from .config.details import show_configuration_details
        
        # Show the enhanced configuration details
        show_configuration_details(self)
        
    except Exception as e:
        error_msg = f"Error showing configuration details: {str(e)}"
        logging.error(error_msg)
        logging.exception("Detailed traceback:")
        messagebox.showerror("Error", "Unable to display configuration details.")

def _initialize_comprehensive_config(self):
    """
    Initialize comprehensive configuration storage for all configuration types.
    
    Args:
        self: The application instance
    """
    try:
        # Use centralized configuration initialization
        self.comprehensive_config = initialize_comprehensive_config()
        
        # Add summary fields for backward compatibility
        for config_type in self.comprehensive_config:
            self.comprehensive_config[config_type]['summary'] = []
        
        logging.info("Comprehensive configuration storage initialized successfully")
        logging.info(f"Initial comprehensive_config keys: {list(self.comprehensive_config.keys())}")
        
    except Exception as e:
        logging.error(f"Error initializing comprehensive config: {str(e)}")
        logging.exception("Detailed traceback:")


def update_anchor_geometry_config(self, geometry_data: Dict[str, Any], stage: Optional[str] = None):
    """
    Update anchor geometry configuration using the integrated system.
    """
    try:
        # Import the integrated function
        from .config.details import update_anchor_geometry_config as update_config
        
        # Update the configuration
        update_config(self, geometry_data, stage)
        
    except Exception as e:
        logging.error(f"Error updating anchor geometry config: {str(e)}")


def update_excavation_level_config(self, excavation_data: Dict[str, Any], stage: Optional[str] = None):
    """
    Update excavation level configuration using the integrated system.
    """
    try:
        # Import the integrated function
        from .config.details import update_excavation_level_config as update_config
        
        # Update the configuration
        update_config(self, excavation_data, stage)
        
    except Exception as e:
        logging.error(f"Error updating excavation level config: {str(e)}")


def update_anchor_parameters_config(self, parameters_data: Dict[str, Any], stage: Optional[str] = None):
    """
    Update anchor parameters configuration using the integrated system.
    """
    try:
        # Import the integrated function
        from .config.details import update_anchor_parameters_config as update_config
        
        # Update the configuration
        update_config(self, parameters_data, stage)
        
    except Exception as e:
        logging.error(f"Error updating anchor parameters config: {str(e)}")
