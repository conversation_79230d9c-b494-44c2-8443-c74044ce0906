# Code Style and Conventions

## General Conventions
- **PEP 8 Compliance** - Python code follows PEP 8 style guidelines
- **Type Hints** - Extensive use of typing annotations for better code clarity
- **Comprehensive Docstrings** - All functions and classes have detailed docstrings with Args/Returns/Raises sections

## Naming Conventions

### Files and Modules
- **Package Structure**: Sophisticated package organization (`app/gen_design/`, `app/gen_design/common/`)
- **Descriptive Module Names**: Clear, purpose-driven naming (e.g., `anchor_geom_def.py`, `anchor_param_class.py`)
- **Common Package**: Shared utilities in `app/gen_design/common/` with specific purposes:
  - `ui_constants.py` - All styling and UI constants
  - `ui_components.py` - Reusable UI component functions
  - `data_utils.py` - Data processing utilities
  - `window_utils.py` - Window management utilities

### Variables and Functions
- **snake_case** for variables, functions, and module names
- **PascalCase** for class names
- **UPPER_CASE** for constants and configuration values

### Component Class Patterns
```python
# UI Component Classes
class CoordinateSet:
    """Represents a set of coordinate inputs with validation."""
    
class GTypeCoordinateSet:
    """Specialized coordinate set for G-type anchors (X1, Y1 only)."""
    
class AnchorSection:
    """Manages multiple coordinate sets for a single anchor."""
    
class GeometryUIComponents:
    """Main UI components manager for anchor geometry."""
```

## Module Structure Pattern
```python
"""
Module Docstring
Detailed description of module purpose and functionality.
"""
import logging
import tkinter as tk
from tkinter import messagebox, ttk
from typing import Dict, List, Optional, Any

# Import shared utilities and constants from common package
from .common import (
    FONT_REGULAR, FONT_BOLD, BG_COLOR, ANCHOR_TYPES, 
    create_scrollable_content_area, create_primary_button,
    safe_float_conversion, handle_error_with_logging
)

# Main classes and functions
```

## Documentation Standards

### Function Documentation
```python
def function_name(param: type, optional_param: Optional[type] = None) -> return_type:
    """
    Brief description of function purpose.
    
    Args:
        param: Description of parameter with type information
        optional_param: Description of optional parameter (optional)
        
    Returns:
        Description of return value and its structure
        
    Raises:
        ValueError: When validation fails with specific conditions
        Exception: For general errors with context
    """
```

### Class Documentation with Examples
```python
class ComponentName:
    """
    Brief description of class purpose.
    
    This class manages [specific functionality] and provides
    [key capabilities]. Used in [context].
    
    Attributes:
        attribute_name: Description of what this stores
        
    Example:
        component = ComponentName(frame, set_number, row)
        values = component.get_values()
    """
```

## Advanced UI Development Patterns

### Apple-Inspired Design System
```python
# Consistent color palette from ui_constants.py
BG_COLOR = "#f5f5f7"          # Light gray background
ACCENT_COLOR = "#0071e3"       # Blue accent color
HIGHLIGHT_COLOR = "#f2f2f7"    # Light highlight color
SUCCESS_COLOR = "#4CAF50"      # Green for success
ERROR_COLOR = "#FF6B6B"        # Red for errors

# Typography hierarchy
FONT_REGULAR = ("Helvetica", 10)
FONT_BOLD = ("Helvetica", 10, "bold")
FONT_HEADER = ("Helvetica", 12, "bold")

# Consistent sizing
ENTRY_WIDTH = 12
LABEL_WIDTH = 15
BUTTON_WIDTH = 3
```

### Component-Based UI Architecture
```python
class UIComponent:
    """Base pattern for reusable UI components."""
    
    def __init__(self, parent_frame: tk.Frame, **kwargs):
        self.frame = parent_frame
        self.create_ui()
    
    def create_ui(self):
        """Create the UI elements for this component."""
        pass
    
    def get_values(self) -> Dict[str, Any]:
        """Get values with validation."""
        pass
    
    def set_values(self, values: Dict[str, Any]):
        """Set values in the component."""
        pass
    
    def destroy(self):
        """Clean up component resources."""
        pass
```

### Advanced Component Patterns
```python
class CoordinateSet:
    """Standard pattern for coordinate input components."""
    
    def __init__(self, frame: tk.Frame, set_number: int, row: int):
        self.frame = frame
        self.set_number = set_number
        self.row = row
        self.entries = {}
        self.create_ui()
    
    def create_ui(self):
        """Create coordinate input fields with consistent styling."""
        # Use shared constants for consistent appearance
        tk.Label(self.frame, text=f"Set {self.set_number}:", 
                font=FONT_BOLD, bg=BG_COLOR).grid(...)
        
        for i, coord_label in enumerate(COORDINATE_LABELS):
            entry = tk.Entry(self.frame, width=ENTRY_WIDTH, 
                           font=FONT_REGULAR, justify='center')
            # Store entry references for validation
            coord_key = coord_label.split()[0]
            self.entries[coord_key] = entry
```

## Error Handling Patterns

### Comprehensive Exception Handling
```python
def operation_with_validation():
    """Example of proper error handling pattern."""
    try:
        # Main operation logic
        result = process_data()
        logging.info("Operation completed successfully")
        return result
    except ValueError as e:
        error_msg = f"Validation error: {str(e)}"
        logging.error(error_msg)
        messagebox.showerror("Validation Error", str(e))
        raise
    except Exception as e:
        error_msg = f"Unexpected error in operation: {str(e)}"
        logging.error(error_msg)
        messagebox.showerror("Error", f"Failed to complete operation: {str(e)}")
        raise
```

### Component-Level Validation
```python
def get_values(self) -> Dict[str, float]:
    """Standard validation pattern for UI components."""
    values = {}
    for coord, entry in self.entries.items():
        try:
            value_str = entry.get().strip()
            value = float(value_str) if value_str else 0.0
            values[coord] = value
        except ValueError:
            raise ValueError(f"Invalid value for {coord} in Set {self.set_number}: '{entry.get()}'")
    return values
```

## Advanced Component Management Patterns

### Dynamic Component Synchronization
```python
def sync_with_paired_anchor(self, action: str):
    """Pattern for synchronizing paired components (F1/F2, G1/G2)."""
    if self._syncing or not self.ui_components_manager:
        return
    
    paired_anchor_name = self.get_paired_anchor_name()
    if not paired_anchor_name:
        return
    
    # Set syncing flag to prevent infinite recursion
    self._syncing = True
    try:
        # Synchronization logic
        # ...
    finally:
        self._syncing = False
```

### Component State Management
```python
def update_button_states(self):
    """Standard pattern for updating UI state based on component state."""
    current_count = len(self.coordinate_sets)
    
    if self.remove_button:
        from .common.ui_constants import ERROR_COLOR
        update_button_state(self.remove_button, current_count > 1, ERROR_COLOR)
```

## Shared Utility Patterns

### Common UI Creation Functions
```python
# In app/gen_design/common/ui_components.py
def create_primary_button(parent, text, command, **kwargs):
    """Create standardized primary button with consistent styling."""
    
def create_danger_button(parent, text, command, **kwargs):
    """Create standardized danger/remove button."""
    
def create_scrollable_content_area(parent):
    """Create scrollable content area for complex forms."""
    
def create_info_label(parent, text, **kwargs):
    """Create standardized info label."""
```

### Data Validation Utilities
```python
# In app/gen_design/common/data_utils.py
def safe_float_conversion(value: str, default: float = 0.0) -> float:
    """Safe conversion with fallback for Excel data."""
    
def handle_error_with_logging(error: Exception, context: str, user_message: str):
    """Standardized error handling with logging."""
    
def handle_validation_error(error_message: str, title: str):
    """Standardized validation error display."""
```

## Package Organization Principles

### Clear Separation by Functionality
- **app/gen_design/**: Main generative design modules
  - `anchor_geom_*`: Anchor geometry configuration
  - `anchor_param_*`: Anchor parameter configuration  
  - `excavation_*`: Excavation configuration
  - `main_window.py`: Main interface
  - `run.py`: Execution logic

- **app/gen_design/common/**: Shared utilities
  - `ui_constants.py`: All UI styling constants
  - `ui_components.py`: Reusable UI component functions
  - `data_utils.py`: Data processing utilities
  - `window_utils.py`: Window management

### Import Patterns
```python
# Standard imports
import logging
import tkinter as tk
from tkinter import messagebox, ttk
from typing import Dict, List, Optional, Any

# Local common imports - specific imports for clarity
from .common import (
    FONT_REGULAR, FONT_BOLD, BG_COLOR, ANCHOR_TYPES,
    create_scrollable_content_area, create_primary_button,
    safe_float_conversion, handle_error_with_logging
)

# Local class imports
from .anchor_geom_class import CoordinateSet, GTypeCoordinateSet, GeometryUIComponents
```

## Advanced Logging Patterns

### Structured Logging with Context
```python
import logging

# Component-specific logging with context
logging.info(f"Added coordinate set {set_number} to {self.anchor_name}")
logging.warning(f"Invalid stage value for filtering: {selected_stage}")
logging.error(f"Error populating anchor sections with defaults: {e}")

# Excel integration logging
logging.info(f"Found {len(coordinate_list)} coordinate sets for anchor type {anchor_type}")
logging.info(f"Populated {len(default_coordinates)} default coordinate sets")
```

### User Feedback Patterns
```python
# Success messages with temporary status
def show_temporary_status(status_label, message, duration=3000):
    """Show temporary status message that clears after duration."""
    
# User-friendly validation messages
if not default_coordinates:
    logging.info(f"No default coordinates found for anchor type {anchor_type}")
    if hasattr(ui_components, 'status_label'):
        ui_components.status_label.config(text="")
```

## Component Lifecycle Patterns

### Standard Component Lifecycle
```python
class ComponentName:
    def __init__(self, parent_frame, *args, **kwargs):
        # Initialize component state
        self.create_ui()
    
    def create_ui(self):
        # Create UI elements using shared constants
        pass
    
    def get_values(self) -> Dict[str, Any]:
        # Validate and return component values
        pass
    
    def set_values(self, values: Dict[str, Any]):
        # Set component values from data
        pass
    
    def clear_values(self):
        # Clear all component values
        pass
    
    def destroy(self):
        # Clean up component resources
        pass
```

### Manager Component Pattern
```python
class UIComponentsManager:
    """Pattern for managing multiple related components."""
    
    def __init__(self):
        self.components = {}
        self.current_state = None
    
    def add_component(self, name: str, *args, **kwargs):
        """Add a new component with standardized creation."""
        
    def remove_component(self, name: str):
        """Remove component with proper cleanup."""
        
    def clear_all_components(self):
        """Clear all components with proper cleanup."""
        
    def update_component_states(self):
        """Update all component states consistently."""
```