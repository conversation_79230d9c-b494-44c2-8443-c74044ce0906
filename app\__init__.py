"""
PLAXIS Automation Tool - App Package

This package contains the main application modules for the PLAXIS Automation Tool.
It includes the main app class, login functionality, and PLAXIS operations.
"""

# Main application class - import with error handling
try:
    from .main_frame import PlaxisAutomationApp
    _main_frame_available = True
except ImportError as e:
    print(f"Warning: Could not import main_frame: {e}")
    PlaxisAutomationApp = None
    _main_frame_available = False

# Login and security functions - import with error handling
try:
    from .login_frame import (
        generate_password_key,
        send_password_email,
        send_email_log,
        log_security_event,
        requires_login,
        show_login_frame,
        check_license,
        send_password,
        login,
        force_security_exit,
        dev_login,
        logout,
        update_timer,
        monitor_elapsed_time,
        close_program
    )
    _login_frame_available = True
except ImportError as e:
    print(f"Warning: Could not import login_frame: {e}")
    _login_frame_available = False

# PLAXIS operations - import with error handling
try:
    from app.build_fem.main_window import build_plaxis
    _build_fem_available = True
except ImportError as e:
    print(f"Warning: Could not import build_fem: {e}")
    build_plaxis = None
    _build_fem_available = False

try:
    from app.gen_design.main_window import autorun_plaxis
    _gen_design_available = True
except ImportError as e:
    print(f"Warning: Could not import gen_design: {e}")
    autorun_plaxis = None
    _gen_design_available = False

try:
    from .config_server import show_plaxis_password_dialog
    _config_server_available = True
except ImportError as e:
    print(f"Warning: Could not import config_server: {e}")
    show_plaxis_password_dialog = None
    _config_server_available = False

# Define what gets imported with "from app import *"
__all__ = [
    # Main application
    'PlaxisAutomationApp',
    
    # Login and security (if available)
    'generate_password_key',
    'send_password_email', 
    'send_email_log',
    'log_security_event',
    'requires_login',
    'show_login_frame',
    'check_license',
    'send_password',
    'login',
    'force_security_exit',
    'dev_login',
    'logout',
    'update_timer',
    'monitor_elapsed_time',
    'close_program',
    
    # PLAXIS operations (if available)
    'build_plaxis',
    'autorun_plaxis',
    'show_plaxis_password_dialog'
]

# Filter out None values from __all__
__all__ = [name for name in __all__ if globals().get(name) is not None]