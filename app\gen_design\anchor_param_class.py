import tkinter as tk
from dataclasses import dataclass
from typing import List

BUTTON_FONT = ("Arial", 8)  # Keep for specific small buttons
DEFAULT_ANCHOR_VALUES = {
    "N": {
        "combos": [
            ("UC356x406x235", 1),
            ("UC356x406x340", 1),
            ("UC356x406x467", 1),
            ("UC356x406x551", 1),
            ("UC356x406x634", 1),
            ("UC356x406x393", 2),
            ("UC356x406x467", 2),
            ("UC356x406x551", 2),
            ("UC356x406x634", 2),
            ("UC356x406x634", 4)
        ]
    },
    "F": {
        # F type anchors use the same default combinations as N type
        "combos": [
            ("UC356x406x235", 1),
            ("UC356x406x340", 1),
            ("UC356x406x467", 1),
            ("UC356x406x551", 1),
            ("UC356x406x634", 1),
            ("UC356x406x393", 2),
            ("UC356x406x467", 2),
            ("UC356x406x551", 2),
            ("UC356x406x634", 2),
            ("UC356x406x634", 4)
        ]
    },
    "G": {
        "combos": [("T40", 1), ("T50", 1)],
        "spacing": ["3"],
        "theta_s": ["45"],
        "theta_g": ["70", "65", "60", "55", "50", "45"],
        "anchor_length": ["10", "11", "13", "15", "15.5"]
    }
}
STEEL_SECTION_SHEETS = {
    "N": "Anchor_List_N",
    "F": "Anchor_List_N",  # F type uses same sections as N type
    "G": "Anchor_List_G"
}
FALLBACK_SECTIONS = ["T13", "T25", "T32", "T40", "T50", "T63"]


@dataclass
class AnchorConfig:
    """Configuration data for an anchor."""
    name: str
    anchor_type: str
    section: str
    unit: str
    spacing: str
    prestress: str


@dataclass
class UIComponentData:
    """Data structure for UI component management."""
    vars_list: List[tk.StringVar]
    entries_list: List[tk.Widget]
    parent_frame: tk.Widget


@dataclass
class SettlementData:
    """Data structure for settlement configuration."""
    x1: str = "0"
    y1: str = "0"
    x2: str = "0"
    y2: str = "0"
