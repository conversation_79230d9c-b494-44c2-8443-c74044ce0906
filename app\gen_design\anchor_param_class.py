"""
Anchor Parameter Classes

This module provides backward compatibility for the reorganized anchor parameter system.
All classes have been moved to the anchors.parameters package but are re-exported here for compatibility.
"""

# Import all classes and constants from the new location for backward compatibility
from .anchors.parameters import (
    BUTTON_FONT,
    DEFAULT_ANCHOR_VALUES,
    STEEL_SECTION_SHEETS,
    FALL<PERSON><PERSON>K_SECTIONS,
    AnchorConfig,
    UIComponentData,
    SettlementData
)

# Re-export for backward compatibility
__all__ = [
    'BUTTON_FONT',
    'DEFAULT_ANCHOR_VALUES',
    'STEEL_SECTION_SHEETS',
    'FALLBACK_SECTIONS',
    'AnchorConfig',
    'UIComponentData',
    'SettlementData'
]
