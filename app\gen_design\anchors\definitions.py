"""
Unified Anchor Configuration Module

This module provides functionality for configuring both anchor geometry and parameters
in a single unified interface for different anchor types (N, F, G) in generative design iterations.
"""
import logging
import tkinter as tk
from tkinter import messagebox, ttk
from typing import Dict, List, Optional, Any

# Import shared utilities and constants
from ..common import (
    FONT_REGULAR, FONT_BOLD, FONT_HEADER, BG_COLOR, ANCHOR_TYPES, PARAM_WINDOW_GEOMETRY, COMBO_WIDTH,
    create_scrollable_content_area, create_primary_button, create_danger_button,
    create_secondary_button, create_success_button, create_info_label,
    show_temporary_status, safe_float_conversion, handle_error_with_logging,
    handle_validation_error, bind_mouse_wheel_globally
)

# Import unified anchor classes
from .models import UnifiedAnchorUIComponents, UnifiedAnchorData

# Import functions from existing modules for compatibility
from ..anchor_geom_def import (
    get_default_coordinates_from_excel, _get_default_anchor_name,
    _populate_anchor_sections_with_defaults
)
from ..anchor_param_def import (
    parse_anchor_data_from_geometry, validate_anchor_parameters,
    create_stage_selection_ui, create_action_buttons as create_param_action_buttons
)


def create_unified_anchor_window(parent_window: tk.Widget, selected_stage: Optional[str] = None) -> tk.Toplevel:
    """
    Create and configure the unified anchor editing window.
    
    Args:
        parent_window: The parent window
        selected_stage: The stage value selected in the parent window
        
    Returns:
        The configured unified anchor window
    """
    from ..common.window_utils import create_modal_window
    from ..common.ui_constants import DEFAULT_WINDOW_SIZES
    
    return create_modal_window(
        parent_window,
        "Edit Unified Anchor Configuration",
        DEFAULT_WINDOW_SIZES.get("unified_anchor", "1400x900"),
        resizable=True,
        center=True
    )


def edit_unified_anchor(app_instance, selected_stage: Optional[str] = None):
    """
    Main function to edit unified anchor configuration (geometry + parameters).
    
    This function creates a unified interface for configuring both anchor geometry
    and parameters in a single window, providing a streamlined user experience.
    
    Args:
        app_instance: The main application instance
        selected_stage: The stage value selected in the parent window
    """
    try:
        logging.info("Starting unified anchor configuration")
        
        # Create the unified anchor window
        window = create_unified_anchor_window(app_instance.root, selected_stage)
        
        # Create scrollable content area
        main_frame, canvas, content_frame = create_scrollable_content_area(window)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Initialize UI components manager
        ui_components = UnifiedAnchorUIComponents()
        ui_components.content_frame = content_frame
        ui_components.excel_master = getattr(app_instance, 'excel_master', None)
        ui_components.selected_stage = selected_stage
        
        # Create anchor type selection frame
        type_frame = tk.Frame(content_frame, bg=BG_COLOR)
        type_frame.pack(fill="x", padx=20, pady=10)
        
        tk.Label(type_frame, text="Anchor Type:", font=FONT_BOLD, bg=BG_COLOR).pack(side=tk.LEFT, padx=5)
        
        ui_components.anchor_type_var = tk.StringVar()
        anchor_type_combo = ttk.Combobox(type_frame, textvariable=ui_components.anchor_type_var,
                                       values=ANCHOR_TYPES, width=COMBO_WIDTH, state="readonly")
        anchor_type_combo.pack(side=tk.LEFT, padx=5)
        anchor_type_combo.set("N")  # Default to N type
        
        # Bind anchor type change event
        def on_anchor_type_change(event=None):
            anchor_type = ui_components.anchor_type_var.get()
            ui_components.current_anchor_type = anchor_type
            
            # Clear existing anchors
            ui_components.clear_all_anchors()
            
            # Add default anchor(s) based on type
            if anchor_type == "N":
                ui_components.add_anchor_section("N")
            elif anchor_type == "F":
                ui_components.add_anchor_section("F1")
            elif anchor_type == "G":
                ui_components.add_anchor_section("G1")
            
            # Update anchor management buttons
            ui_components.update_anchor_management_buttons()
            
            logging.info(f"Changed anchor type to {anchor_type}")
        
        anchor_type_combo.bind("<<ComboboxSelected>>", on_anchor_type_change)
        
        # Create stage selection UI
        stage_frame = create_stage_selection_ui(content_frame, ui_components, selected_stage)
        
        # Create anchor management frame (for F2/G2 addition/removal)
        ui_components.anchor_management_frame = tk.Frame(content_frame, bg=BG_COLOR)
        
        def add_secondary_anchor():
            """Add F2 or G2 anchor based on current type."""
            anchor_type = ui_components.anchor_type_var.get()
            if anchor_type == "F" and "F2" not in ui_components.anchor_sections:
                ui_components.add_anchor_section("F2")
            elif anchor_type == "G" and "G2" not in ui_components.anchor_sections:
                ui_components.add_anchor_section("G2")
            ui_components.update_anchor_management_buttons()
        
        def remove_secondary_anchor():
            """Remove F2 or G2 anchor based on current type."""
            anchor_type = ui_components.anchor_type_var.get()
            if anchor_type == "F" and "F2" in ui_components.anchor_sections:
                ui_components.remove_anchor_section("F2")
            elif anchor_type == "G" and "G2" in ui_components.anchor_sections:
                ui_components.remove_anchor_section("G2")
            ui_components.update_anchor_management_buttons()
        
        ui_components.add_anchor_button = create_primary_button(
            ui_components.anchor_management_frame, "Add Secondary Anchor", add_secondary_anchor
        )
        ui_components.remove_anchor_button = create_danger_button(
            ui_components.anchor_management_frame, "Remove Secondary Anchor", remove_secondary_anchor
        )
        
        # Create action buttons
        action_frame = tk.Frame(content_frame, bg=BG_COLOR)
        action_frame.pack(fill="x", padx=20, pady=20)
        
        def load_defaults():
            """Load default values from Excel."""
            try:
                if not ui_components.excel_master:
                    messagebox.showwarning("No Data", "No Excel data available for loading defaults.")
                    return
                
                # Load default coordinates and parameters
                _populate_anchor_sections_with_defaults(ui_components)
                show_temporary_status(ui_components.status_label, "Default values loaded successfully", 3000)
                logging.info("Loaded default values for unified anchor configuration")
                
            except Exception as e:
                error_msg = f"Error loading defaults: {str(e)}"
                logging.error(error_msg)
                messagebox.showerror("Error", error_msg)
        
        def save_configuration():
            """Save unified anchor configuration."""
            try:
                # Validate and collect all anchor data
                all_anchor_data = ui_components.get_all_anchor_data()
                
                if not all_anchor_data:
                    messagebox.showwarning("No Data", "No anchor configuration to save.")
                    return
                
                # Validate the data
                validated_data = validate_unified_anchor_data(all_anchor_data)
                
                # Store in app instance
                app_instance.unified_anchor_configuration = validated_data
                
                # Update comprehensive configuration using the integrated system
                try:
                    from ..config_details import update_anchor_geometry_config, update_anchor_parameters_config
                    stage = getattr(ui_components, 'selected_stage', None)
                    
                    # Update geometry configuration
                    update_anchor_geometry_config(app_instance, validated_data["geometry_data"], stage)
                    
                    # Update parameter configuration
                    update_anchor_parameters_config(app_instance, validated_data["parameter_data"], stage)
                    
                except ImportError:
                    logging.warning("Could not import configuration update functions")
                except Exception as e:
                    logging.error(f"Error updating comprehensive configuration: {str(e)}")
                
                # Show success message and close window
                messagebox.showinfo("Success", "Unified anchor configuration saved successfully!")
                window.destroy()
                
                logging.info("Saved unified anchor configuration successfully")
                
            except ValueError as e:
                handle_validation_error(str(e))
            except Exception as e:
                handle_error_with_logging(f"Error saving unified anchor configuration: {str(e)}")
        
        def cancel_configuration():
            """Cancel and close the window."""
            window.destroy()
        
        # Create action buttons
        create_primary_button(action_frame, "Load Defaults", load_defaults)
        create_success_button(action_frame, "Save Configuration", save_configuration)
        create_secondary_button(action_frame, "Cancel", cancel_configuration)
        
        # Create status label
        ui_components.status_label = create_info_label(content_frame, "Ready to configure anchors")
        
        # Initialize with default anchor type
        on_anchor_type_change()
        
        # Bind mouse wheel for scrolling
        bind_mouse_wheel_globally(window, canvas)
        
        # Focus the window
        window.focus_set()
        
    except Exception as e:
        error_msg = f"Error creating unified anchor configuration window: {str(e)}"
        logging.error(error_msg)
        logging.exception("Detailed traceback:")
        messagebox.showerror("Error", "Unable to open unified anchor configuration.")


def validate_unified_anchor_data(all_anchor_data: Dict[str, UnifiedAnchorData]) -> Dict[str, Any]:
    """
    Validate unified anchor data and return structured data for storage.
    
    Args:
        all_anchor_data: Dictionary of anchor name to UnifiedAnchorData
        
    Returns:
        Dictionary with validated geometry and parameter data
        
    Raises:
        ValueError: If validation fails
    """
    if not all_anchor_data:
        raise ValueError("No anchor data provided for validation")
    
    geometry_data = {"anchors": {}}
    parameter_data = {}
    
    for anchor_name, anchor_data in all_anchor_data.items():
        # Validate geometry data
        if not anchor_data.geometry_data.get("coordinates"):
            raise ValueError(f"No coordinates provided for anchor {anchor_name}")
        
        geometry_data["anchors"][anchor_name] = anchor_data.geometry_data
        parameter_data[anchor_name] = anchor_data.parameter_data
    
    # Set anchor type from first anchor
    first_anchor = next(iter(all_anchor_data.values()))
    geometry_data["anchor_type"] = first_anchor.anchor_type
    
    return {
        "geometry_data": geometry_data,
        "parameter_data": parameter_data,
        "stage": first_anchor.stage
    }
