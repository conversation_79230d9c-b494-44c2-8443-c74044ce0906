"""
PLAXIS Model Building Module

This module contains functions for building PLAXIS models from Excel input files
through the GUI application. It handles file selection, model building in a separate
thread, and provides user feedback through the UI.
"""

# Standard library imports
import logging
import os
import threading
import traceback
from typing import Optional, Callable, Dict, Any

# Third-party library imports
import tkinter as tk
from tkinter import messagebox, ttk, filedialog

# Local application imports
import _Main_Class
import _Main_Read
import plaxis.builder.build
from app.login_frame import send_email_log


class ModelBuildError(Exception):
    """Exception raised for errors during model building process"""
    pass


def build_plaxis(self):
    """
    Run the PLAXIS initialization function with file selection and progress tracking.
    
    This method provides file selection dialog, processes the selected Excel file
    and builds a PLAXIS model based on the input data.
    
    Returns:
        None
    """
    # Validate user is logged in and has PLAXIS password configured
    if not _validate_prerequisites(self):
        return

    # Ask user to select input file
    input_filepath = _select_input_file()
    if not input_filepath:
        return  # User cancelled the file dialog

    # Log the selected file for audit purposes
    _log_file_selection(self, input_filepath)

    # Create and configure progress window
    progress_window = _create_progress_window(self.root, input_filepath)

    # Create progress indicators
    progress_bar, status_label = _setup_progress_indicators(progress_window)

    # Start process in separate thread
    try:
        _start_build_thread(
            self,
            input_filepath,
            progress_window,
            progress_bar,
            status_label
        )
    except Exception as e:
        progress_window.destroy()
        _handle_startup_error(e)


def _validate_prerequisites(self) -> bool:
    """
    Validate that the user has proper permissions and PLAXIS password is configured.
    
    Args:
        self: Application instance
        
    Returns:
        bool: True if prerequisites are met, False otherwise
    """
    # Log the usage of this feature
    try:
        send_email_log(
            user_name=self.user_name,
            case="2. Build PLAXIS Model",
            version_type="Base",
            user_email=self.user_email or f"{self.user_name}@asiainfrasolutions.com"
        )
        logging.info(f"User {self.user_name} initiated PLAXIS model building")
    except Exception as e:
        logging.error(f"Failed to log PLAXIS model building: {str(e)}")
        # Continue with the function even if logging fails

    # Check if PLAXIS password is configured
    if not self.plaxis_password:
        messagebox.showinfo("PLAXIS Password Required",
                            "Please configure your PLAXIS password before building a model.")
        self.show_plaxis_password_dialog()
        if not self.plaxis_password:
            return False  # User cancelled or didn't set password

    return True


def _select_input_file() -> str:
    """
    Display file selection dialog and return the selected file path.
    
    Returns:
        str: Selected file path or empty string if cancelled
    """
    return filedialog.askopenfilename(
        title="Select PLAXIS Input Excel File",
        filetypes=[("Excel Files", "*.xlsx;*.xls"), ("All Files", "*.*")],
        initialdir=os.path.expanduser("~")
    )


def _log_file_selection(self, filepath: str) -> None:
    """
    Log the file selected by the user.
    
    Args:
        self: Application instance
        filepath: Path to the selected file
    """
    file_name = os.path.basename(filepath)
    logging.info(f"User {self.user_name} selected file: {file_name} for PLAXIS model building")


def _create_progress_window(parent: tk.Tk, filepath: str) -> tk.Toplevel:
    """
    Create a progress window for the model building process.
    
    Args:
        parent: Parent window
        filepath: Path to the selected input file
        
    Returns:
        tk.Toplevel: Configured progress window
    """
    progress_window = tk.Toplevel(parent)
    progress_window.title("Building PLAXIS Model")
    progress_window.geometry("400x150")
    progress_window.transient(parent)
    progress_window.grab_set()  # Make window modal
    progress_window.resizable(False, False)

    # Center the window on screen
    progress_window.update_idletasks()
    width = progress_window.winfo_width()
    height = progress_window.winfo_height()
    x = (progress_window.winfo_screenwidth() // 2) - (width // 2)
    y = (progress_window.winfo_screenheight() // 2) - (height // 2)
    progress_window.geometry(f'{width}x{height}+{x}+{y}')

    try:
        progress_window.iconbitmap('AIS.ico')
    except Exception as e:
        logging.debug(f"Icon not loaded: {str(e)}")

    # Create labels for information
    tk.Label(progress_window, text="Building PLAXIS model with the selected data file:",
             font=("Arial", 10)).pack(pady=10)

    filepath_label = tk.Label(progress_window,
                              text=os.path.basename(filepath),
                              font=("Arial", 9, "italic"))
    filepath_label.pack(pady=5)

    return progress_window


def _setup_progress_indicators(progress_window: tk.Toplevel) -> tuple:
    """
    Create progress bar and status label in the progress window.
    
    Args:
        progress_window: Window to add indicators to
        
    Returns:
        tuple: (progress_bar, status_label)
    """
    progress_bar = ttk.Progressbar(progress_window, mode="indeterminate")
    progress_bar.pack(fill="x", padx=20, pady=10)
    progress_bar.start()

    status_label = tk.Label(progress_window, text="Building model, please wait...",
                            font=("Arial", 10))
    status_label.pack(pady=5)

    return progress_bar, status_label


def _update_ui(progress_window: tk.Toplevel, success: bool, message: str) -> None:
    """
    Update the UI from the main thread with success or error message.
    
    Args:
        progress_window: Progress window to close
        success: Whether operation was successful
        message: Message to display
    """
    try:
        if progress_window.winfo_exists():  # Check if window still exists
            progress_window.destroy()  # Close progress window first

        if success:
            messagebox.showinfo("Success", message)
        else:
            messagebox.showerror("Error", message)
    except tk.TclError:
        # Handle case where window was already destroyed
        if success:
            messagebox.showinfo("Success", message)
        else:
            messagebox.showerror("Error", message)


def _start_build_thread(self, input_filepath: str, progress_window: tk.Toplevel,
                        progress_bar: ttk.Progressbar, status_label: tk.Label) -> None:
    """
    Start a thread to build the PLAXIS model in background.
    
    Args:
        self: Application instance
        input_filepath: Path to the selected input file
        progress_window: Progress window for the operation
        progress_bar: Progress bar widget
        status_label: Status label widget
    """

    # Function to run in a separate thread
    def process_thread():
        try:
            # Update status in main thread
            progress_window.after(0, lambda: status_label.config(
                text="Initializing file paths and data structures..."))

            # Initialize the file paths with the selected file
            self.file_paths = _Main_Class.FilePaths()
            self.file_paths.ExcelPlaxis = input_filepath
            self.excel_master = _Main_Class.ExcelMaster()
            self.PORT_i = 10000
            self.PORT_o = 10001

            # Update status
            progress_window.after(0, lambda: status_label.config(
                text="Reading PLAXIS input file..."))

            logging.info("Reading PLAXIS input file...")
            self.excel_master = _Main_Read.read_input_plaxis(self.file_paths.ExcelPlaxis, self.excel_master)
            logging.info("PLAXIS input file read successfully.")

            # Update status
            progress_window.after(0, lambda: status_label.config(
                text="Building PLAXIS model..."))

            # Build the PLAXIS model
            logging.info("Starting PLAXIS model build...")
            g_i = plaxis.builder.build.build_plaxis(self.excel_master, self.plaxis_password, self.PORT_i, self.PORT_o,
                                                    end_stage='Final')
            logging.info("PLAXIS model build completed.")

            # Schedule success message in the main thread
            progress_window.after(0, lambda: _update_ui(
                progress_window, True, "PLAXIS model built successfully!"))

        except FileNotFoundError as fnf_error:
            error_msg = f"Input file not found: {str(fnf_error)}"
            logging.error(error_msg)
            progress_window.after(0, lambda: _update_ui(progress_window, False, error_msg))

        except Exception as e:
            # Get detailed error information
            error_msg = _format_error_message(e)
            logging.exception(f"Error building PLAXIS model: {error_msg}")
            progress_window.after(0, lambda: _update_ui(progress_window, False, error_msg))

    # Start thread with daemon=True so it doesn't prevent application exit
    build_thread = threading.Thread(target=process_thread, daemon=True)
    build_thread.start()


def _format_error_message(exception: Exception) -> str:
    """
    Format an exception into a user-friendly error message.
    
    Args:
        exception: The exception to format
        
    Returns:
        str: Formatted error message
    """
    # Determine the specific error type and customize the message
    if isinstance(exception, _Main_Read.ReadInputError):
        return f"Error reading input file: {str(exception)}"
    elif isinstance(exception, AttributeError):
        return f"Missing data: {str(exception)}"
    elif isinstance(exception, ValueError):
        return f"Invalid value in input data: {str(exception)}"
    else:
        # Get exception name for unexpected errors
        error_type = exception.__class__.__name__
        return f"An unexpected {error_type} occurred: {str(exception)}"


def _handle_startup_error(exception: Exception) -> None:
    """
    Handle errors that occur during thread startup.
    
    Args:
        exception: The exception that occurred
    """
    error_msg = f"Failed to initiate PLAXIS model build: {str(exception)}"
    logging.error(error_msg)
    messagebox.showerror("Setup Error", error_msg)
