from plxscripting.easy import new_server

from plaxis.generative_design.utils import ensure_directory_exists
from plaxis.builder.utils import get_output_phase_by_number

import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def read_uy_min_max(g_o, phase_o, plot_o):
    """
    Extracts minimum and maximum vertical displacement (Uy) values from a cross-section.

    Parameters:
        g_o: PLAXIS output interface
        phase_o: PLAXIS phase to analyze
        plot_o: Cross-section plot object

    Returns:
        tuple: (uy_min, uy_max) - Minimum and maximum vertical displacement values

    Raises:
        ValueError: If displacement data cannot be retrieved
    """
    try:
        displacement_values = g_o.getcrosssectionresults(plot_o, phase_o, g_o.ResultTypes.Soil.Uy)

        if not displacement_values:
            return 0.0, 0.0

        # More efficient than using list comprehensions
        uy_min = min(displacement_values)
        uy_max = max(displacement_values)

        return uy_min, uy_max
    except Exception as e:
        raise ValueError(f"Failed to retrieve displacement data: {str(e)}")


def extract_fea_result(file_paths, excel_inputs, excel_master, password, cross_sections):
    """
    Extract finite element analysis results from PLAXIS for specified cross_sections.

    Parameters:
        file_paths: Object containing file path information including AutoRunLog path
        excel_inputs: Object containing Excel input data
        excel_master: Object containing master data including AutoRunLog DataFrame
        password: Password for PLAXIS server connection
        cross_sections: List of cross_section objects with x1, y1, x2, y2, and uy_limit properties

    Returns:
        excel_master: Updated excel_master object with extracted results

    Raises:
        ConnectionError: If connection to PLAXIS server fails
        ValueError: If required data is missing from cross_sections
    """
    # Validate inputs
    if not cross_sections:
        logger.warning("No cross sections provided for result extraction")
        return excel_master

    df_autorunlog = excel_master.AutoRunLog.copy()

    logger.info('Reading Analytical Result...')
    port = 10001  # Output port for PLAXIS

    try:
        server, g_o = new_server('localhost', port, password=password)

        # Process each cross-section
        for i, cross_section in enumerate(cross_sections):
            # Validate cross section data
            if not hasattr(cross_section, 'x1') or not hasattr(cross_section, 'y1') or \
                    not hasattr(cross_section, 'x2') or not hasattr(cross_section, 'y2'):
                logger.warning(f"Cross section {i + 1} is missing coordinate attributes")
                continue

            line_pt1 = (cross_section.x1, cross_section.y1)
            line_pt2 = (cross_section.x2, cross_section.y2)
            uy_limit = getattr(cross_section, 'uy_limit', 0.02)  # Default if not specified

            try:
                plot_o = g_o.Plots[-1]
                plot_o = g_o.linecrosssectionplot(plot_o, line_pt1, line_pt2)

                # Define unique column identifiers for this cross section
                points_column_name = f'Line{i + 1}_Points'
                column_name = f'Line{i + 1}_Uy_Min (mm)'

                # Add line points to dataframe (more efficiently using vectorized operation)
                df_autorunlog[points_column_name] = str((line_pt1, line_pt2))

                # Extract results for each phase
                for index, row in df_autorunlog.iterrows():
                    try:
                        phase_num = row['Phase Number']
                        phase_o = get_output_phase_by_number(g_o, phase_num)
                        uy_line_min, uy_line_max = read_uy_min_max(g_o, phase_o, plot_o)

                        # Store results (convert to mm)
                        df_autorunlog.loc[index, column_name] = uy_line_min * 1000
                    except Exception as e:
                        logger.error(f"Error processing phase {phase_num} for cross section {i + 1}: {str(e)}")
                        df_autorunlog.loc[index, column_name] = None

            except Exception as e:
                logger.error(f"Error processing cross section {i + 1}: {str(e)}")
                continue

        # Properly close connection
        g_o.close()

    except Exception as e:
        logger.error(f"Error connecting to PLAXIS server: {str(e)}")
        return excel_master

    # Update master data and save to Excel
    excel_master.AutoRunLog = df_autorunlog
    try:
        ensure_directory_exists(file_paths.AutoRunLog)
        df_autorunlog.to_excel(file_paths.AutoRunLog)
    except Exception as e:
        logger.error(f"Error saving results to Excel: {str(e)}")

    return excel_master
