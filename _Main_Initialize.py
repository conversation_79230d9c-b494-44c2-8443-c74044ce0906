"""
PLAXIS Automation Project - Initialization Module

This module provides functions to initialize file paths and create template data
for the PLAXIS automation process. It handles folder creation and Excel file generation
when no existing files are found.
"""

import os
import logging
from pathlib import Path
from typing import Optional, <PERSON>ple
import tkinter as tk
from tkinter import filedialog
import pandas as pd

import _Main_Class

# Constants
DEFAULT_EXCEL_FILENAMES = {
    'property': 'A.PLAXISInput_Property.xlsx',
    'geometry': 'A.PLAXISInput_Geometry.xlsx',
    'geology': 'A.PLAXISInput_Geology.xlsx',
    'plaxis': 'B.PLAXIS_Input.xlsx',
}
PLAXIS_MODEL_SUBFOLDER = 'PLAXIS Model'
STEEL_LIBRARY_PATH = 'Library_Steel/ELS_Steel_Section.xlsx'

# Configure logger
logger = logging.getLogger(__name__)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


def init_file_paths() -> Optional[_Main_Class.FilePaths]:
    """
    Initialize file paths by prompting the user to select a folder location.
    
    Creates the PLAXIS Model subfolder if it doesn't exist.
    
    Returns:
        FilePaths object with configured paths or None if the user cancels
    """
    file_paths = _Main_Class.FilePaths()
    
    logger.info('PLAXIS Excel Master Data: Please select the folder location')
    
    # Create a root window and hide it
    root = tk.Tk()
    root.withdraw()
    
    # Ask user for directory
    path_folder = filedialog.askdirectory(title="Select folder for PLAXIS data")
    
    # Return None if user cancels
    if not path_folder:
        logger.warning("Folder selection canceled by user")
        return None
    
    logger.info(f'Folder location of PLAXIS Excel Master Data: {path_folder}')
    
    # Create model output directory if needed
    path_folder_output = Path(path_folder) / PLAXIS_MODEL_SUBFOLDER
    try:
        if not path_folder_output.exists():
            path_folder_output.mkdir(parents=True)
            logger.info(f"Created new directory: {path_folder_output}")
    except OSError as e:
        logger.error(f"Error creating directory {path_folder_output}: {e}")
        return None
    
    # Set file paths
    file_paths.ExcelProperty = str(Path(path_folder) / DEFAULT_EXCEL_FILENAMES['property'])
    file_paths.ExcelGeometry = str(Path(path_folder) / DEFAULT_EXCEL_FILENAMES['geometry'])
    file_paths.ExcelGeology = str(Path(path_folder) / DEFAULT_EXCEL_FILENAMES['geology'])
    file_paths.ExcelPlaxis = str(Path(path_folder) / DEFAULT_EXCEL_FILENAMES['plaxis'])
    file_paths.ELSSteelData = STEEL_LIBRARY_PATH
    
    return file_paths


def init_input_geometry(excel_inputs: _Main_Class.ExcelInputs, 
                       file_paths: _Main_Class.FilePaths) -> _Main_Class.ExcelInputs:
    """
    Initialize geometry input data and save to Excel file.
    
    Creates template data for Anchor, Section, VBH and Plate sheets
    and writes them to the geometry Excel file.
    
    Args:
        excel_inputs: ExcelInputs object to store the generated data
        file_paths: FilePaths object with path information
        
    Returns:
        Updated ExcelInputs object with geometry data
        
    Raises:
        IOError: If there's an error writing to the Excel file
    """
    try:
        # Define Anchor data
        data_anchor = [
            ['MS03', 43248.2355, -3200, 82642.57368, -3200, 'UC356x406x634', 1, 85, 7.5, 0],
            ['MS03', 43248.2355, -500, 82642.57368, -500, 'UC356x406x634', 1, 85, 7.5, 0],
            ['MS03', 43248.2355, 2000, 82642.57368, 2000, 'UC356x406x634', 1, 85, 7.5, 0],
            ['MS03', 43248.2355, 4500, 82642.57368, 4500, 'UC356x406x634', 1, 85, 7.5, 0]
        ]

        columns_anchor = [
            'Strut Mark', 'Anchor Type (F/N/G)', 'Stage', 'X1', 'Z1', 'X2', 'Z2', 'Steel Section', 'Number of Section',
            'Angle to Waling (Deg)', 'Spacing (m)', 'Prestress (kN/m)'
        ]

        excel_inputs.Anchor = pd.DataFrame(data_anchor, columns=columns_anchor)

        # Define Section data
        data_section = [
            ['Section_Start', 0],
            ['Section_End', 125890.8],
        ]

        columns_section = ['Name', 'X']
        excel_inputs.Section = pd.DataFrame(data_section, columns=columns_section)

        # Define VBH (Vertical Borehole) data
        data_vbh = [
            ['DH5', 79542.251926],
            ['BH04', 65027.571528],
            ['DH04', 43276.480071]
        ]

        columns_vbh = ['Borehole Name', 'X']
        excel_inputs.VBH = pd.DataFrame(data_vbh, columns=columns_vbh)

        # Define Plate data
        data_plate = [
            ['CHS610x25', 82642.57368, 900, 5500, -4800, -21500],
            ['CHS610x25', 43248.2355, 900, 5500, -4800, -21500]
        ]

        columns_plate = [
            'Pile Section', 'X', 'Pile Spacing',
            'Cut-Off Level', 'Final Excavation Level', 'Founding Level',
        ]

        excel_inputs.Plate = pd.DataFrame(data_plate, columns=columns_plate)

        # Write data to Excel file
        with pd.ExcelWriter(file_paths.ExcelGeometry) as writer:
            excel_inputs.Anchor.to_excel(writer, sheet_name='Anchor', index=False)
            excel_inputs.Section.to_excel(writer, sheet_name='Section', index=False)
            excel_inputs.VBH.to_excel(writer, sheet_name='VBH', index=False)
            excel_inputs.Plate.to_excel(writer, sheet_name='Plate', index=False)

        logger.info(f"Created geometry input file: {file_paths.ExcelGeometry}")
        
    except Exception as e:
        logger.error(f"Error creating geometry input file: {e}")
        raise

    return excel_inputs


def init_input_geology(excel_inputs: _Main_Class.ExcelInputs,
                      file_paths: _Main_Class.FilePaths) -> _Main_Class.ExcelInputs:
    """
    Initialize geology input data and save to Excel file.
    
    Creates template data for soil properties and borehole information
    and writes them to the geology Excel file.
    
    Args:
        excel_inputs: ExcelInputs object to store the generated data
        file_paths: FilePaths object with path information
        
    Returns:
        Updated ExcelInputs object with geology data
        
    Raises:
        IOError: If there's an error writing to the Excel file
    """
    try:
        # Define soil properties data
        data_soil_prop = [
            ['FILL', None, 'MC', 'D', 19, 12000, 0.3, 0, 30, 0, 0.667, True, True, 0.667, 0.667, 0],
            ['MD', None, 'MC', 'UB', 19, 8000, 0.25, 0, 0, 0, 0.667, True, True, 0.667, 0.667, 10],
            ['ALL', None, 'MC', 'D', 19, 20000, 0.25, 0, 33, 0, 0.667, True, True, 0.667, 0.667, 0],
            ['RES', None, 'MC', 'D', 19, 25500, 0.25, 0, 33, 0, 0.667, True, True, 0.667, 0.667, 0],
            ['CDG', -20, 'MC', 'D', 19, 15000, 0.25, 5, 33, 0, 0.667, True, True, 0.667, 0.667, 0],
            ['CDG', -28, 'MC', 'D', 19, 25000, 0.25, 5, 33, 0, 0.667, True, True, 0.667, 0.667, 0],
            ['CDG', -35, 'MC', 'D', 19, 33000, 0.25, 5, 33, 0, 0.667, True, True, 0.667, 0.667, 0],
            ['CDG', -40, 'MC', 'D', 19, 47000, 0.25, 5, 33, 0, 0.667, True, True, 0.667, 0.667, 0],
            ['CDG', -47, 'MC', 'D', 19, 60000, 0.25, 5, 33, 0, 0.667, True, True, 0.667, 0.667, 0],
            ['CDG', -49, 'MC', 'D', 19, 100000, 0.25, 5, 33, 0, 0.667, True, True, 0.667, 0.667, 0],
            ['CDG', None, 'MC', 'D', 19, 200000, 0.25, 5, 33, 0, 0.667, True, True, 0.667, 0.667, 0],
            ['HDG', None, 'MC', 'D', 19, 15000, 0.25, 5, 33, 0, 0.667, True, True, 0.667, 0.667, 0],
            ['ROCK', None, 'LE', 'NP', 26, 5000000, 0.25, 400, 45, 0, 0.667, True, True, 0.667, 0.667, 0]
        ]

        columns_soil_prop = [
            'Soil Type', 'Bottom Level (mPD)', 'Material', 'Drainage Type', 'Unit weight (kN/m3)', 
            "E' (kPa)", "v'", "c' (kPa)", "Φ' (deg)", "Tensile Strength (kPa)", "Interface strength", 
            "K0 Auto (T/F)", "K0x = K0y (T/F)", "K0x", "K0y", "s_u (kN/m2)"
        ]

        excel_inputs.SoilProperties = pd.DataFrame(data_soil_prop, columns=columns_soil_prop)

        # Define borehole data
        data_borehole = [
            ['DH04', 'FILL', 5.5, -14.65],
            ['DH04', 'ALL', -14.65, -18],
            ['DH04', 'CDG', -18, -44.65],
            ['DH04', 'HDG', -44.65, -49.63],
            ['DH04', 'ROCK', -49.63, -61.55],
            ['BH04', 'FILL', 5.5, -13.09],
            ['BH04', 'ALL', -13.09, -21.09],
            ['BH04', 'CDG', -21.09, -39.09],
            ['BH04', 'HDG', -39.09, -46.16],
            ['BH04', 'ROCK', -46.16, -61.55],
            ['DH5', 'FILL', 5.5, -9.3],
            ['DH5', 'MD', -9.3, -15.3],
            ['DH5', 'ALL', -15.3, -23.3],
            ['DH5', 'CDG', -23.3, -45.3],
            ['DH5', 'HDG', -45.3, -49.97],
            ['DH5', 'ROCK', -49.97, -61.55]
        ]

        columns_borehole = ['Borehole', 'Soil', 'Top Level (mPD)', 'Bottom Level (mPD)']
        excel_inputs.Borehole = pd.DataFrame(data_borehole, columns=columns_borehole)

        # Write data to Excel file
        with pd.ExcelWriter(file_paths.ExcelGeology) as writer:
            excel_inputs.SoilProperties.to_excel(writer, sheet_name='Soil_Properties', index=False)
            excel_inputs.Borehole.to_excel(writer, sheet_name='Borehole', index=False)

        logger.info(f"Created geology input file: {file_paths.ExcelGeology}")
        
    except Exception as e:
        logger.error(f"Error creating geology input file: {e}")
        raise

    return excel_inputs


def init_input_property(excel_inputs: _Main_Class.ExcelInputs, 
                       file_paths: _Main_Class.FilePaths) -> _Main_Class.ExcelInputs:
    """
    Initialize property input data and save to Excel file.
    
    Reads steel section data from the library file and initializes property data.
    
    Args:
        excel_inputs: ExcelInputs object to store the generated data
        file_paths: FilePaths object with path information
        
    Returns:
        Updated ExcelInputs object with property data
        
    Raises:
        FileNotFoundError: If the steel library file is not found
        IOError: If there's an error writing to the Excel file
    """
    try:
        # Check if steel library exists
        if not os.path.exists(file_paths.ELSSteelData):
            logger.error(f"Steel library file not found: {file_paths.ELSSteelData}")
            raise FileNotFoundError(f"Steel library file not found: {file_paths.ELSSteelData}")
            
        # Read steel section properties from library
        data_steel_section_prop = pd.read_excel(file_paths.ELSSteelData, sheet_name="Steel_Section")
        excel_inputs.SteelSectionProp = pd.DataFrame(data_steel_section_prop)
        
        # Ensure PlateProp exists
        if excel_inputs.PlateProp.empty:
            logger.warning("PlateProp is empty, initializing empty DataFrame")
            excel_inputs.PlateProp = pd.DataFrame()

        # Write data to Excel file
        with pd.ExcelWriter(file_paths.ExcelProperty) as writer:
            excel_inputs.SteelSectionProp.to_excel(writer, sheet_name='Steel_Sections_Properties', index=False)
            excel_inputs.PlateProp.to_excel(writer, sheet_name='Plate_Properties', index=False)

        logger.info(f"Created property input file: {file_paths.ExcelProperty}")
        
    except FileNotFoundError:
        # Re-raise to be handled by caller
        raise
    except Exception as e:
        logger.error(f"Error creating property input file: {e}")
        raise

    return excel_inputs


def init_input(file_paths: _Main_Class.FilePaths) -> _Main_Class.ExcelInputs:
    """
    Initialize all input data by checking for existing files and creating them if needed.
    
    Args:
        file_paths: FilePaths object with path information
        
    Returns:
        ExcelInputs object with all initialized data
        
    Raises:
        ValueError: If file_paths is None
    """
    if file_paths is None:
        logger.error("File paths are not initialized")
        raise ValueError("File paths must be initialized before initializing inputs")
    
    excel_inputs = _Main_Class.ExcelInputs()
    
    # Create geometry input file if it doesn't exist
    if not os.path.exists(file_paths.ExcelGeometry):
        logger.info(f"Geometry input file not found, creating: {file_paths.ExcelGeometry}")
        excel_inputs = init_input_geometry(excel_inputs, file_paths)
    
    # Create property input file if it doesn't exist
    if not os.path.exists(file_paths.ExcelProperty):
        logger.info(f"Property input file not found, creating: {file_paths.ExcelProperty}")
        try:
            excel_inputs = init_input_property(excel_inputs, file_paths)
        except FileNotFoundError as e:
            logger.warning(f"Could not create property input file: {e}")
    
    # Create geology input file if it doesn't exist
    if not os.path.exists(file_paths.ExcelGeology):
        logger.info(f"Geology input file not found, creating: {file_paths.ExcelGeology}")
        excel_inputs = init_input_geology(excel_inputs, file_paths)
    
    logger.info("Input initialization complete")
    return excel_inputs

