"""
Common utilities and shared components for the generative design module.

This package contains shared constants, UI components, and utility functions
used across the generative design modules to reduce code duplication and
improve maintainability.
"""

from .ui_constants import *
from .ui_components import *
from .data_utils import *
from .window_utils import *
from .config_utils import *

__all__ = [
    # UI Constants
    'FONT_REGULAR', 'FONT_BOLD', 'FONT_HEADER', 
    'BG_COLOR', 'ACCENT_COLOR', 'HIGHLIGHT_COLOR', 'SUCCESS_COLOR',
    'ENTRY_WIDTH', 'BUTTON_WIDTH', 'COMBO_WIDTH', 'DEFAULT_WINDOW_SIZES',
    'TABLE_ROW_HEIGHT',
    'SETTLEMENT_COLUMNS', 'SETTLEMENT_DEFAULT_VALUES',
    
    # UI Components
    'create_styled_button', 'create_modal_window', 'create_scrollable_content_area',
    'create_action_buttons_frame', 'apply_window_icon', 'create_standard_frame',
    'create_info_label', 'create_header_label', 'create_section_header',
    'create_primary_button', 'create_secondary_button', 'create_success_button',
    'create_danger_button', 'create_link_button', 'create_labeled_combobox', 'create_treeview_with_scrollbars',
    'configure_treeview_style', 'bind_mouse_wheel_globally',
    
    # Data Utils
    'safe_float_conversion', 'safe_int_conversion', 'safe_string_conversion',
    'validate_dataframe', 'handle_error_with_logging', 'handle_validation_error',
    'find_coordinate_columns', 'extract_unique_values',
    
    # Window Utils
    'setup_modal_window', 'center_window_on_screen', 'create_main_configuration_window',
    'create_stage_selection_frame',
    
    # Config Utils
    'initialize_comprehensive_config', 'ensure_comprehensive_config_exists',
    'update_anchor_geometry_config', 'update_anchor_parameters_config', 
    'update_excavation_level_config', 'get_configuration_status', 
    'is_configuration_complete', 'reset_configuration'
]