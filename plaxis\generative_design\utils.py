import math
import os

import logging

import numpy as np
import pandas as pd

import _Main_Class

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


def ensure_directory_exists(file_path: str) -> None:
    """
    Ensure the directory for a given file path exists, creating it if necessary.

    Args:
        file_path: Full path to a file (the directory will be extracted and created)

    Raises:
        OSError: If directory creation fails due to permissions or other issues
    """
    try:
        directory = os.path.dirname(file_path)
        logger.info(f"Checking directory for file: {file_path}")
        logger.info(f"Directory to check/create: {directory}")

        if directory and not os.path.exists(directory):
            os.makedirs(directory)
            logger.info(f"Successfully created directory: {directory}")
        elif directory:
            logger.info(f"Directory already exists: {directory}")
        else:
            logger.warning(f"No directory component in file path: {file_path}")
    except OSError as e:
        logger.error(f"Failed to create directory for {file_path}: {e}")
        raise


def define_anchor(row):
    """
    Creates and configures an Anchor object from input data.

    Parameters:
        row (pandas.DataFrame or pandas.Series): DataFrame row containing anchor properties

    Returns:
        _Main_Class.Anchor: Configured anchor object

    Raises:
        KeyError: If required columns are missing from input data
        ValueError: If data format is invalid or conversion fails
        TypeError: If row is not a DataFrame or Series
    """
    # Validate row is not empty
    if row is None or (hasattr(row, 'empty') and row.empty):
        raise ValueError("Input row is empty or None")

    # Check input type
    if not isinstance(row, (pd.DataFrame, pd.Series)):
        raise TypeError(f"Expected DataFrame or Series, got {type(row).__name__}")

    # Use sets for more efficient column validation
    required_columns = {
        'Anchor Name', 'Anchor Type (F/N/G)', 'Stage',
        'X1 (m)', 'Y1 (m)', 'X2 (m)', 'Y2 (m)',
        'α, Angle to Waling (deg)', 'θ, Angle to Gravity (deg)',
        'Section', 'Unit', 'E (MPa)', 'Area (cm2)', 'Spacing (m)', 'Prestress (kN/m)',
        'X3 (m)', 'Y3 (m)', 'EB Unit Weights (kN/m3)', 'EB Diameter (m/m)',
        'EB TSkinStartMax (kN/m)', 'EB TSkinEndMax (kN/m)'
    }

    # Use set difference for more efficient missing column detection
    available_columns = set(row.columns if isinstance(row, pd.DataFrame) else row.index)
    missing_columns = required_columns - available_columns
    if missing_columns:
        raise KeyError(f"Missing required columns: {', '.join(missing_columns)}")

    # Helper function to safely extract and convert values
    def extract_value(column, dtype=float, allow_negative=True):
        try:
            value = row[column]
            # Handle both DataFrame and Series
            if isinstance(value, pd.Series):
                value = value.iloc[0]
            value = dtype(value)

            # Validate non-negative values where appropriate
            if not allow_negative and value < 0:
                logger.warning(f"Column '{column}' contains negative value: {value}")

            return value
        except (ValueError, TypeError) as e:
            raise ValueError(f"Error converting '{column}' to {dtype.__name__}: {str(e)}")
        except Exception as e:
            raise ValueError(f"Unexpected error processing '{column}': {str(e)}")

    # Create and configure anchor object
    try:
        anchor = _Main_Class.Anchor()

        # Basic properties
        anchor.name = extract_value('Anchor Name', str)
        anchor.type = extract_value('Anchor Type (F/N/G)', str)
        anchor.stage = extract_value('Stage', int)

        # Coordinates
        anchor.x1 = extract_value('X1 (m)')
        anchor.y1 = extract_value('Y1 (m)')
        anchor.x2 = extract_value('X2 (m)')
        anchor.y2 = extract_value('Y2 (m)')
        anchor.x3 = extract_value('X3 (m)')
        anchor.y3 = extract_value('Y3 (m)')

        # Angles
        anchor.alpha = extract_value('α, Angle to Waling (deg)')
        anchor.theta = extract_value('θ, Angle to Gravity (deg)')

        # Material properties
        anchor.section = extract_value('Section', str)
        anchor.unit = extract_value('Unit', int, allow_negative=False)
        anchor.e = extract_value('E (MPa)', allow_negative=False)
        anchor.area = extract_value('Area (cm2)', allow_negative=False)
        anchor.spacing = extract_value('Spacing (m)', allow_negative=False)
        anchor.prestress = extract_value('Prestress (kN/m)')

        # Embedded beam properties
        anchor.eb_unit_weights = extract_value('EB Unit Weights (kN/m3)', allow_negative=False)
        anchor.eb_diameter = extract_value('EB Diameter (m/m)', allow_negative=False)
        anchor.eb_tskin_start_max = extract_value('EB TSkinStartMax (kN/m)')
        anchor.eb_tskin_end_max = extract_value('EB TSkinEndMax (kN/m)')

        logger.debug(f"Successfully created anchor '{anchor.name}' of type '{anchor.type}'")
        return anchor

    except Exception as e:
        logger.error(f"Failed to create anchor object: {str(e)}")
        raise


def define_anchors(autorun_params, df_anchor_loc):
    """
    Creates anchor objects from autorun parameters and anchor location data.

    Args:
        autorun_params: Dictionary containing anchor parameters from UI
        df_anchor_loc: DataFrame containing anchor location data

    Returns:
        tuple: (anchors, cross_sections) lists

    Raises:
        ValueError: If autorun_params is None or empty
        TypeError: If autorun_params is not iterable
    """
    # Validate input parameters
    if autorun_params is None:
        raise ValueError("autorun_params cannot be None. Please ensure anchor parameters are collected from the UI before calling this function.")
    
    if not autorun_params:
        raise ValueError("autorun_params is empty. Please configure anchor parameters in the UI first.")
    
    # Initialize an empty list to store all anchor objects
    anchors = []
    cross_sections = []

    # Iterate through each autorun parameter set
    for autorun_param in autorun_params:
        # Filter anchor location data for the current anchor
        row = df_anchor_loc.loc[df_anchor_loc['Anchor Name'] == autorun_params[autorun_param]['Name']]

        # Create and configure the anchor object
        anchor = define_anchor(row)

        # Extract layer and other parameters from the autorun configuration
        list_section_unit = autorun_params[autorun_param].get('Section_Unit')
        list_spacing = autorun_params[autorun_param].get('Spacing')
        list_prestress = autorun_params[autorun_param].get('Prestress')
        list_settlement = autorun_params[autorun_param].get('Settlement')
        list_theta_s = autorun_params[autorun_param].get('Theta_s')
        list_theta_g = autorun_params[autorun_param].get('Theta_g')
        list_length = autorun_params[autorun_param].get('Length')

        if list_settlement:
            for ((x1, y1), (x2, y2)) in list_settlement:
                # Create a cross-section object and set its properties
                cross_section = _Main_Class.CrossSection()
                cross_section.x1 = x1
                cross_section.y1 = y1
                cross_section.x2 = x2
                cross_section.y2 = y2
                cross_section.uy_limit = 0.02  # Default displacement limit
                cross_sections.append(cross_section)

        anchor.list_section_unit = list_section_unit
        anchor.list_spacing = list_spacing
        anchor.list_prestress = list_prestress
        anchor.list_theta_s = list_theta_s
        anchor.list_theta_g = list_theta_g
        anchor.list_length = list_length

        # Add the configured anchor to the list
        anchors.append(anchor)
    return anchors, cross_sections


def interpolate_y(x, x_values, y_values):
    """
    Interpolate the height of the soil at a given x-coordinate.

    Parameters:
        x (float): The x-coordinate at which to calculate the soil height.
        x_values (numpy.ndarray): Array of x-coordinates from the ground profile data.
        y_values (numpy.ndarray): Array of corresponding soil heights (top values) for the x-coordinates.

    Returns:
        float: The interpolated soil height at the given x-coordinate.

    Raises:
        ValueError: If the x-coordinate is out of the bounds of the x_values array.
    """
    # Check if x is within the bounds of the data
    if not (x_values.min() <= x <= x_values.max()):
        raise ValueError(
            f"x-coordinate {x} is out of bounds of the ground profile data ({x_values.min()} to {x_values.max()})")

    # Interpolate the height of the soil at the given x-coordinate
    y = np.interp(x, x_values, y_values)
    return y


def cal_anchor_g_end_pts(x1, y1, y_fel, theta_s, theta_g, length, side='left'):
    """
    Calculate the positions of two anchor points (x2, y2) and (x3, y3) based on the given parameters.

    Parameters:
        x1 (float): The x-coordinate of the starting point of the anchor.
        y1 (float): The y-coordinate of the starting point of the anchor.
        y_fel (float): The y-coordinate of the excavation level.
        length (float): The total length of the anchor.
        theta_g (float): The angle of the anchor with respect to the horizontal plane (in degrees).
        theta_s (float): The angle of the failure plane with respect to the horizontal plane (in degrees).
        side (str, optional): The side of the anchor ('left' or 'right'). Defaults to 'left'.

    Returns:
        tuple: A tuple containing two points:
            - (x_2, y_2): The position of the anchor socket in the failure plane.
            - (x_3, y_3): The position of the anchor tip.
    """
    # Determine the sign for x-coordinate calculations based on the side
    sign = -1 if side == 'left' else 1

    # Calculate the length of the anchor socket in the failure plane (l_s)
    l_s = (y1 - y_fel) / math.sin(math.radians(180 - theta_g - (90 - theta_s))) * math.sin(math.radians(90 - theta_s))

    # Calculate the anchor socket (x2, y2)
    x2 = x1 + sign * l_s * math.sin(math.radians(theta_g))
    y2 = y1 - l_s * math.cos(math.radians(theta_g))

    if l_s <= length:
        # Calculate the anchor tip (x3, y3)
        x3 = x1 + sign * length * math.sin(math.radians(theta_g))
        y3 = y1 - length * math.cos(math.radians(theta_g))

        coordinates = [round(coord, 2) for coord in (x2, y2, x3, y3)]
        x2, y2, x3, y3 = coordinates
    else:
        x3, y3 = x2, y2

    return (x2, y2), (x3, y3)
