import math


def add_plate_prop(g_i, name, uw, ea, ei, v):
    """
    Adds or updates a plate property in the PLAXIS server.

    Args:
        g_i: The PLAXIS server instance.
        name (str): The name of the plate property.
        uw (float): The unit weight of the plate (kN/m^3).
        ea (float): The axial stiffness of the plate (kN/m).
        ei (float): The bending stiffness of the plate (kNm^2/m).
        v (float): The Poisson's ratio of the plate.

    Returns:
        target_platemat: The plate material object in the PLAXIS server.
    """
    material_type = 1  # Assume elastic
    list_platemat = [mat for mat in g_i.Materials if mat.TypeName.value == 'PlateMat']

    # Check if the plate property already exists
    for platemat in list_platemat:
        if platemat.Name == name:
            platemat.setproperties(
                ("Identification", name),
                ("MaterialType", material_type),
                ("w", uw),
                ("EA1", ea),
                ("EI", ei),
                ("StructNu", v)
            )
            return platemat

    # Create a new plate property if not found
    target_platemat = g_i.platemat()
    target_platemat.setproperties(
        ("Identification", name),
        ("MaterialType", material_type),
        ("w", uw),
        ("EA1", ea),
        ("EI", ei),
        ("StructNu", v)
    )
    return target_platemat


def add_anchor_prop(g_i, anchor_name, alpha, spacing, area, e):
    """
    Adds or updates an anchor property in the PLAXIS server.

    Args:
        g_i: The PLAXIS server instance.
        anchor_name (str): The name of the anchor property.
        alpha (float): The angle to waling (degrees).
        spacing (float): The strut spacing (m).
        area (float): The strut area (cm^2).
        e (float): The Young's modulus of the anchor (MPa).

    Returns:
        target_anchormat: The anchor material object in the PLAXIS server.
    """
    l_s = spacing / math.sin(math.radians(alpha)) ** 2
    ea = (e * 10 ** 6) * (area / 100 ** 2) / 1000
    ea_ls = ea / l_s
    material_type = 1

    # Check if the anchor property already exists
    for anchormat in [mat for mat in g_i.Materials if mat.TypeName.value == 'AnchorMat']:
        if str(anchormat.Identification) == str(anchor_name):
            return anchormat

    # Create a new anchor property if not found
    target_anchormat = g_i.anchormat()
    target_anchormat.setproperties(
        "Identification", anchor_name,
        "MaterialType", material_type,
        "LSpacing", 1,
        "EA", ea_ls,
    )
    return target_anchormat


def add_eb_prop(g_i, eb_name, spacing, e, unit_weight, diameter, TSkinStartMax, TSkinEndMax):
    """
    Adds or updates an embedded beam property in the PLAXIS server.

    Args:
        g_i: The PLAXIS server instance.
        eb_name (str): The name of the embedded beam property.
        spacing (float): The spacing of the embedded beam (m).
        e (float): The Young's modulus of the embedded beam (MPa).
        unit_weight (float): The unit weight of the embedded beam (kN/m^3).
        diameter (float): The diameter of the embedded beam (m).
        TSkinStartMax (float): The maximum skin friction at the start of the embedded beam (kN/m).
        TSkinEndMax (float): The maximum skin friction at the end of the embedded beam (kN/m).

    Returns:
        target_ebmat: The embedded beam material object in the PLAXIS server.
    """
    # Create a new embedded beam material object
    target_ebmat = g_i.embeddedbeammat()

    # Check if the anchor property already exists
    for ebmat in [mat for mat in g_i.Materials if mat.TypeName.value == 'EmbeddedBeamMat']:
        if str(ebmat.Identification) == str(eb_name):
            return ebmat

    # Set properties for the embedded beam material
    target_ebmat.setproperties(
        "Identification", eb_name,
        "MaterialType", "Elastic",
        "Gamma", unit_weight,
        "E", e,
        "Diameter", diameter,
        "LSpacing", spacing,
        "TSkinStartMax", TSkinStartMax,
        "TSkinEndMax", TSkinEndMax,
        "FMax", 0
    )

    return target_ebmat
