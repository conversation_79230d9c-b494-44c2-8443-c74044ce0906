"""
Configuration Management Package

This package provides centralized configuration management functionality including:
- Configuration details display and management
- Configuration status tracking
- Integration with anchor and excavation modules
- Comprehensive configuration storage

The package maintains backward compatibility with the original module structure.
"""

# Import all classes and functions for backward compatibility
try:
    from .manager import (
        ConfigurationManager,
        initialize_comprehensive_config,
        ensure_comprehensive_config_exists,
        get_configuration_status,
        is_configuration_complete,
        reset_configuration
    )
except ImportError:
    pass

try:
    from .details import (
        show_configuration_details,
        update_anchor_geometry_config,
        update_anchor_parameters_config,
        update_excavation_level_config
    )
except ImportError:
    pass

__all__ = [
    # Manager
    'ConfigurationManager',
    'initialize_comprehensive_config',
    'ensure_comprehensive_config_exists', 
    'get_configuration_status',
    'is_configuration_complete',
    'reset_configuration',
    
    # Details
    'show_configuration_details',
    'update_anchor_geometry_config',
    'update_anchor_parameters_config',
    'update_excavation_level_config'
]
